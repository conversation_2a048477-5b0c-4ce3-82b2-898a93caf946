<?php

namespace Plugins\ClickUp\Tests;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Plugins\ClickUp\Services\ClickUpApiService;
use Plugins\ClickUp\Services\ClickUpCacheService;
use Plugins\ClickUp\Services\ClickUpMonitoringService;
use Plugins\ClickUp\Models\ClickUpApiToken;
use Plugins\ClickUp\Models\ClickUpList;

class CachePerformanceTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private ClickUpApiService $apiService;
    private ClickUpCacheService $cacheService;
    private ClickUpMonitoringService $monitoringService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test API token
        $token = ClickUpApiToken::create([
            'name' => 'Test Token',
            'encrypted_token' => encrypt('test_token_123'),
            'is_active' => true,
            'created_by' => 1,
            'metadata' => [
                'general_settings' => [
                    'cache_duration' => 1800
                ]
            ]
        ]);

        $this->apiService = new ClickUpApiService($token);
        $this->cacheService = $this->apiService->getCacheServiceInstance();
        $this->monitoringService = new ClickUpMonitoringService();
    }

    /**
     * Test cache service initialization
     */
    public function test_cache_service_initialization()
    {
        $this->assertInstanceOf(ClickUpCacheService::class, $this->cacheService);
    }

    /**
     * Test cache health check
     */
    public function test_cache_health_check()
    {
        $health = $this->cacheService->checkCacheHealth();
        
        $this->assertIsArray($health);
        $this->assertArrayHasKey('status', $health);
        $this->assertArrayHasKey('issues', $health);
        $this->assertContains($health['status'], ['healthy', 'warning', 'unhealthy']);
    }

    /**
     * Test cache statistics
     */
    public function test_cache_statistics()
    {
        $stats = $this->cacheService->getCacheStats();
        
        $this->assertIsArray($stats);
        $this->assertArrayHasKey('today', $stats);
        $this->assertArrayHasKey('yesterday', $stats);
        $this->assertArrayHasKey('cache_info', $stats);
        
        // Check structure of daily stats
        $this->assertArrayHasKey('list_hits', $stats['today']);
        $this->assertArrayHasKey('list_misses', $stats['today']);
        $this->assertArrayHasKey('hit_rate', $stats['today']);
    }

    /**
     * Test performance monitoring
     */
    public function test_performance_monitoring()
    {
        // Record some test metrics
        $this->monitoringService->recordPerformanceMetric('assignment', 0.150, true);
        $this->monitoringService->recordPerformanceMetric('assignment', 0.300, false);
        
        $metrics = $this->monitoringService->getPerformanceMetrics(1);
        
        $this->assertIsArray($metrics);
        $this->assertArrayHasKey('assignment', $metrics);
        $this->assertArrayHasKey('total_requests', $metrics['assignment']);
        $this->assertArrayHasKey('cache_hits', $metrics['assignment']);
        $this->assertArrayHasKey('cache_misses', $metrics['assignment']);
        $this->assertArrayHasKey('hit_rate', $metrics['assignment']);
    }

    /**
     * Test system health metrics
     */
    public function test_system_health_metrics()
    {
        $health = $this->monitoringService->getSystemHealthMetrics();
        
        $this->assertIsArray($health);
        $this->assertArrayHasKey('status', $health);
        $this->assertArrayHasKey('issues', $health);
        $this->assertArrayHasKey('metrics', $health);
        
        $this->assertContains($health['status'], ['healthy', 'warning', 'unhealthy']);
        $this->assertIsArray($health['issues']);
        $this->assertIsArray($health['metrics']);
    }

    /**
     * Test cache efficiency report
     */
    public function test_cache_efficiency_report()
    {
        $report = $this->monitoringService->getCacheEfficiencyReport();
        
        $this->assertIsArray($report);
        $this->assertArrayHasKey('summary', $report);
        $this->assertArrayHasKey('trends', $report);
        
        // Check summary structure
        $summary = $report['summary'];
        $this->assertArrayHasKey('total_cache_operations', $summary);
        $this->assertArrayHasKey('cache_hits', $summary);
        $this->assertArrayHasKey('cache_misses', $summary);
        $this->assertArrayHasKey('overall_hit_rate', $summary);
        $this->assertArrayHasKey('estimated_api_calls_saved', $summary);
        
        // Check trends structure
        $this->assertIsArray($report['trends']);
        if (!empty($report['trends'])) {
            $trend = $report['trends'][0];
            $this->assertArrayHasKey('date', $trend);
            $this->assertArrayHasKey('cache_operations', $trend);
            $this->assertArrayHasKey('hit_rate', $trend);
        }
    }

    /**
     * Test list caching functionality
     */
    public function test_list_caching()
    {
        // Create a test list in database
        $list = ClickUpList::create([
            'clickup_id' => 'test_list_123',
            'name' => 'Test List',
            'description' => 'Test Description',
            'space_id' => 'test_space_123',
            'team_id' => 'test_team_123',
            'status' => 'active',
            'is_private' => false,
            'is_archived' => false,
            'task_count' => 5,
            'last_synced_at' => now(),
            'sync_status' => 'success',
            'created_by' => 1
        ]);

        // Test caching all lists
        $result = $this->cacheService->cacheAllLists();
        
        $this->assertIsArray($result);
        $this->assertTrue($result['success']);
        $this->assertGreaterThan(0, $result['cached']);
        $this->assertEquals(0, $result['errors']);
    }

    /**
     * Test cache clearing functionality
     */
    public function test_cache_clearing()
    {
        // First cache some data
        $this->cacheService->cacheAllLists();
        
        // Then clear it
        $result = $this->cacheService->clearAllListCaches();
        
        $this->assertIsArray($result);
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('cleared', $result);
    }

    /**
     * Test cache warm-up functionality
     */
    public function test_cache_warmup()
    {
        // Create some test data
        ClickUpList::create([
            'clickup_id' => 'warmup_test_123',
            'name' => 'Warmup Test List',
            'description' => 'Test Description',
            'space_id' => 'test_space_123',
            'team_id' => 'test_team_123',
            'status' => 'active',
            'is_private' => false,
            'is_archived' => false,
            'task_count' => 3,
            'last_synced_at' => now(),
            'sync_status' => 'success',
            'created_by' => 1
        ]);

        $result = $this->cacheService->warmUpCache();
        
        $this->assertIsArray($result);
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('warmed', $result);
        $this->assertArrayHasKey('errors', $result);
    }

    /**
     * Performance comparison test
     */
    public function test_performance_comparison()
    {
        // Create test list
        $list = ClickUpList::create([
            'clickup_id' => 'perf_test_123',
            'name' => 'Performance Test List',
            'description' => 'Test Description',
            'space_id' => 'test_space_123',
            'team_id' => 'test_team_123',
            'status' => 'active',
            'is_private' => false,
            'is_archived' => false,
            'task_count' => 10,
            'last_synced_at' => now(),
            'sync_status' => 'success',
            'created_by' => 1
        ]);

        // Cache the list
        $this->cacheService->cacheAllLists();

        // Measure cached access time
        $startTime = microtime(true);
        $cachedResult = $this->cacheService->getList('perf_test_123');
        $cachedDuration = microtime(true) - $startTime;

        // Verify cached result
        $this->assertTrue($cachedResult['success']);
        $this->assertEquals('perf_test_123', $cachedResult['data']['id']);
        
        // Record the performance metric
        $this->monitoringService->recordAssignmentPerformance(
            'perf_test_123', 
            $cachedDuration, 
            true
        );

        // Cached access should be very fast (under 10ms typically)
        $this->assertLessThan(0.01, $cachedDuration, 'Cached access should be under 10ms');
        
        echo "\nPerformance Test Results:\n";
        echo "Cached access time: " . round($cachedDuration * 1000, 2) . "ms\n";
    }
}
