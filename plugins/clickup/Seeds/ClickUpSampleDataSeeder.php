<?php

namespace Plugins\ClickUp\Seeds;

use Illuminate\Database\Seeder;
use Plugins\ClickUp\Models\ClickUpProductManager;
use Plugins\ClickUp\Models\ClickUpList;
use Plugins\ClickUp\Models\ClickUpTask;

class ClickUpSampleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Only seed if no data exists
        if (ClickUpProductManager::count() > 0) {
            $this->command->info('ClickUp sample data already exists. Skipping seeder.');
            return;
        }

        // Create sample product managers
        $managers = [
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'role' => 'Senior Product Manager',
                'department' => 'Product',
                'hire_date' => now()->subYears(2),
                'timezone' => 'America/New_York',
                'is_active' => true,
                'created_by' => 1
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'role' => 'Product Manager',
                'department' => 'Product',
                'hire_date' => now()->subYear(),
                'timezone' => 'America/Los_Angeles',
                'is_active' => true,
                'created_by' => 1
            ],
            [
                'name' => 'Mike Chen',
                'email' => '<EMAIL>',
                'role' => 'Associate Product Manager',
                'department' => 'Product',
                'hire_date' => now()->subMonths(6),
                'timezone' => 'America/Chicago',
                'is_active' => true,
                'created_by' => 1
            ]
        ];

        $createdManagers = [];
        foreach ($managers as $managerData) {
            $manager = ClickUpProductManager::create($managerData);
            $createdManagers[] = $manager;
        }

        // Create sample lists
        $lists = [
            [
                'clickup_id' => 'list_001',
                'name' => 'Mobile App Development',
                'description' => 'Development tasks for the mobile application',
                'space_id' => 'space_001',
                'team_id' => 'team_001',
                'product_manager_id' => $createdManagers[0]->id,
                'status' => 'active',
                'priority' => 'high',
                'color' => '#3b82f6',
                'is_private' => false,
                'is_archived' => false,
                'task_count' => 0,
                'created_by' => 1
            ],
            [
                'clickup_id' => 'list_002',
                'name' => 'Web Platform Features',
                'description' => 'New features for the web platform',
                'space_id' => 'space_001',
                'team_id' => 'team_001',
                'product_manager_id' => $createdManagers[1]->id,
                'status' => 'active',
                'priority' => 'normal',
                'color' => '#10b981',
                'is_private' => false,
                'is_archived' => false,
                'task_count' => 0,
                'created_by' => 1
            ],
            [
                'clickup_id' => 'list_003',
                'name' => 'API Integration',
                'description' => 'Third-party API integrations and improvements',
                'space_id' => 'space_002',
                'team_id' => 'team_001',
                'product_manager_id' => $createdManagers[2]->id,
                'status' => 'active',
                'priority' => 'normal',
                'color' => '#8b5cf6',
                'is_private' => false,
                'is_archived' => false,
                'task_count' => 0,
                'created_by' => 1
            ]
        ];

        $createdLists = [];
        foreach ($lists as $listData) {
            $list = ClickUpList::create($listData);
            $createdLists[] = $list;
        }

        // Create sample tasks
        $tasks = [
            // Mobile App Development tasks
            [
                'clickup_id' => 'task_001',
                'list_id' => $createdLists[0]->id,
                'name' => 'Implement user authentication',
                'description' => 'Add login and registration functionality to the mobile app',
                'status' => 'in progress',
                'priority' => 'high',
                'assignees' => [['username' => 'dev1', 'email' => '<EMAIL>']],
                'due_date' => now()->addDays(7),
                'date_created' => now()->subDays(3),
                'date_updated' => now()->subHours(2),
                'url' => 'https://app.clickup.com/t/task_001'
            ],
            [
                'clickup_id' => 'task_002',
                'list_id' => $createdLists[0]->id,
                'name' => 'Design user profile screen',
                'description' => 'Create wireframes and mockups for user profile',
                'status' => 'complete',
                'priority' => 'normal',
                'assignees' => [['username' => 'designer1', 'email' => '<EMAIL>']],
                'due_date' => now()->subDays(1),
                'date_created' => now()->subDays(5),
                'date_updated' => now()->subDays(1),
                'date_closed' => now()->subDays(1),
                'url' => 'https://app.clickup.com/t/task_002'
            ],
            // Web Platform Features tasks
            [
                'clickup_id' => 'task_003',
                'list_id' => $createdLists[1]->id,
                'name' => 'Add dark mode support',
                'description' => 'Implement dark mode theme for the web application',
                'status' => 'open',
                'priority' => 'normal',
                'assignees' => [['username' => 'frontend1', 'email' => '<EMAIL>']],
                'due_date' => now()->addDays(14),
                'date_created' => now()->subDays(1),
                'date_updated' => now()->subHours(6),
                'url' => 'https://app.clickup.com/t/task_003'
            ],
            [
                'clickup_id' => 'task_004',
                'list_id' => $createdLists[1]->id,
                'name' => 'Optimize database queries',
                'description' => 'Improve performance of slow database queries',
                'status' => 'in progress',
                'priority' => 'high',
                'assignees' => [['username' => 'backend1', 'email' => '<EMAIL>']],
                'due_date' => now()->addDays(5),
                'date_created' => now()->subDays(2),
                'date_updated' => now()->subHours(1),
                'url' => 'https://app.clickup.com/t/task_004'
            ],
            // API Integration tasks
            [
                'clickup_id' => 'task_005',
                'list_id' => $createdLists[2]->id,
                'name' => 'Integrate payment gateway',
                'description' => 'Add Stripe payment processing to the platform',
                'status' => 'open',
                'priority' => 'urgent',
                'assignees' => [['username' => 'backend2', 'email' => '<EMAIL>']],
                'due_date' => now()->addDays(10),
                'date_created' => now(),
                'date_updated' => now(),
                'url' => 'https://app.clickup.com/t/task_005'
            ]
        ];

        foreach ($tasks as $taskData) {
            ClickUpTask::create($taskData);
        }

        // Update list task counts
        foreach ($createdLists as $list) {
            $taskCount = ClickUpTask::where('list_id', $list->id)->count();
            $list->update(['task_count' => $taskCount]);
        }

        $this->command->info('ClickUp sample data seeded successfully.');
        $this->command->info('Created:');
        $this->command->info('- ' . count($createdManagers) . ' product managers');
        $this->command->info('- ' . count($createdLists) . ' lists');
        $this->command->info('- ' . count($tasks) . ' tasks');
    }
}
