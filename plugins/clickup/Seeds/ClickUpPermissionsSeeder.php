<?php

namespace Plugins\ClickUp\Seeds;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ClickUpPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [
            [
                'name' => 'manage_clickup_integration',
                'display_name' => 'Manage ClickUp Integration',
                'description' => 'Full access to manage ClickUp integration settings and configuration',
                'plugin' => 'clickup',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'view_clickup_data',
                'display_name' => 'View ClickUp Data',
                'description' => 'View ClickUp lists, tasks, and basic information',
                'plugin' => 'clickup',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'manage_clickup_lists',
                'display_name' => 'Manage ClickUp Lists',
                'description' => 'Create, edit, and manage ClickUp lists and their assignments',
                'plugin' => 'clickup',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'view_clickup_lists',
                'display_name' => 'View ClickUp Lists',
                'description' => 'View ClickUp lists and their details',
                'plugin' => 'clickup',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'manage_clickup_tasks',
                'display_name' => 'Manage ClickUp Tasks',
                'description' => 'Manage ClickUp tasks and their properties',
                'plugin' => 'clickup',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'view_clickup_tasks',
                'display_name' => 'View ClickUp Tasks',
                'description' => 'View ClickUp tasks and their details',
                'plugin' => 'clickup',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'manage_clickup_product_managers',
                'display_name' => 'Manage Product Managers',
                'description' => 'Create, edit, and manage product manager profiles',
                'plugin' => 'clickup',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'view_clickup_product_managers',
                'display_name' => 'View Product Managers',
                'description' => 'View product manager profiles and their assignments',
                'plugin' => 'clickup',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'view_clickup_reports',
                'display_name' => 'View ClickUp Reports',
                'description' => 'Access ClickUp performance reports and analytics',
                'plugin' => 'clickup',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'manage_clickup_settings',
                'display_name' => 'Manage ClickUp Settings',
                'description' => 'Configure ClickUp API settings and integration options',
                'plugin' => 'clickup',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'sync_clickup_data',
                'display_name' => 'Sync ClickUp Data',
                'description' => 'Trigger data synchronization from ClickUp API',
                'plugin' => 'clickup',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        // Insert permissions if they don't exist
        foreach ($permissions as $permission) {
            $exists = DB::table('permissions')
                ->where('name', $permission['name'])
                ->exists();

            if (!$exists) {
                DB::table('permissions')->insert($permission);
            }
        }

        // Assign all ClickUp permissions to admin role (role_id = 1)
        $adminRoleId = DB::table('roles')->where('name', 'admin')->value('id');
        
        if ($adminRoleId) {
            $clickupPermissions = DB::table('permissions')
                ->whereIn('name', array_column($permissions, 'name'))
                ->pluck('id');

            foreach ($clickupPermissions as $permissionId) {
                $exists = DB::table('permission_role')
                    ->where('role_id', $adminRoleId)
                    ->where('permission_id', $permissionId)
                    ->exists();

                if (!$exists) {
                    DB::table('permission_role')->insert([
                        'role_id' => $adminRoleId,
                        'permission_id' => $permissionId,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
        }

        $this->command->info('ClickUp permissions seeded successfully.');
    }
}
