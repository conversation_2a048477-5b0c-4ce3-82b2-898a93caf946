<?php

namespace Plugins\ClickUp\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Plugins\ClickUp\Models\ClickUpList;
use Plugins\ClickUp\Models\ClickUpTask;
use Plugins\ClickUp\Models\ClickUpProductManager;

class ReportController extends Controller
{
    /**
     * Display reports dashboard
     */
    public function index(Request $request): View
    {
        // Check permissions
        if (!auth()->user()->hasPermission('view_clickup_reports')) {
            abort(403, 'You do not have permission to view ClickUp reports.');
        }

        $overviewData = $this->getOverviewData();
        $productManagers = ClickUpProductManager::active()->orderBy('name')->get();

        return view('plugins.clickup::reports.index', compact('overviewData', 'productManagers'));
    }

    /**
     * Performance report
     */
    public function performance(Request $request): View
    {
        // Check permissions
        if (!auth()->user()->hasPermission('view_clickup_reports')) {
            abort(403, 'You do not have permission to view ClickUp reports.');
        }

        $performanceData = $this->getPerformanceData($request);
        $productManagers = ClickUpProductManager::active()->orderBy('name')->get();

        return view('plugins.clickup::reports.performance', compact('performanceData', 'productManagers'));
    }

    /**
     * Productivity report
     */
    public function productivity(Request $request): View
    {
        // Check permissions
        if (!auth()->user()->hasPermission('view_clickup_reports')) {
            abort(403, 'You do not have permission to view ClickUp reports.');
        }

        $productivityData = $this->getProductivityData($request);
        $productManagers = ClickUpProductManager::active()->orderBy('name')->get();

        return view('plugins.clickup::reports.productivity', compact('productivityData', 'productManagers'));
    }

    /**
     * Completion rates report
     */
    public function completionRates(Request $request): View
    {
        // Check permissions
        if (!auth()->user()->hasPermission('view_clickup_reports')) {
            abort(403, 'You do not have permission to view ClickUp reports.');
        }

        $completionData = $this->getCompletionRatesData($request);
        $productManagers = ClickUpProductManager::active()->orderBy('name')->get();

        return view('plugins.clickup::reports.completion-rates', compact('completionData', 'productManagers'));
    }

    /**
     * Time tracking report
     */
    public function timeTracking(Request $request): View
    {
        // Check permissions
        if (!auth()->user()->hasPermission('view_clickup_reports')) {
            abort(403, 'You do not have permission to view ClickUp reports.');
        }

        $timeData = $this->getTimeTrackingData($request);
        $productManagers = ClickUpProductManager::active()->orderBy('name')->get();

        return view('plugins.clickup::reports.time-tracking', compact('timeData', 'productManagers'));
    }

    /**
     * Bugs and features report
     */
    public function bugsFeatures(Request $request): View
    {
        // Check permissions
        if (!auth()->user()->hasPermission('view_clickup_reports')) {
            abort(403, 'You do not have permission to view ClickUp reports.');
        }

        $bugsAndFeaturesData = $this->getBugsAndFeaturesData($request);
        $productManagers = ClickUpProductManager::active()->orderBy('name')->get();

        return view('plugins.clickup::reports.bugs-features', compact('bugsAndFeaturesData', 'productManagers'));
    }

    /**
     * API endpoint for report data
     */
    public function apiData(Request $request)
    {
        // Check permissions
        if (!auth()->user()->hasPermission('view_clickup_reports')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $type = $request->get('type');

        switch ($type) {
            case 'overview':
                return response()->json($this->getOverviewData());
            case 'performance':
                return response()->json($this->getPerformanceData($request));
            case 'productivity':
                return response()->json($this->getProductivityData($request));
            case 'completion':
                return response()->json($this->getCompletionRatesData($request));
            case 'time':
                return response()->json($this->getTimeTrackingData($request));
            case 'bugs_features':
                return response()->json($this->getBugsAndFeaturesData($request));
            default:
                return response()->json(['error' => 'Invalid report type'], 400);
        }
    }

    /**
     * Get overview data
     */
    private function getOverviewData(): array
    {
        $totalTasks = ClickUpTask::count();
        $completedTasks = ClickUpTask::completed()->count();
        $activeTasks = ClickUpTask::active()->count();
        $overdueTasks = ClickUpTask::overdue()->count();

        $completionRate = $totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100, 1) : 0;

        return [
            'total_tasks' => $totalTasks,
            'completed_tasks' => $completedTasks,
            'active_tasks' => $activeTasks,
            'overdue_tasks' => $overdueTasks,
            'completion_rate' => $completionRate,
            'total_lists' => ClickUpList::count(),
            'active_lists' => ClickUpList::active()->count(),
            'total_managers' => ClickUpProductManager::active()->count()
        ];
    }

    /**
     * Get performance data
     */
    private function getPerformanceData(Request $request): array
    {
        $managerId = $request->get('manager_id');
        $dateFrom = $request->get('date_from', now()->subMonth()->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));

        $query = ClickUpTask::whereBetween('date_created', [$dateFrom, $dateTo]);

        if ($managerId) {
            $query->whereHas('list', function ($q) use ($managerId) {
                $q->where('product_manager_id', $managerId);
            });
        }

        $tasks = $query->get();
        $completedTasks = $tasks->where('is_completed', true);

        // Group by week
        $weeklyData = [];
        $currentDate = \Carbon\Carbon::parse($dateFrom);
        $endDate = \Carbon\Carbon::parse($dateTo);

        while ($currentDate <= $endDate) {
            $weekStart = $currentDate->copy()->startOfWeek();
            $weekEnd = $currentDate->copy()->endOfWeek();
            
            $weekTasks = $tasks->whereBetween('date_created', [$weekStart, $weekEnd]);
            $weekCompleted = $completedTasks->whereBetween('date_closed', [$weekStart, $weekEnd]);

            $weeklyData[] = [
                'week' => $weekStart->format('M d') . ' - ' . $weekEnd->format('M d'),
                'created' => $weekTasks->count(),
                'completed' => $weekCompleted->count(),
                'completion_rate' => $weekTasks->count() > 0 ? 
                    round(($weekCompleted->count() / $weekTasks->count()) * 100, 1) : 0
            ];

            $currentDate->addWeek();
        }

        return [
            'weekly_data' => $weeklyData,
            'total_created' => $tasks->count(),
            'total_completed' => $completedTasks->count(),
            'overall_completion_rate' => $tasks->count() > 0 ? 
                round(($completedTasks->count() / $tasks->count()) * 100, 1) : 0
        ];
    }

    /**
     * Get productivity data
     */
    private function getProductivityData(Request $request): array
    {
        $managers = ClickUpProductManager::active()->with(['tasks', 'clickupLists'])->get();

        $productivityData = $managers->map(function ($manager) {
            $metrics = $manager->getPerformanceMetrics();
            return [
                'manager' => $manager->name,
                'total_tasks' => $metrics['total_tasks'],
                'completed_tasks' => $metrics['completed_tasks'],
                'completion_rate' => $metrics['completion_rate'],
                'productivity_score' => $metrics['productivity_score'],
                'average_completion_time' => $metrics['average_completion_time'],
                'overdue_tasks' => $metrics['overdue_tasks']
            ];
        })->sortByDesc('productivity_score')->values();

        return [
            'managers' => $productivityData,
            'top_performer' => $productivityData->first(),
            'average_productivity' => $productivityData->avg('productivity_score')
        ];
    }

    /**
     * Get completion rates data
     */
    private function getCompletionRatesData(Request $request): array
    {
        $lists = ClickUpList::active()->with(['productManager', 'tasks'])->get();

        $completionData = $lists->map(function ($list) {
            $metrics = $list->getPerformanceMetrics();
            return [
                'list_name' => $list->name,
                'manager' => $list->productManager ? $list->productManager->name : 'Unassigned',
                'total_tasks' => $metrics['total_tasks'],
                'completed_tasks' => $metrics['completed_tasks'],
                'completion_rate' => $metrics['completion_rate'],
                'overdue_tasks' => $metrics['overdue_tasks']
            ];
        })->sortByDesc('completion_rate')->values();

        return [
            'lists' => $completionData,
            'average_completion_rate' => $completionData->avg('completion_rate'),
            'best_performing_list' => $completionData->first()
        ];
    }

    /**
     * Get time tracking data
     */
    private function getTimeTrackingData(Request $request): array
    {
        $completedTasks = ClickUpTask::completed()
            ->whereNotNull('date_created')
            ->whereNotNull('date_closed')
            ->get();

        $timeData = [];
        $totalHours = 0;

        foreach ($completedTasks as $task) {
            $created = \Carbon\Carbon::parse($task->date_created);
            $closed = \Carbon\Carbon::parse($task->date_closed);
            $hours = $created->diffInHours($closed);
            $totalHours += $hours;

            $timeData[] = [
                'task_name' => $task->name,
                'list_name' => $task->list->name,
                'manager' => $task->list->productManager ? $task->list->productManager->name : 'Unassigned',
                'completion_time' => $hours,
                'priority' => $task->priority
            ];
        }

        $averageTime = $completedTasks->count() > 0 ? 
            round($totalHours / $completedTasks->count(), 1) : 0;

        return [
            'tasks' => collect($timeData)->sortByDesc('completion_time')->values(),
            'average_completion_time' => $averageTime,
            'total_time_spent' => $totalHours,
            'fastest_completion' => collect($timeData)->min('completion_time'),
            'slowest_completion' => collect($timeData)->max('completion_time')
        ];
    }

    /**
     * Get bugs and features data
     */
    private function getBugsAndFeaturesData(Request $request): array
    {
        $managerId = $request->get('manager_id');
        $dateFrom = $request->get('date_from', now()->subMonth()->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));

        // Get all product managers with their lists
        $managersQuery = ClickUpProductManager::active()->with(['clickupLists', 'tasks']);

        if ($managerId) {
            $managersQuery->where('id', $managerId);
        }

        $managers = $managersQuery->get();

        $reportData = [];

        foreach ($managers as $manager) {
            // Get tasks within date range
            $tasks = $manager->tasks()
                ->whereBetween('click_up_tasks.date_created', [$dateFrom, $dateTo])
                ->get();

            // Categorize tasks as bugs or features based on tags, priority, or keywords
            $bugs = $this->categorizeBugs($tasks);
            $features = $this->categorizeFeatures($tasks);

            $reportData[] = [
                'manager' => [
                    'id' => $manager->id,
                    'name' => $manager->name,
                    'email' => $manager->email,
                    'department' => $manager->department,
                    'role' => $manager->role
                ],
                'lists_count' => $manager->clickupLists->count(),
                'bugs' => [
                    'total_reported' => $bugs['reported']->count(),
                    'total_solved' => $bugs['solved']->count(),
                    'pending' => $bugs['pending']->count(),
                    'in_progress' => $bugs['in_progress']->count(),
                    'critical' => $bugs['critical']->count(),
                    'high_priority' => $bugs['high_priority']->count(),
                    'average_resolution_time' => $this->calculateAverageResolutionTime($bugs['solved']),
                    'recent_bugs' => $bugs['reported']->take(5)->map(function ($task) {
                        return [
                            'id' => $task->id,
                            'name' => $task->name,
                            'status' => $task->status,
                            'priority' => $task->priority,
                            'created_at' => $task->date_created,
                            'closed_at' => $task->date_closed,
                            'url' => $task->url
                        ];
                    })
                ],
                'features' => [
                    'total_requested' => $features['requested']->count(),
                    'total_completed' => $features['completed']->count(),
                    'in_development' => $features['in_development']->count(),
                    'pending_approval' => $features['pending']->count(),
                    'average_development_time' => $this->calculateAverageResolutionTime($features['completed']),
                    'recent_features' => $features['requested']->take(5)->map(function ($task) {
                        return [
                            'id' => $task->id,
                            'name' => $task->name,
                            'status' => $task->status,
                            'priority' => $task->priority,
                            'created_at' => $task->date_created,
                            'closed_at' => $task->date_closed,
                            'url' => $task->url
                        ];
                    })
                ],
                'summary' => [
                    'total_items' => $tasks->count(),
                    'bugs_percentage' => $tasks->count() > 0 ? round(($bugs['reported']->count() / $tasks->count()) * 100, 1) : 0,
                    'features_percentage' => $tasks->count() > 0 ? round(($features['requested']->count() / $tasks->count()) * 100, 1) : 0,
                    'resolution_rate' => $bugs['reported']->count() > 0 ? round(($bugs['solved']->count() / $bugs['reported']->count()) * 100, 1) : 0,
                    'completion_rate' => $features['requested']->count() > 0 ? round(($features['completed']->count() / $features['requested']->count()) * 100, 1) : 0
                ]
            ];
        }

        // Calculate overall statistics
        $overallStats = [
            'total_managers' => $managers->count(),
            'total_bugs_reported' => collect($reportData)->sum('bugs.total_reported'),
            'total_bugs_solved' => collect($reportData)->sum('bugs.total_solved'),
            'total_features_requested' => collect($reportData)->sum('features.total_requested'),
            'total_features_completed' => collect($reportData)->sum('features.total_completed'),
            'overall_bug_resolution_rate' => 0,
            'overall_feature_completion_rate' => 0
        ];

        if ($overallStats['total_bugs_reported'] > 0) {
            $overallStats['overall_bug_resolution_rate'] = round(($overallStats['total_bugs_solved'] / $overallStats['total_bugs_reported']) * 100, 1);
        }

        if ($overallStats['total_features_requested'] > 0) {
            $overallStats['overall_feature_completion_rate'] = round(($overallStats['total_features_completed'] / $overallStats['total_features_requested']) * 100, 1);
        }

        return [
            'managers' => $reportData,
            'overall_stats' => $overallStats,
            'date_range' => [
                'from' => $dateFrom,
                'to' => $dateTo
            ]
        ];
    }

    /**
     * Categorize tasks as bugs based on tags, keywords, or priority
     */
    private function categorizeBugs($tasks): array
    {
        $bugKeywords = ['bug', 'error', 'issue', 'fix', 'broken', 'crash', 'problem', 'defect'];

        $allBugs = $tasks->filter(function ($task) use ($bugKeywords) {
            // Check tags
            if ($task->tags) {
                foreach ($task->tags as $tag) {
                    $tagName = is_array($tag) ? ($tag['name'] ?? '') : $tag;
                    if (in_array(strtolower($tagName), ['bug', 'issue', 'error', 'fix'])) {
                        return true;
                    }
                }
            }

            // Check task name for bug keywords
            $taskName = strtolower($task->name);
            foreach ($bugKeywords as $keyword) {
                if (strpos($taskName, $keyword) !== false) {
                    return true;
                }
            }

            return false;
        });

        return [
            'reported' => $allBugs,
            'solved' => $allBugs->whereIn('status', ['complete', 'closed']),
            'pending' => $allBugs->where('status', 'open'),
            'in_progress' => $allBugs->where('status', 'in progress'),
            'critical' => $allBugs->where('priority', 'urgent'),
            'high_priority' => $allBugs->where('priority', 'high')
        ];
    }

    /**
     * Categorize tasks as features based on tags, keywords, or priority
     */
    private function categorizeFeatures($tasks): array
    {
        $featureKeywords = ['feature', 'enhancement', 'improvement', 'new', 'add', 'implement', 'develop'];

        $allFeatures = $tasks->filter(function ($task) use ($featureKeywords) {
            // Check tags
            if ($task->tags) {
                foreach ($task->tags as $tag) {
                    $tagName = is_array($tag) ? ($tag['name'] ?? '') : $tag;
                    if (in_array(strtolower($tagName), ['feature', 'enhancement', 'improvement', 'new'])) {
                        return true;
                    }
                }
            }

            // Check task name for feature keywords
            $taskName = strtolower($task->name);
            foreach ($featureKeywords as $keyword) {
                if (strpos($taskName, $keyword) !== false) {
                    return true;
                }
            }

            return false;
        });

        return [
            'requested' => $allFeatures,
            'completed' => $allFeatures->whereIn('status', ['complete', 'closed']),
            'in_development' => $allFeatures->where('status', 'in progress'),
            'pending' => $allFeatures->where('status', 'open')
        ];
    }

    /**
     * Calculate average resolution time in hours
     */
    private function calculateAverageResolutionTime($tasks): ?float
    {
        $tasksWithTimes = $tasks->filter(function ($task) {
            return $task->date_created && $task->date_closed;
        });

        if ($tasksWithTimes->isEmpty()) {
            return null;
        }

        $totalHours = 0;
        foreach ($tasksWithTimes as $task) {
            $created = \Carbon\Carbon::parse($task->date_created);
            $closed = \Carbon\Carbon::parse($task->date_closed);
            $totalHours += $created->diffInHours($closed);
        }

        return round($totalHours / $tasksWithTimes->count(), 1);
    }
}
