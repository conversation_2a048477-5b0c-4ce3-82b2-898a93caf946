<?php

namespace Plugins\ClickUp\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Plugins\ClickUp\Models\ClickUpTask;
use Plugins\ClickUp\Models\ClickUpList;

class TaskController extends Controller
{
    /**
     * Display tasks
     */
    public function index(Request $request): View
    {
        // Check permissions
        if (!auth()->user()->hasPermission('view_clickup_tasks')) {
            abort(403, 'You do not have permission to view ClickUp tasks.');
        }

        $query = ClickUpTask::with(['list', 'list.productManager']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('clickup_id', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->filled('list_id')) {
            $query->where('list_id', $request->list_id);
        }

        if ($request->filled('overdue')) {
            $query->overdue();
        }

        $tasks = $query->orderBy('date_created', 'desc')->paginate(20);
        $lists = ClickUpList::active()->orderBy('name')->get();

        return view('plugins.clickup::tasks.index', compact('tasks', 'lists'));
    }

    /**
     * Show a specific task
     */
    public function show(ClickUpTask $task): View
    {
        // Check permissions
        if (!auth()->user()->hasPermission('view_clickup_tasks')) {
            abort(403, 'You do not have permission to view ClickUp tasks.');
        }

        $task->load(['list', 'list.productManager', 'parentTask', 'subtasks']);

        return view('plugins.clickup::tasks.show', compact('task'));
    }

    /**
     * Sync a specific task
     */
    public function sync(Request $request, ClickUpTask $task)
    {
        // Check permissions
        if (!auth()->user()->hasPermission('sync_clickup_data')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            // This would typically call the sync service to update this specific task
            // For now, we'll just mark it as needing sync
            $task->update([
                'sync_status' => 'pending',
                'last_synced_at' => null
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Task marked for sync successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to sync task: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * API endpoint for tasks data
     */
    public function apiIndex(Request $request)
    {
        // Check permissions
        if (!auth()->user()->hasPermission('view_clickup_tasks')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $query = ClickUpTask::with(['list', 'list.productManager']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if ($request->filled('list_id')) {
            $query->where('list_id', $request->list_id);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        $tasks = $query->orderBy('date_created', 'desc')
                      ->limit($request->get('limit', 50))
                      ->get();

        return response()->json([
            'success' => true,
            'data' => $tasks->map(function ($task) {
                return [
                    'id' => $task->id,
                    'clickup_id' => $task->clickup_id,
                    'name' => $task->name,
                    'description' => $task->description,
                    'status' => $task->status,
                    'priority' => $task->priority,
                    'due_date' => $task->due_date,
                    'is_overdue' => $task->is_overdue,
                    'is_completed' => $task->is_completed,
                    'progress_percentage' => $task->progress_percentage,
                    'assignee_names' => $task->assignee_names,
                    'list' => [
                        'id' => $task->list->id,
                        'name' => $task->list->name,
                        'product_manager' => $task->list->productManager ? [
                            'id' => $task->list->productManager->id,
                            'name' => $task->list->productManager->name
                        ] : null
                    ],
                    'url' => $task->url,
                    'last_synced_at' => $task->last_synced_at
                ];
            })
        ]);
    }
}
