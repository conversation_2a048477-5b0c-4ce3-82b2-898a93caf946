<?php

namespace Plugins\ClickUp\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Plugins\ClickUp\Services\ClickUpSyncService;

class SyncController extends Controller
{
    private ClickUpSyncService $syncService;

    public function __construct(ClickUpSyncService $syncService)
    {
        $this->syncService = $syncService;
    }

    /**
     * Sync lists from ClickUp
     */
    public function syncLists(Request $request)
    {
        // Check permissions
        if (!auth()->user()->hasPermission('sync_clickup_data')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            $result = $this->syncService->syncLists();
            
            Log::info('ClickUp lists sync initiated', [
                'user_id' => auth()->id(),
                'result' => $result
            ]);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('ClickUp lists sync failed', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Sync failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Sync tasks from ClickUp
     */
    public function syncTasks(Request $request)
    {
        // Check permissions
        if (!auth()->user()->hasPermission('sync_clickup_data')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            $result = $this->syncService->syncTasks();
            
            Log::info('ClickUp tasks sync initiated', [
                'user_id' => auth()->id(),
                'result' => $result
            ]);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('ClickUp tasks sync failed', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Sync failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Sync all data from ClickUp
     */
    public function syncAll(Request $request)
    {
        // Check permissions
        if (!auth()->user()->hasPermission('sync_clickup_data')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            $result = $this->syncService->syncAll();
            
            Log::info('ClickUp full sync initiated', [
                'user_id' => auth()->id(),
                'result' => $result
            ]);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('ClickUp full sync failed', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Sync failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get sync status
     */
    public function status(Request $request)
    {
        // Check permissions
        if (!auth()->user()->hasPermission('view_clickup_data')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            $status = $this->syncService->getSyncStatus();
            
            return response()->json([
                'success' => true,
                'data' => $status
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get sync status', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get sync status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Schedule automatic sync
     */
    public function scheduleSync(Request $request)
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $validator = \Validator::make($request->all(), [
            'interval' => 'required|integer|min:300|max:86400', // 5 minutes to 24 hours
            'enabled' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid parameters',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Store sync schedule in cache or database
            // For now, we'll use cache
            $scheduleData = [
                'interval' => $request->interval,
                'enabled' => $request->boolean('enabled', true),
                'next_sync' => now()->addSeconds($request->interval),
                'created_by' => auth()->id(),
                'created_at' => now()->toISOString()
            ];

            \Cache::put('clickup_sync_schedule', $scheduleData, now()->addDays(30));

            Log::info('ClickUp sync schedule updated', [
                'user_id' => auth()->id(),
                'schedule' => $scheduleData
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Sync schedule updated successfully',
                'data' => $scheduleData
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to schedule sync', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to schedule sync: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get sync schedule
     */
    public function getSchedule(Request $request)
    {
        // Check permissions
        if (!auth()->user()->hasPermission('view_clickup_data')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            $schedule = \Cache::get('clickup_sync_schedule');

            return response()->json([
                'success' => true,
                'data' => $schedule
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get sync schedule: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Cancel scheduled sync
     */
    public function cancelSchedule(Request $request)
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            \Cache::forget('clickup_sync_schedule');

            Log::info('ClickUp sync schedule cancelled', [
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Sync schedule cancelled successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel sync schedule: ' . $e->getMessage()
            ], 500);
        }
    }
}
