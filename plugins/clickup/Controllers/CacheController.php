<?php

namespace Plugins\ClickUp\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Plugins\ClickUp\Services\ClickUpApiService;
use Plugins\ClickUp\Services\ClickUpCacheService;
use Plugins\ClickUp\Services\ClickUpSyncService;
use Plugins\ClickUp\Services\ClickUpMonitoringService;

class CacheController extends Controller
{
    private ClickUpApiService $apiService;
    private ClickUpSyncService $syncService;

    public function __construct(ClickUpApiService $apiService, ClickUpSyncService $syncService)
    {
        $this->apiService = $apiService;
        $this->syncService = $syncService;
    }

    /**
     * Get cache status and health
     */
    public function status(Request $request)
    {
        // Check permissions
        if (!auth()->user()->hasPermission('view_clickup_data')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            $cacheService = $this->apiService->getCacheServiceInstance();
            $health = $cacheService->checkCacheHealth();
            
            return response()->json([
                'success' => true,
                'health' => $health
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get cache status', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get cache status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get cache statistics
     */
    public function stats(Request $request)
    {
        // Check permissions
        if (!auth()->user()->hasPermission('view_clickup_data')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            $stats = $this->apiService->getCacheStats();
            
            return response()->json([
                'success' => true,
                'stats' => $stats
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get cache stats', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get cache statistics: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Refresh all caches
     */
    public function refresh(Request $request)
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            $cacheService = $this->apiService->getCacheServiceInstance();
            $result = $cacheService->refreshAllListCaches();
            
            Log::info('ClickUp cache refresh initiated', [
                'user_id' => auth()->id(),
                'result' => $result
            ]);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('ClickUp cache refresh failed', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Cache refresh failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Warm up cache
     */
    public function warmup(Request $request)
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            $result = $this->apiService->warmUpCache();
            
            Log::info('ClickUp cache warmup initiated', [
                'user_id' => auth()->id(),
                'result' => $result
            ]);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('ClickUp cache warmup failed', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Cache warmup failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear all caches
     */
    public function clear(Request $request)
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            $result = $this->apiService->clearAllClickUpCaches();
            
            Log::info('ClickUp cache clear initiated', [
                'user_id' => auth()->id(),
                'result' => $result
            ]);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('ClickUp cache clear failed', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Cache clear failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Refresh cache for specific list
     */
    public function refreshList(Request $request, string $listId)
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            $result = $this->apiService->refreshListCache($listId);
            
            Log::info('ClickUp list cache refresh initiated', [
                'user_id' => auth()->id(),
                'list_id' => $listId,
                'result' => $result
            ]);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('ClickUp list cache refresh failed', [
                'user_id' => auth()->id(),
                'list_id' => $listId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'List cache refresh failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get cache metrics for monitoring
     */
    public function metrics(Request $request)
    {
        // Check permissions
        if (!auth()->user()->hasPermission('view_clickup_data')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            $cacheService = $this->apiService->getCacheServiceInstance();
            $stats = $cacheService->getCacheStats();
            $health = $cacheService->checkCacheHealth();
            
            // Format for monitoring systems
            $metrics = [
                'cache_hit_rate_today' => $stats['today']['hit_rate'] ?? 0,
                'cache_requests_today' => $stats['today']['total_requests'] ?? 0,
                'cache_hit_rate_yesterday' => $stats['yesterday']['hit_rate'] ?? 0,
                'cache_requests_yesterday' => $stats['yesterday']['total_requests'] ?? 0,
                'cache_ttl_seconds' => $stats['cache_info']['ttl'] ?? 0,
                'active_lists_count' => $stats['cache_info']['active_lists_in_db'] ?? 0,
                'total_lists_count' => $stats['cache_info']['total_lists_in_db'] ?? 0,
                'cache_health_status' => $health['status'] ?? 'unknown',
                'cache_issues_count' => count($health['issues'] ?? []),
                'timestamp' => now()->toISOString()
            ];
            
            return response()->json([
                'success' => true,
                'metrics' => $metrics
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get cache metrics', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get cache metrics: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get performance monitoring data
     */
    public function performance(Request $request)
    {
        // Check permissions
        if (!auth()->user()->hasPermission('view_clickup_data')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            $monitoringService = new ClickUpMonitoringService();
            $days = $request->get('days', 7);

            $performance = $monitoringService->getPerformanceMetrics($days);
            $health = $monitoringService->getSystemHealthMetrics();
            $efficiency = $monitoringService->getCacheEfficiencyReport();

            return response()->json([
                'success' => true,
                'data' => [
                    'performance' => $performance,
                    'health' => $health,
                    'efficiency' => $efficiency,
                    'generated_at' => now()->toISOString()
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get performance data', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get performance data: ' . $e->getMessage()
            ], 500);
        }
    }
}
