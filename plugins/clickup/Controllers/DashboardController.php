<?php

namespace Plugins\ClickUp\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Plugins\ClickUp\Models\ClickUpList;
use Plugins\ClickUp\Models\ClickUpTask;
use Plugins\ClickUp\Models\ClickUpProductManager;
use Plugins\ClickUp\Models\ClickUpApiToken;
use Plugins\ClickUp\Services\ClickUpApiService;
use Plugins\ClickUp\Services\ClickUpSyncService;

class DashboardController extends Controller
{
    private ClickUpApiService $apiService;
    private ClickUpSyncService $syncService;

    public function __construct(ClickUpApiService $apiService, ClickUpSyncService $syncService)
    {
        $this->apiService = $apiService;
        $this->syncService = $syncService;
    }

    /**
     * Display the ClickUp dashboard
     */
    public function index(Request $request): View
    {
        // Check permissions
        if (!auth()->user()->hasPermission('view_clickup_data')) {
            abort(403, 'You do not have permission to view ClickUp data.');
        }

        // Get overview statistics
        $stats = $this->getOverviewStats();
        
        // Get recent activity
        $recentTasks = $this->getRecentTasks();
        
        // Get performance metrics
        $performanceMetrics = $this->getPerformanceMetrics();
        
        // Get sync status
        $syncStatus = $this->syncService->getSyncStatus();
        
        // Get API status
        $apiStatus = $this->apiService->getApiUsageStats();

        return view('plugins.clickup::dashboard.index', compact(
            'stats',
            'recentTasks',
            'performanceMetrics',
            'syncStatus',
            'apiStatus'
        ));
    }

    /**
     * Get overview statistics
     */
    private function getOverviewStats(): array
    {
        $totalLists = ClickUpList::count();
        $activeLists = ClickUpList::active()->count();
        $totalTasks = ClickUpTask::count();
        $activeTasks = ClickUpTask::active()->count();
        $completedTasks = ClickUpTask::completed()->count();
        $overdueTasks = ClickUpTask::overdue()->count();
        $productManagers = ClickUpProductManager::active()->count();

        $completionRate = $totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100, 1) : 0;

        return [
            'total_lists' => $totalLists,
            'active_lists' => $activeLists,
            'total_tasks' => $totalTasks,
            'active_tasks' => $activeTasks,
            'completed_tasks' => $completedTasks,
            'overdue_tasks' => $overdueTasks,
            'product_managers' => $productManagers,
            'completion_rate' => $completionRate,
        ];
    }

    /**
     * Get recent tasks activity
     */
    private function getRecentTasks(): array
    {
        $recentlyCreated = ClickUpTask::with(['list', 'list.productManager'])
            ->orderBy('date_created', 'desc')
            ->limit(5)
            ->get();

        $recentlyCompleted = ClickUpTask::completed()
            ->with(['list', 'list.productManager'])
            ->orderBy('date_closed', 'desc')
            ->limit(5)
            ->get();

        $recentlyUpdated = ClickUpTask::with(['list', 'list.productManager'])
            ->orderBy('date_updated', 'desc')
            ->limit(5)
            ->get();

        return [
            'recently_created' => $recentlyCreated,
            'recently_completed' => $recentlyCompleted,
            'recently_updated' => $recentlyUpdated,
        ];
    }

    /**
     * Get performance metrics
     */
    private function getPerformanceMetrics(): array
    {
        // Tasks completed this week
        $tasksThisWeek = ClickUpTask::completed()
            ->where('date_closed', '>=', now()->startOfWeek())
            ->count();

        // Tasks completed last week
        $tasksLastWeek = ClickUpTask::completed()
            ->whereBetween('date_closed', [
                now()->subWeek()->startOfWeek(),
                now()->subWeek()->endOfWeek()
            ])
            ->count();

        // Calculate week-over-week change
        $weeklyChange = $tasksLastWeek > 0 ? 
            round((($tasksThisWeek - $tasksLastWeek) / $tasksLastWeek) * 100, 1) : 
            ($tasksThisWeek > 0 ? 100 : 0);

        // Average completion time
        $completedTasks = ClickUpTask::completed()
            ->whereNotNull('date_created')
            ->whereNotNull('date_closed')
            ->get();

        $avgCompletionTime = 0;
        if ($completedTasks->count() > 0) {
            $totalHours = 0;
            foreach ($completedTasks as $task) {
                $created = \Carbon\Carbon::parse($task->date_created);
                $closed = \Carbon\Carbon::parse($task->date_closed);
                $totalHours += $created->diffInHours($closed);
            }
            $avgCompletionTime = round($totalHours / $completedTasks->count(), 1);
        }

        // Top performing product managers
        $topManagers = ClickUpProductManager::active()
            ->withCount(['tasks as completed_tasks_count' => function ($query) {
                $query->where('click_up_tasks.date_closed', '>=', now()->startOfMonth())
                      ->whereIn('click_up_tasks.status', ['complete', 'closed']);
            }])
            ->orderBy('completed_tasks_count', 'desc')
            ->limit(5)
            ->get();

        return [
            'tasks_this_week' => $tasksThisWeek,
            'tasks_last_week' => $tasksLastWeek,
            'weekly_change' => $weeklyChange,
            'avg_completion_time' => $avgCompletionTime,
            'top_managers' => $topManagers,
        ];
    }

    /**
     * Get dashboard data via AJAX
     */
    public function getData(Request $request)
    {
        if (!auth()->user()->hasPermission('view_clickup_data')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $type = $request->get('type');

        switch ($type) {
            case 'stats':
                return response()->json($this->getOverviewStats());
            
            case 'recent':
                return response()->json($this->getRecentTasks());
            
            case 'performance':
                return response()->json($this->getPerformanceMetrics());
            
            case 'sync_status':
                return response()->json($this->syncService->getSyncStatus());
            
            case 'api_status':
                return response()->json($this->apiService->getApiUsageStats());
            
            default:
                return response()->json(['error' => 'Invalid data type'], 400);
        }
    }

    /**
     * Refresh dashboard data
     */
    public function refresh(Request $request)
    {
        if (!auth()->user()->hasPermission('view_clickup_data')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // Clear relevant caches
        $this->apiService->clearCache([
            'clickup_dashboard_stats',
            'clickup_recent_tasks',
            'clickup_performance_metrics'
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Dashboard data refreshed successfully'
        ]);
    }
}
