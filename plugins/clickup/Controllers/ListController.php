<?php

namespace Plugins\ClickUp\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Plugins\ClickUp\Models\ClickUpList;
use Plugins\ClickUp\Models\ClickUpProductManager;

class ListController extends Controller
{
    /**
     * Display lists
     */
    public function index(Request $request): View
    {
        // Check permissions
        if (!auth()->user()->hasPermission('view_clickup_lists')) {
            abort(403, 'You do not have permission to view ClickUp lists.');
        }

        $query = ClickUpList::with(['productManager', 'creator']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('clickup_id', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('manager')) {
            $query->where('product_manager_id', $request->manager);
        }

        if ($request->filled('archived')) {
            $query->where('is_archived', $request->boolean('archived'));
        } else {
            // Default to non-archived
            $query->where('is_archived', false);
        }

        $lists = $query->orderBy('name')->paginate(20);
        $productManagers = ClickUpProductManager::active()->orderBy('name')->get();

        return view('plugins.clickup::lists.index', compact('lists', 'productManagers'));
    }

    /**
     * Show a specific list
     */
    public function show(ClickUpList $list): View
    {
        // Check permissions
        if (!auth()->user()->hasPermission('view_clickup_lists')) {
            abort(403, 'You do not have permission to view ClickUp lists.');
        }

        $list->load(['productManager', 'creator', 'tasks' => function ($query) {
            $query->orderBy('date_created', 'desc')->limit(20);
        }]);

        $metrics = $list->getPerformanceMetrics();

        return view('plugins.clickup::lists.show', compact('list', 'metrics'));
    }

    /**
     * Show edit form
     */
    public function edit(ClickUpList $list): View
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_lists')) {
            abort(403, 'You do not have permission to manage ClickUp lists.');
        }

        $productManagers = ClickUpProductManager::active()->orderBy('name')->get();

        return view('plugins.clickup::lists.edit', compact('list', 'productManagers'));
    }

    /**
     * Update list
     */
    public function update(Request $request, ClickUpList $list): RedirectResponse
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_lists')) {
            abort(403, 'You do not have permission to manage ClickUp lists.');
        }

        $validator = \Validator::make($request->all(), [
            'product_manager_id' => 'nullable|exists:click_up_product_managers,id',
            'priority' => 'nullable|in:urgent,high,normal,low',
            'status' => 'nullable|string|max:50',
            'notes' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $updateData = [];

            if ($request->filled('product_manager_id')) {
                $updateData['product_manager_id'] = $request->product_manager_id;
            }

            if ($request->filled('priority')) {
                $updateData['priority'] = $request->priority;
            }

            if ($request->filled('status')) {
                $updateData['status'] = $request->status;
            }

            // Update metadata with notes
            if ($request->filled('notes')) {
                $metadata = $list->metadata ?? [];
                $metadata['notes'] = $request->notes;
                $metadata['updated_by'] = auth()->id();
                $metadata['updated_at'] = now()->toISOString();
                $updateData['metadata'] = $metadata;
            }

            $list->update($updateData);

            return redirect()->route('clickup.lists.show', $list)
                ->with('success', 'List updated successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['general' => 'Failed to update list: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Assign product manager to list
     */
    public function assignManager(Request $request, ClickUpList $list)
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_lists')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $validator = \Validator::make($request->all(), [
            'product_manager_id' => 'required|exists:click_up_product_managers,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid product manager selected',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $list->update(['product_manager_id' => $request->product_manager_id]);

            $manager = ClickUpProductManager::find($request->product_manager_id);

            return response()->json([
                'success' => true,
                'message' => "List assigned to {$manager->name} successfully",
                'data' => [
                    'list_id' => $list->id,
                    'manager' => $manager
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to assign manager: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove product manager from list
     */
    public function removeManager(Request $request, ClickUpList $list)
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_lists')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            $list->update(['product_manager_id' => null]);

            return response()->json([
                'success' => true,
                'message' => 'Product manager removed from list successfully',
                'data' => ['list_id' => $list->id]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to remove manager: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * API endpoint for lists data
     */
    public function apiIndex(Request $request)
    {
        // Check permissions
        if (!auth()->user()->hasPermission('view_clickup_lists')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $query = ClickUpList::with(['productManager']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if ($request->filled('manager_id')) {
            $query->where('product_manager_id', $request->manager_id);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $lists = $query->orderBy('name')
                      ->limit($request->get('limit', 50))
                      ->get();

        return response()->json([
            'success' => true,
            'data' => $lists->map(function ($list) {
                return [
                    'id' => $list->id,
                    'clickup_id' => $list->clickup_id,
                    'name' => $list->name,
                    'description' => $list->description,
                    'status' => $list->status,
                    'priority' => $list->priority,
                    'task_count' => $list->task_count,
                    'completion_rate' => $list->completion_rate,
                    'product_manager' => $list->productManager ? [
                        'id' => $list->productManager->id,
                        'name' => $list->productManager->name,
                        'email' => $list->productManager->email
                    ] : null,
                    'last_synced_at' => $list->last_synced_at,
                    'sync_status' => $list->sync_status
                ];
            })
        ]);
    }
}
