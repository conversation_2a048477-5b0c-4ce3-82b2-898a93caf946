<?php

namespace Plugins\ClickUp\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Plugins\ClickUp\Models\ClickUpProductManager;
use App\Models\User;

class ProductManagerController extends Controller
{
    /**
     * Display product managers
     */
    public function index(Request $request): View
    {
        // Check permissions
        if (!auth()->user()->hasPermission('view_clickup_product_managers')) {
            abort(403, 'You do not have permission to view product managers.');
        }

        $query = ClickUpProductManager::with(['user', 'creator']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('department', 'like', "%{$search}%");
            });
        }

        if ($request->filled('department')) {
            $query->where('department', $request->department);
        }

        if ($request->filled('active')) {
            $query->where('is_active', $request->boolean('active'));
        } else {
            // Default to active only
            $query->where('is_active', true);
        }

        $managers = $query->orderBy('name')->paginate(20);

        return view('plugins.clickup::product-managers.index', compact('managers'));
    }

    /**
     * Show create form
     */
    public function create(): View
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_product_managers')) {
            abort(403, 'You do not have permission to manage product managers.');
        }

        // Get users who are not already product managers (excluding NULL user_ids)
        $existingProductManagerUserIds = ClickUpProductManager::whereNotNull('user_id')->pluck('user_id')->toArray();
        $users = User::whereNotIn('id', $existingProductManagerUserIds)->orderBy('name')->get();

        return view('plugins.clickup::product-managers.create', compact('users'));
    }

    /**
     * Store new product manager
     */
    public function store(Request $request): RedirectResponse
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_product_managers')) {
            abort(403, 'You do not have permission to manage product managers.');
        }

        $validator = \Validator::make($request->all(), [
            'user_id' => 'nullable|exists:users,id|unique:click_up_product_managers,user_id',
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:click_up_product_managers,email',
            'role' => 'nullable|string|max:100',
            'department' => 'nullable|string|max:100',
            'hire_date' => 'nullable|date',
            'timezone' => 'nullable|string|max:50'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $manager = ClickUpProductManager::create([
                'user_id' => $request->user_id,
                'name' => $request->name,
                'email' => $request->email,
                'role' => $request->role,
                'department' => $request->department,
                'hire_date' => $request->hire_date,
                'timezone' => $request->timezone ?? 'UTC',
                'is_active' => true,
                'created_by' => auth()->id()
            ]);

            return redirect()->route('clickup.product-managers.show', $manager)
                ->with('success', 'Product manager created successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['general' => 'Failed to create product manager: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Show a specific product manager
     */
    public function show(ClickUpProductManager $manager): View
    {
        // Check permissions
        if (!auth()->user()->hasPermission('view_clickup_product_managers')) {
            abort(403, 'You do not have permission to view product managers.');
        }

        $manager->load(['user', 'creator', 'clickupLists']);
        $metrics = $manager->getPerformanceMetrics();

        return view('plugins.clickup::product-managers.show', compact('manager', 'metrics'));
    }

    /**
     * Show edit form
     */
    public function edit(ClickUpProductManager $manager): View
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_product_managers')) {
            abort(403, 'You do not have permission to manage product managers.');
        }

        // Get users who are not already product managers (except current manager's user, excluding NULL user_ids)
        $existingProductManagerUserIds = ClickUpProductManager::where('id', '!=', $manager->id)
            ->whereNotNull('user_id')->pluck('user_id')->toArray();
        $users = User::where(function ($query) use ($existingProductManagerUserIds, $manager) {
            $query->whereNotIn('id', $existingProductManagerUserIds)
                  ->orWhere('id', $manager->user_id);
        })->orderBy('name')->get();

        return view('plugins.clickup::product-managers.edit', compact('manager', 'users'));
    }

    /**
     * Update product manager
     */
    public function update(Request $request, ClickUpProductManager $manager): RedirectResponse
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_product_managers')) {
            abort(403, 'You do not have permission to manage product managers.');
        }

        $validator = \Validator::make($request->all(), [
            'user_id' => 'nullable|exists:users,id|unique:click_up_product_managers,user_id,' . $manager->id,
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:click_up_product_managers,email,' . $manager->id,
            'role' => 'nullable|string|max:100',
            'department' => 'nullable|string|max:100',
            'hire_date' => 'nullable|date',
            'timezone' => 'nullable|string|max:50',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $manager->update([
                'user_id' => $request->user_id,
                'name' => $request->name,
                'email' => $request->email,
                'role' => $request->role,
                'department' => $request->department,
                'hire_date' => $request->hire_date,
                'timezone' => $request->timezone ?? 'UTC',
                'is_active' => $request->boolean('is_active', true)
            ]);

            return redirect()->route('clickup.product-managers.show', $manager)
                ->with('success', 'Product manager updated successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['general' => 'Failed to update product manager: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Delete product manager
     */
    public function destroy(ClickUpProductManager $manager): RedirectResponse
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_product_managers')) {
            abort(403, 'You do not have permission to manage product managers.');
        }

        try {
            // Check if manager has assigned lists
            if ($manager->clickupLists()->count() > 0) {
                return redirect()->back()
                    ->withErrors(['general' => 'Cannot delete product manager with assigned lists. Please reassign lists first.']);
            }

            $manager->delete();

            return redirect()->route('clickup.product-managers.index')
                ->with('success', 'Product manager deleted successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['general' => 'Failed to delete product manager: ' . $e->getMessage()]);
        }
    }

    /**
     * API endpoint for product managers data
     */
    public function apiIndex(Request $request)
    {
        // Check permissions
        if (!auth()->user()->hasPermission('view_clickup_product_managers')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $query = ClickUpProductManager::with(['user']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if ($request->filled('department')) {
            $query->where('department', $request->department);
        }

        $managers = $query->active()
                         ->orderBy('name')
                         ->limit($request->get('limit', 50))
                         ->get();

        return response()->json([
            'success' => true,
            'data' => $managers->map(function ($manager) {
                return [
                    'id' => $manager->id,
                    'name' => $manager->name,
                    'email' => $manager->email,
                    'role' => $manager->role,
                    'department' => $manager->department,
                    'completion_rate' => $manager->completion_rate,
                    'productivity_score' => $manager->productivity_score,
                    'total_lists' => $manager->clickupLists()->count(),
                    'total_tasks' => $manager->tasks()->count()
                ];
            })
        ]);
    }
}
