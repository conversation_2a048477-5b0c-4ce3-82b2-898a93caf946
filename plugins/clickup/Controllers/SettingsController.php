<?php

namespace Plugins\ClickUp\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Validator;
use Plugins\ClickUp\Models\ClickUpApiToken;
use Plugins\ClickUp\Models\ClickUpProductManager;
use Plugins\ClickUp\Models\ClickUpList;
use Plugins\ClickUp\Services\ClickUpApiService;

class SettingsController extends Controller
{
    private ClickUpApiService $apiService;

    public function __construct(ClickUpApiService $apiService)
    {
        $this->apiService = $apiService;
    }

    /**
     * Display unified ClickUp settings with tabs
     */
    public function index(): View
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            abort(403, 'You do not have permission to manage ClickUp settings.');
        }

        // Get all data needed for the unified interface
        $currentToken = ClickUpApiToken::getActiveToken();
        $apiStatus = $this->apiService->getApiUsageStats();

        // Workspace data
        $workspaces = $this->getWorkspaces();
        $selectedWorkspace = $this->getSelectedWorkspace();
        $selectedSpaces = $this->getSelectedSpaces();
        $spaces = $selectedWorkspace ? $this->getSpaces($selectedWorkspace) : [];

        // Teams data
        $teams = $this->getTeams();

        // Lists data
        $lists = $this->getLists();
        $assignedLists = $this->getAssignedListsFromDatabase();

        // General settings
        $syncSettings = $this->getSyncSettings();
        $generalSettings = $this->getGeneralSettings();

        return view('plugins.clickup::settings.unified', compact(
            'currentToken',
            'apiStatus',
            'workspaces',
            'selectedWorkspace',
            'selectedSpaces',
            'spaces',
            'teams',
            'lists',
            'assignedLists',
            'syncSettings',
            'generalSettings'
        ));
    }

    /**
     * Display legacy ClickUp settings (original interface)
     */
    public function legacy(): View
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            abort(403, 'You do not have permission to manage ClickUp settings.');
        }

        $currentToken = ClickUpApiToken::getActiveToken();
        $apiStatus = $this->apiService->getApiUsageStats();
        $workspaces = $this->getWorkspaces();
        $selectedWorkspace = $this->getSelectedWorkspace();
        $selectedSpaces = $this->getSelectedSpaces();
        $spaces = $selectedWorkspace ? $this->getSpaces($selectedWorkspace) : [];

        return view('plugins.clickup::settings.legacy', compact('currentToken', 'apiStatus', 'workspaces', 'selectedWorkspace', 'selectedSpaces', 'spaces'));
    }

    /**
     * Display sync settings
     */
    public function sync(): View
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            abort(403, 'You do not have permission to manage ClickUp settings.');
        }

        $currentToken = ClickUpApiToken::getActiveToken();
        $syncSettings = $this->getSyncSettings();

        return view('plugins.clickup::settings.sync', compact('currentToken', 'syncSettings'));
    }

    /**
     * Display general settings
     */
    public function general(): View
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            abort(403, 'You do not have permission to manage ClickUp settings.');
        }

        $generalSettings = $this->getGeneralSettings();

        return view('plugins.clickup::settings.general', compact('generalSettings'));
    }

    /**
     * Display list assignments
     */
    public function assignments(): View
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            abort(403, 'You do not have permission to manage ClickUp settings.');
        }

        $productManagers = ClickUpProductManager::with(['clickupLists'])->orderBy('name')->get();
        $unassignedLists = ClickUpList::whereNull('product_manager_id')->orderBy('name')->get();
        $allLists = ClickUpList::orderBy('name')->get();

        return view('plugins.clickup::settings.assignments', compact('productManagers', 'unassignedLists', 'allLists'));
    }

    /**
     * Save API token
     */
    public function saveToken(Request $request): RedirectResponse
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            abort(403, 'You do not have permission to manage ClickUp settings.');
        }

        $validator = Validator::make($request->all(), [
            'token' => 'required|string|min:10',
            'name' => 'nullable|string|max:255'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Debug: Log the received token
            \Log::info('ClickUp Token Debug', [
                'raw_token' => $request->token,
                'token_length' => strlen($request->token),
                'token_trimmed' => trim($request->token),
                'trimmed_length' => strlen(trim($request->token))
            ]);

            // Create new token instance to test
            $testToken = new ClickUpApiToken([
                'name' => $request->name ?: 'Default Token',
                'is_active' => false,
                'created_by' => auth()->id()
            ]);

            // Trim the token to remove any whitespace
            $cleanToken = trim($request->token);
            $testToken->setTokenAttribute($cleanToken);

            // Validate token format first
            if (!$testToken->hasValidTokenFormat()) {
                \Log::error('ClickUp Token Validation Failed', [
                    'token' => $cleanToken,
                    'length' => strlen($cleanToken),
                    'format_check' => $testToken->hasValidTokenFormat(),
                    'retrieved_token' => $testToken->getTokenAttribute()
                ]);

                return redirect()->back()
                    ->withErrors(['token' => 'Invalid token format. ClickUp tokens should start with "pk_" followed by numbers and letters. Received: ' . substr($cleanToken, 0, 10) . '...'])
                    ->withInput();
            }

            // Test the token by making a simple API call
            $tempApiService = new ClickUpApiService($testToken);

            $testResponse = $tempApiService->testConnection();

            if (!$testResponse['success']) {
                return redirect()->back()
                    ->withErrors(['token' => 'Token validation failed: ' . $testResponse['message']])
                    ->withInput();
            }

            // Token is valid, save it
            $token = ClickUpApiToken::create([
                'name' => $request->name ?: 'Default Token',
                'encrypted_token' => $testToken->encrypted_token,
                'is_active' => true,
                'created_by' => auth()->id(),
                'metadata' => [
                    'user_info' => $testResponse['user'] ?? null,
                    'created_at' => now()->toISOString()
                ]
            ]);

            return redirect()->route('clickup.settings.index')
                ->with('success', 'ClickUp API token saved successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['token' => 'Failed to save token: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Delete API token
     */
    public function deleteToken(Request $request): RedirectResponse
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            abort(403, 'You do not have permission to manage ClickUp settings.');
        }

        $token = ClickUpApiToken::getActiveToken();

        if (!$token) {
            return redirect()->back()
                ->withErrors(['general' => 'No active token found.']);
        }

        try {
            $token->delete();

            return redirect()->route('clickup.settings.index')
                ->with('success', 'ClickUp API token deleted successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['general' => 'Failed to delete token: ' . $e->getMessage()]);
        }
    }

    /**
     * Test API connection
     */
    public function testConnection(Request $request)
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $token = $request->input('token');

        if (!$token) {
            // Test existing token
            $result = $this->apiService->testConnection();
        } else {
            // Test provided token
            $testToken = new ClickUpApiToken([
                'name' => 'Test Token',
                'is_active' => false,
                'created_by' => auth()->id()
            ]);
            $testToken->setTokenAttribute($token);

            $tempApiService = new ClickUpApiService($testToken);
            
            $result = $tempApiService->testConnection();
        }

        return response()->json($result);
    }

    /**
     * Get API usage statistics
     */
    public function getApiStats(Request $request)
    {
        // Check permissions
        if (!auth()->user()->hasPermission('view_clickup_data')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $stats = $this->apiService->getApiUsageStats();
        
        return response()->json($stats);
    }

    /**
     * Update settings
     */
    public function updateSettings(Request $request): RedirectResponse
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            abort(403, 'You do not have permission to manage ClickUp settings.');
        }

        $validator = Validator::make($request->all(), [
            'sync_interval' => 'nullable|integer|min:300|max:86400', // 5 minutes to 24 hours
            'cache_duration' => 'nullable|integer|min:60|max:7200', // 1 minute to 2 hours
            'auto_sync' => 'boolean',
            'notifications_enabled' => 'boolean'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Update settings in config or database
            // For now, we'll store in the token metadata
            $token = ClickUpApiToken::getActiveToken();
            
            if ($token) {
                $metadata = $token->metadata ?? [];
                $metadata['settings'] = [
                    'sync_interval' => $request->sync_interval ?? 3600,
                    'cache_duration' => $request->cache_duration ?? 1800,
                    'auto_sync' => $request->boolean('auto_sync', false),
                    'notifications_enabled' => $request->boolean('notifications_enabled', true),
                    'updated_at' => now()->toISOString(),
                    'updated_by' => auth()->id()
                ];
                
                $token->update(['metadata' => $metadata]);
            }

            return redirect()->route('clickup.settings.index')
                ->with('success', 'Settings updated successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['general' => 'Failed to update settings: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Reset settings to defaults
     */
    public function resetSettings(Request $request): RedirectResponse
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            abort(403, 'You do not have permission to manage ClickUp settings.');
        }

        try {
            $token = ClickUpApiToken::getActiveToken();
            
            if ($token) {
                $metadata = $token->metadata ?? [];
                $metadata['settings'] = [
                    'sync_interval' => 3600,
                    'cache_duration' => 1800,
                    'auto_sync' => false,
                    'notifications_enabled' => true,
                    'reset_at' => now()->toISOString(),
                    'reset_by' => auth()->id()
                ];
                
                $token->update(['metadata' => $metadata]);
            }

            return redirect()->route('clickup.settings.index')
                ->with('success', 'Settings reset to defaults successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['general' => 'Failed to reset settings: ' . $e->getMessage()]);
        }
    }

    /**
     * Update sync settings
     */
    public function updateSyncSettings(Request $request): RedirectResponse
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            abort(403, 'You do not have permission to manage ClickUp settings.');
        }

        $validator = Validator::make($request->all(), [
            'sync_interval' => 'required|integer|min:300|max:86400',
            'auto_sync' => 'boolean',
            'sync_on_startup' => 'boolean',
            'batch_size' => 'required|integer|min:10|max:1000'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $this->saveSyncSettings([
                'sync_interval' => $request->sync_interval,
                'auto_sync' => $request->boolean('auto_sync'),
                'sync_on_startup' => $request->boolean('sync_on_startup'),
                'batch_size' => $request->batch_size,
                'updated_at' => now()->toISOString(),
                'updated_by' => auth()->id()
            ]);

            return redirect()->route('clickup.settings.sync')
                ->with('success', 'Sync settings updated successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['general' => 'Failed to update sync settings: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Update general settings
     */
    public function updateGeneralSettings(Request $request): RedirectResponse
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            abort(403, 'You do not have permission to manage ClickUp settings.');
        }

        $validator = Validator::make($request->all(), [
            'notifications_enabled' => 'boolean',
            'email_notifications' => 'boolean',
            'cache_duration' => 'required|integer|min:60|max:7200',
            'timezone' => 'required|string|max:50'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $this->saveGeneralSettings([
                'notifications_enabled' => $request->boolean('notifications_enabled'),
                'email_notifications' => $request->boolean('email_notifications'),
                'cache_duration' => $request->cache_duration,
                'timezone' => $request->timezone,
                'updated_at' => now()->toISOString(),
                'updated_by' => auth()->id()
            ]);

            return redirect()->route('clickup.settings.general')
                ->with('success', 'General settings updated successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['general' => 'Failed to update general settings: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Get sync settings
     */
    private function getSyncSettings(): array
    {
        $token = ClickUpApiToken::getActiveToken();
        $defaults = [
            'sync_interval' => 3600,
            'auto_sync' => false,
            'sync_on_startup' => true,
            'batch_size' => 100
        ];

        if (!$token || !isset($token->metadata['sync_settings'])) {
            return $defaults;
        }

        return array_merge($defaults, $token->metadata['sync_settings']);
    }

    /**
     * Get general settings
     */
    private function getGeneralSettings(): array
    {
        $token = ClickUpApiToken::getActiveToken();
        $defaults = [
            'notifications_enabled' => true,
            'email_notifications' => false,
            'cache_duration' => 1800,
            'timezone' => 'UTC'
        ];

        if (!$token || !isset($token->metadata['general_settings'])) {
            return $defaults;
        }

        return array_merge($defaults, $token->metadata['general_settings']);
    }

    /**
     * Save sync settings
     */
    private function saveSyncSettings(array $settings): void
    {
        $token = ClickUpApiToken::getActiveToken();
        if ($token) {
            $metadata = $token->metadata ?? [];
            $metadata['sync_settings'] = $settings;
            $token->update(['metadata' => $metadata]);
        }
    }

    /**
     * Update cache configuration
     */
    public function updateCacheConfig(Request $request)
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            return redirect()->route('clickup.settings.index')
                ->with('error', 'You do not have permission to manage ClickUp settings.');
        }

        $request->validate([
            'cache_duration' => 'required|integer|min:300|max:86400',
            'auto_warmup' => 'boolean'
        ]);

        try {
            $token = ClickUpApiToken::getActiveToken();

            if ($token) {
                $metadata = $token->metadata ?? [];
                $metadata['general_settings'] = array_merge(
                    $metadata['general_settings'] ?? [],
                    [
                        'cache_duration' => $request->cache_duration,
                        'auto_warmup' => $request->boolean('auto_warmup', false),
                        'updated_at' => now()->toISOString(),
                        'updated_by' => auth()->id()
                    ]
                );

                $token->update(['metadata' => $metadata]);

                \Log::info('ClickUp cache configuration updated', [
                    'user_id' => auth()->id(),
                    'cache_duration' => $request->cache_duration,
                    'auto_warmup' => $request->boolean('auto_warmup', false)
                ]);
            }

            return redirect()->route('clickup.settings.index')
                ->with('success', 'Cache configuration updated successfully!');

        } catch (\Exception $e) {
            \Log::error('Failed to update cache configuration', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return redirect()->route('clickup.settings.index')
                ->with('error', 'Failed to update cache configuration: ' . $e->getMessage());
        }
    }

    /**
     * Save general settings
     */
    private function saveGeneralSettings(array $settings): void
    {
        $token = ClickUpApiToken::getActiveToken();
        if ($token) {
            $metadata = $token->metadata ?? [];
            $metadata['general_settings'] = $settings;
            $token->update(['metadata' => $metadata]);
        }
    }

    /**
     * Assign lists to a product manager
     */
    public function assignLists(Request $request): RedirectResponse
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            abort(403, 'You do not have permission to manage ClickUp settings.');
        }

        $validator = Validator::make($request->all(), [
            'product_manager_id' => 'required|exists:click_up_product_managers,id',
            'list_ids' => 'required|array',
            'list_ids.*' => 'exists:click_up_lists,id'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $productManager = ClickUpProductManager::findOrFail($request->product_manager_id);

            // Assign the selected lists to this product manager
            ClickUpList::whereIn('id', $request->list_ids)
                ->update(['product_manager_id' => $productManager->id]);

            $assignedCount = count($request->list_ids);

            return redirect()->route('clickup.settings.assignments')
                ->with('success', "Successfully assigned {$assignedCount} lists to {$productManager->name}!");

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['general' => 'Failed to assign lists: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Unassign a list from its product manager
     */
    public function unassignList(Request $request): RedirectResponse
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            abort(403, 'You do not have permission to manage ClickUp settings.');
        }

        $validator = Validator::make($request->all(), [
            'list_id' => 'required|exists:click_up_lists,id'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator);
        }

        try {
            $list = ClickUpList::findOrFail($request->list_id);
            $managerName = $list->productManager ? $list->productManager->name : 'Unknown';

            $list->update(['product_manager_id' => null]);

            return redirect()->route('clickup.settings.assignments')
                ->with('success', "Successfully unassigned '{$list->name}' from {$managerName}!");

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['general' => 'Failed to unassign list: ' . $e->getMessage()]);
        }
    }

    /**
     * Get available workspaces
     */
    private function getWorkspaces(): array
    {
        if (!$this->apiService->isConfigured()) {
            return [];
        }

        try {
            $response = $this->apiService->getTeams();
            if ($response['success']) {
                return $response['data']['teams'] ?? [];
            }
        } catch (\Exception $e) {
            \Log::error('Failed to fetch ClickUp workspaces', ['error' => $e->getMessage()]);
        }

        return [];
    }

    /**
     * Get selected workspace from settings
     */
    private function getSelectedWorkspace(): ?string
    {
        $token = ClickUpApiToken::getActiveToken();
        if (!$token || !isset($token->metadata['selected_workspace'])) {
            return null;
        }

        return $token->metadata['selected_workspace'];
    }

    /**
     * Get selected spaces from settings
     */
    private function getSelectedSpaces(): array
    {
        $token = ClickUpApiToken::getActiveToken();
        if (!$token || !isset($token->metadata['selected_spaces'])) {
            return [];
        }

        return $token->metadata['selected_spaces'] ?? [];
    }

    /**
     * Get spaces for selected workspace
     */
    private function getSpaces(?string $workspaceId): array
    {
        \Log::info('ClickUp: Getting spaces for workspace', ['workspace_id' => $workspaceId]);

        if (!$workspaceId) {
            \Log::warning('ClickUp: No workspace ID provided');
            return [];
        }

        if (!$this->apiService->isConfigured()) {
            \Log::warning('ClickUp: API service not configured');
            return [];
        }

        try {
            $response = $this->apiService->getSpaces($workspaceId);
            \Log::info('ClickUp: Spaces API response', [
                'workspace_id' => $workspaceId,
                'success' => $response['success'] ?? false,
                'has_data' => isset($response['data']),
                'response_keys' => array_keys($response['data'] ?? [])
            ]);

            if ($response['success']) {
                $spaces = $response['data']['spaces'] ?? [];
                \Log::info('ClickUp: Retrieved spaces', [
                    'workspace_id' => $workspaceId,
                    'space_count' => count($spaces),
                    'space_names' => array_column($spaces, 'name')
                ]);
                return $spaces;
            } else {
                \Log::warning('ClickUp: Failed to get spaces', [
                    'workspace_id' => $workspaceId,
                    'response' => $response
                ]);
            }
        } catch (\Exception $e) {
            \Log::error('ClickUp: Exception while fetching spaces', [
                'workspace_id' => $workspaceId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        return [];
    }

    /**
     * Update workspace and space selection
     */
    public function updateWorkspaceSettings(Request $request): RedirectResponse
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            abort(403, 'You do not have permission to manage ClickUp settings.');
        }

        $validator = Validator::make($request->all(), [
            'workspace_id' => 'required|string',
            'space_ids' => 'array',
            'space_ids.*' => 'string'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $token = ClickUpApiToken::getActiveToken();
            if ($token) {
                $metadata = $token->metadata ?? [];
                $metadata['selected_workspace'] = $request->workspace_id;
                $metadata['selected_spaces'] = $request->space_ids ?? [];
                $token->update(['metadata' => $metadata]);
            }

            return redirect()->route('clickup.settings.index')
                ->with('success', 'Workspace settings updated successfully! You can now sync data from the selected workspace.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['general' => 'Failed to update workspace settings: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Get teams data for the unified interface
     */
    private function getTeams(): array
    {
        try {
            $token = ClickUpApiToken::getActiveToken();
            if (!$token) {
                return [];
            }

            $response = $this->apiService->getTeams();
            return $response['success'] ? ($response['data']['teams'] ?? []) : [];
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Get lists data for the unified interface
     */
    private function getLists(): \Illuminate\Support\Collection
    {
        try {
            \Log::info('ClickUp: Starting to fetch lists for settings interface');

            $selectedWorkspace = $this->getSelectedWorkspace();
            \Log::info('ClickUp: Selected workspace', ['workspace_id' => $selectedWorkspace]);

            if (!$selectedWorkspace) {
                \Log::warning('ClickUp: No workspace selected, cannot fetch lists');
                return collect([]);
            }

            $spaces = $this->getSpaces($selectedWorkspace);
            \Log::info('ClickUp: Retrieved spaces', ['space_count' => count($spaces), 'spaces' => array_column($spaces, 'name')]);

            if (empty($spaces)) {
                \Log::warning('ClickUp: No spaces found for workspace', ['workspace_id' => $selectedWorkspace]);
                return collect([]);
            }

            $selectedSpaces = $this->getSelectedSpaces();
            \Log::info('ClickUp: Selected spaces configuration', ['selected_spaces' => $selectedSpaces]);

            // Get workspace name for full path
            $workspaceName = 'Unknown Workspace';
            $workspaces = $this->getWorkspaces();
            foreach ($workspaces as $workspace) {
                if ($workspace['id'] === $selectedWorkspace) {
                    $workspaceName = $workspace['name'];
                    break;
                }
            }

            $allLists = [];
            $spacesToProcess = $spaces;

            // If specific spaces are selected, filter to only those spaces
            if (!empty($selectedSpaces)) {
                $spacesToProcess = array_filter($spaces, function($space) use ($selectedSpaces) {
                    return in_array($space['id'], $selectedSpaces);
                });
                \Log::info('ClickUp: Filtered to selected spaces', ['filtered_space_count' => count($spacesToProcess)]);

                // If no spaces match the selection, log this issue
                if (empty($spacesToProcess)) {
                    \Log::warning('ClickUp: No spaces match the selected space configuration', [
                        'selected_spaces' => $selectedSpaces,
                        'available_spaces' => array_column($spaces, 'id')
                    ]);
                }
            } else {
                \Log::info('ClickUp: No specific spaces selected, processing all spaces');
            }

            foreach ($spacesToProcess as $space) {
                \Log::info('ClickUp: Fetching lists for space', ['space_id' => $space['id'], 'space_name' => $space['name']]);

                // First, get folderless lists directly in the space
                $response = $this->apiService->getLists($space['id']);
                \Log::info('ClickUp: Raw API response for space lists', [
                    'space_id' => $space['id'],
                    'success' => $response['success'] ?? false,
                    'status_code' => $response['status_code'] ?? null,
                    'has_data' => isset($response['data']),
                    'full_response' => $response
                ]);

                if ($response['success']) {
                    // Check different possible response structures
                    $lists = [];
                    if (isset($response['data']['lists'])) {
                        $lists = $response['data']['lists'];
                        \Log::info('ClickUp: Found folderless lists in response[data][lists]', ['count' => count($lists)]);
                    } elseif (isset($response['data']) && is_array($response['data'])) {
                        // Sometimes the lists are directly in data
                        $lists = $response['data'];
                        \Log::info('ClickUp: Found folderless lists directly in response[data]', ['count' => count($lists)]);
                    } else {
                        \Log::warning('ClickUp: Unexpected folderless lists response structure', [
                            'space_id' => $space['id'],
                            'response_data_keys' => array_keys($response['data'] ?? []),
                            'response_data' => $response['data'] ?? null
                        ]);
                    }

                    foreach ($lists as $list) {
                        $list['space_name'] = $space['name'];
                        $list['workspace_name'] = $workspaceName;
                        $list['full_path'] = $workspaceName . ' > ' . $space['name'] . ' > ' . $list['name'];
                        $allLists[] = $list;
                    }
                }

                // Also get lists from folders in this space
                try {
                    $foldersResponse = $this->apiService->getFolders($space['id']);
                    \Log::info('ClickUp: Folders response for space', [
                        'space_id' => $space['id'],
                        'success' => $foldersResponse['success'] ?? false,
                        'folder_count' => isset($foldersResponse['data']['folders']) ? count($foldersResponse['data']['folders']) : 0
                    ]);

                    if ($foldersResponse['success'] && isset($foldersResponse['data']['folders'])) {
                        foreach ($foldersResponse['data']['folders'] as $folder) {
                            \Log::info('ClickUp: Fetching lists from folder', [
                                'folder_id' => $folder['id'],
                                'folder_name' => $folder['name']
                            ]);

                            $folderListsResponse = $this->apiService->getListsInFolder($folder['id']);
                            if ($folderListsResponse['success'] && isset($folderListsResponse['data']['lists'])) {
                                foreach ($folderListsResponse['data']['lists'] as $list) {
                                    $list['space_name'] = $space['name'];
                                    $list['folder_name'] = $folder['name'];
                                    $list['workspace_name'] = $workspaceName;
                                    $list['full_path'] = $workspaceName . ' > ' . $space['name'] . ' > ' . $folder['name'] . ' > ' . $list['name'];
                                    $allLists[] = $list;
                                }
                                \Log::info('ClickUp: Added lists from folder', [
                                    'folder_id' => $folder['id'],
                                    'list_count' => count($folderListsResponse['data']['lists'])
                                ]);
                            }
                        }
                    }
                } catch (\Exception $e) {
                    \Log::warning('ClickUp: Failed to fetch folders/folder lists', [
                        'space_id' => $space['id'],
                        'error' => $e->getMessage()
                    ]);
                }

                \Log::info('ClickUp: Total lists collected for space', [
                    'space_id' => $space['id'],
                    'total_lists' => count($allLists)
                ]);
            }

            \Log::info('ClickUp: Total lists retrieved', ['total_count' => count($allLists)]);
            return collect($allLists);

        } catch (\Exception $e) {
            \Log::error('ClickUp: Exception while fetching lists', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return collect([]);
        }
    }

    /**
     * Get assigned lists from database
     */
    private function getAssignedListsFromDatabase(): array
    {
        try {
            return \Plugins\ClickUp\Models\ClickUpListAssignment::active()
                ->with('assignedToContact')
                ->get()
                ->toArray();
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Get assigned lists for the unified interface (legacy method)
     */
    private function getAssignedLists(): array
    {
        return $this->getAssignedListsFromDatabase();
    }

    /**
     * Assign a list to a contact
     */
    public function assignList(Request $request)
    {
        $startTime = microtime(true);

        try {
            $request->validate([
                'list_id' => 'required|string',
                'contact_id' => 'required|integer|exists:business_contacts,id'
            ]);

            // Get the list details from cache (much faster than API call)
            $listResponse = $this->apiService->getList($request->list_id, true);
            if (!$listResponse['success']) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to fetch list details: ' . ($listResponse['message'] ?? 'Unknown error')
                ], 400);
            }

            $cached = isset($listResponse['source']) && $listResponse['source'] !== 'api';

            // Get contact details
            $contact = \Plugins\business\Models\Contact::find($request->contact_id);
            if (!$contact) {
                return response()->json([
                    'success' => false,
                    'message' => 'Contact not found'
                ], 404);
            }

            \Log::info('ClickUp: Assigning list to contact', [
                'list_id' => $request->list_id,
                'contact_id' => $request->contact_id,
                'contact_name' => $contact->name,
                'data_source' => $listResponse['source'] ?? 'unknown',
                'cached' => isset($listResponse['source']) && $listResponse['source'] !== 'api'
            ]);

            // Create or update assignment
            $assignment = \Plugins\ClickUp\Models\ClickUpListAssignment::assignList(
                $listResponse['data'],
                $contact->id,
                $contact->name,
                auth()->id()
            );

            // Automatically add contact to team members if not already a member
            $teamMember = \Plugins\ClickUp\Models\ClickUpTeamMember::createFromContact(
                $contact,
                auth()->user(),
                'member' // Default role
            );

            // Assign the list to the team member
            $teamMember->assignList($request->list_id);

            // Record performance metrics
            $duration = microtime(true) - $startTime;
            $monitoringService = new \Plugins\ClickUp\Services\ClickUpMonitoringService();
            $monitoringService->recordAssignmentPerformance($request->list_id, $duration, $cached);

            \Log::info('ClickUp: List assigned and team member updated', [
                'assignment_id' => $assignment->id,
                'team_member_id' => $teamMember->id,
                'assigned_lists_count' => $teamMember->assigned_lists_count,
                'duration_ms' => round($duration * 1000, 2),
                'cached' => $cached
            ]);

            return response()->json([
                'success' => true,
                'message' => 'List assigned successfully and contact added to team',
                'assignment' => $assignment,
                'team_member' => [
                    'id' => $teamMember->id,
                    'display_name' => $teamMember->display_name,
                    'role' => $teamMember->role_display,
                    'assigned_lists_count' => $teamMember->assigned_lists_count
                ],
                'performance' => [
                    'duration_ms' => round($duration * 1000, 2),
                    'cached' => $cached
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to assign list: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Unassign a list (new method for API)
     */
    public function unassignListApi(Request $request)
    {
        try {
            $request->validate([
                'list_id' => 'required|string'
            ]);

            $result = \Plugins\ClickUp\Models\ClickUpListAssignment::unassignList($request->list_id);

            return response()->json([
                'success' => true,
                'message' => 'List unassigned successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to unassign list: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Assign a list to a contact (API method)
     */
    public function assignListApi(Request $request)
    {
        try {
            $request->validate([
                'list_id' => 'required|string',
                'contact_id' => 'required|integer|exists:business_contacts,id'
            ]);

            // Get the list details from cache (much faster than API call)
            $listResponse = $this->apiService->getList($request->list_id, true);

            // If API call fails, create a basic list data structure for assignment
            if (!$listResponse['success']) {
                \Log::warning('ClickUp: API call failed for list assignment, using basic list data', [
                    'list_id' => $request->list_id,
                    'error' => $listResponse['message'] ?? 'Unknown error'
                ]);

                // Create basic list data for assignment
                $listResponse = [
                    'success' => true,
                    'data' => [
                        'id' => $request->list_id,
                        'name' => 'ClickUp List ' . $request->list_id,
                        'space' => [
                            'id' => null,
                            'name' => 'Unknown Space',
                            'workspace_id' => null
                        ]
                    ]
                ];
            }

            // Get contact details
            $contact = \Plugins\business\Models\Contact::find($request->contact_id);
            if (!$contact) {
                return response()->json([
                    'success' => false,
                    'message' => 'Contact not found'
                ], 404);
            }

            // Create or update assignment
            $assignment = \Plugins\ClickUp\Models\ClickUpListAssignment::assignList(
                $listResponse['data'],
                $contact->id,
                $contact->name,
                auth()->id()
            );

            // Log successful assignment
            \Log::info('ClickUp: List assigned successfully via API', [
                'assignment_id' => $assignment->id,
                'list_id' => $request->list_id,
                'contact_id' => $contact->id,
                'contact_name' => $contact->name
            ]);

            return response()->json([
                'success' => true,
                'message' => 'List assigned successfully',
                'assignment' => [
                    'id' => $assignment->id,
                    'list_id' => $assignment->clickup_list_id,
                    'contact_name' => $assignment->assigned_to_contact_name,
                    'assigned_at' => $assignment->assigned_at
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('ClickUp: Failed to assign list via API', [
                'error' => $e->getMessage(),
                'list_id' => $request->list_id ?? null,
                'contact_id' => $request->contact_id ?? null
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to assign list: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available contacts for assignment
     */
    public function getContacts()
    {
        try {
            $contacts = \Plugins\business\Models\Contact::select('id', 'name', 'arabic_name', 'position', 'email', 'phone', 'department')
                ->where('is_active', true)
                ->orderBy('name')
                ->get();

            \Log::info('ClickUp: Fetched contacts for assignment', [
                'contact_count' => $contacts->count(),
                'sample_contact' => $contacts->first() ? $contacts->first()->toArray() : null
            ]);

            return response()->json([
                'success' => true,
                'contacts' => $contacts
            ]);

        } catch (\Exception $e) {
            \Log::error('ClickUp: Failed to fetch contacts', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch contacts: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get list details from ClickUp API
     */
    public function getListDetails(string $listId)
    {
        try {
            $response = $this->apiService->getList($listId);

            if (!$response['success']) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to fetch list details'
                ], 400);
            }

            return response()->json([
                'success' => true,
                'list' => $response['data']
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch list details: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Debug endpoint to test list fetching
     */
    public function debugLists()
    {
        // Check permissions
        if (!auth()->user()->hasPermission('manage_clickup_settings')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            $debug = [
                'timestamp' => now()->toISOString(),
                'api_configured' => $this->apiService->isConfigured(),
                'selected_workspace' => $this->getSelectedWorkspace(),
                'selected_spaces' => $this->getSelectedSpaces(),
            ];

            $selectedWorkspace = $this->getSelectedWorkspace();
            if ($selectedWorkspace) {
                $debug['spaces'] = $this->getSpaces($selectedWorkspace);
                $lists = $this->getLists();
                $debug['lists'] = $lists->toArray(); // Convert collection to array for JSON

                // Add more detailed API testing
                $spaces = $this->getSpaces($selectedWorkspace);
                if (!empty($spaces)) {
                    $testSpace = $spaces[0]; // Test with first space
                    $debug['api_test'] = [
                        'test_space_id' => $testSpace['id'],
                        'test_space_name' => $testSpace['name'],
                        'folderless_lists_response' => $this->apiService->getLists($testSpace['id']),
                        'folders_response' => $this->apiService->getFolders($testSpace['id'])
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'debug' => $debug
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }

    /**
     * Debug endpoint to test contacts API
     */
    public function debugContacts()
    {
        try {
            $contacts = \Plugins\business\Models\Contact::select('id', 'name', 'arabic_name', 'position', 'email', 'phone', 'department')
                ->where('is_active', true)
                ->orderBy('name')
                ->get();

            return response()->json([
                'success' => true,
                'debug' => [
                    'timestamp' => now()->toISOString(),
                    'contact_count' => $contacts->count(),
                    'contacts' => $contacts->toArray(),
                    'sample_contact' => $contacts->first() ? $contacts->first()->toArray() : null,
                    'table_exists' => \Schema::hasTable('business_contacts'),
                    'columns' => \Schema::hasTable('business_contacts') ? \Schema::getColumnListing('business_contacts') : []
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }
}
