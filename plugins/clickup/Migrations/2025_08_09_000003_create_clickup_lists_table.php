<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('click_up_lists', function (Blueprint $table) {
            $table->id();
            $table->string('clickup_id')->unique();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('folder_id')->nullable();
            $table->string('space_id')->nullable();
            $table->string('team_id')->nullable();
            $table->foreignId('product_manager_id')->nullable()->constrained('click_up_product_managers')->onDelete('set null');
            $table->string('status')->default('active');
            $table->string('priority')->nullable();
            $table->string('color')->nullable();
            $table->string('avatar_url')->nullable();
            $table->boolean('is_private')->default(false);
            $table->boolean('is_archived')->default(false);
            $table->integer('task_count')->default(0);
            $table->timestamp('due_date')->nullable();
            $table->timestamp('start_date')->nullable();
            $table->string('permission_level')->nullable();
            $table->timestamp('last_synced_at')->nullable();
            $table->string('sync_status')->default('pending');
            $table->json('metadata')->nullable();
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['clickup_id', 'deleted_at']);
            $table->index(['product_manager_id', 'is_archived']);
            $table->index(['status', 'is_archived']);
            $table->index('last_synced_at');
            $table->index('sync_status');
            $table->index(['space_id', 'team_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('click_up_lists');
    }
};
