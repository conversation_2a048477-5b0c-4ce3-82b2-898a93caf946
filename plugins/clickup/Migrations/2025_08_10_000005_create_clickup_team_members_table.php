<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clickup_team_members', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('contact_id'); // Reference to business_contacts.id
            $table->string('contact_name'); // Cached contact name for performance
            $table->string('contact_arabic_name')->nullable(); // Cached Arabic name
            $table->string('contact_email')->nullable(); // Cached email
            $table->string('contact_position')->nullable(); // Cached position
            $table->string('role')->default('member'); // Team role: member, lead, manager
            $table->boolean('is_active')->default(true);
            $table->timestamp('joined_at')->useCurrent();
            $table->unsignedBigInteger('added_by'); // User who added this member
            $table->json('assigned_lists')->nullable(); // Array of ClickUp list IDs assigned to this member
            $table->json('permissions')->nullable(); // Custom permissions for this team member
            $table->text('notes')->nullable();
            $table->timestamps();

            // Indexes
            $table->index('contact_id');
            $table->index('is_active');
            $table->index('role');
            $table->unique('contact_id'); // Each contact can only be a team member once

            // Foreign keys
            $table->foreign('contact_id')->references('id')->on('business_contacts')->onDelete('cascade');
            $table->foreign('added_by')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clickup_team_members');
    }
};
