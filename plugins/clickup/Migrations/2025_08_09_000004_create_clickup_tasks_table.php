<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('click_up_tasks', function (Blueprint $table) {
            $table->id();
            $table->string('clickup_id')->unique();
            $table->foreignId('list_id')->constrained('click_up_lists')->onDelete('cascade');
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('status');
            $table->string('priority')->nullable();
            $table->json('assignees')->nullable();
            $table->json('watchers')->nullable();
            $table->json('tags')->nullable();
            $table->timestamp('due_date')->nullable();
            $table->timestamp('start_date')->nullable();
            $table->timestamp('date_created')->nullable();
            $table->timestamp('date_updated')->nullable();
            $table->timestamp('date_closed')->nullable();
            $table->string('creator_id')->nullable();
            $table->string('url')->nullable();
            $table->string('parent_task_id')->nullable();
            $table->integer('order_index')->nullable();
            $table->integer('points')->nullable();
            $table->bigInteger('time_estimate')->nullable(); // in milliseconds
            $table->bigInteger('time_spent')->nullable(); // in milliseconds
            $table->json('custom_fields')->nullable();
            $table->json('attachments')->nullable();
            $table->json('dependencies')->nullable();
            $table->timestamp('last_synced_at')->nullable();
            $table->string('sync_status')->default('pending');
            $table->json('metadata')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index(['clickup_id', 'deleted_at']);
            $table->index(['list_id', 'status']);
            $table->index(['status', 'priority']);
            $table->index('due_date');
            $table->index('date_created');
            $table->index('date_closed');
            $table->index('last_synced_at');
            $table->index('sync_status');
            $table->index('parent_task_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('click_up_tasks');
    }
};
