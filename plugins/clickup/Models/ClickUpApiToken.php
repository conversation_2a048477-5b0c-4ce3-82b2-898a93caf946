<?php

namespace Plugins\ClickUp\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Crypt;

class ClickUpApiToken extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'encrypted_token',
        'is_active',
        'last_used_at',
        'expires_at',
        'rate_limit_remaining',
        'rate_limit_reset_at',
        'created_by',
        'metadata'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'last_used_at' => 'datetime',
        'expires_at' => 'datetime',
        'rate_limit_reset_at' => 'datetime',
        'metadata' => 'array'
    ];

    protected $hidden = [
        'encrypted_token'
    ];

    /**
     * Get the user who created this token
     */
    public function creator()
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * Set the token (encrypted)
     */
    public function setTokenAttribute($value)
    {
        $this->attributes['encrypted_token'] = Crypt::encryptString($value);
    }

    /**
     * Get the decrypted token
     */
    public function getTokenAttribute()
    {
        if (!$this->encrypted_token) {
            return null;
        }
        
        try {
            return Crypt::decryptString($this->encrypted_token);
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Check if token is valid and active
     */
    public function isValid(): bool
    {
        return $this->is_active &&
               (!$this->expires_at || $this->expires_at->isFuture()) &&
               !$this->trashed() &&
               $this->hasValidTokenFormat();
    }

    /**
     * Check if the token has a valid ClickUp format
     */
    public function hasValidTokenFormat(): bool
    {
        $token = $this->getTokenAttribute();

        if (empty($token)) {
            return false;
        }

        // ClickUp tokens can have different formats:
        // Personal API tokens: pk_XXXXXX_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
        // App tokens: Various formats
        // We'll be more flexible with validation

        // Check for personal API token format (most common)
        if (preg_match('/^pk_\d+_[A-Z0-9]{20,40}$/', $token) === 1) {
            return true;
        }

        // Check for other possible ClickUp token formats
        if (preg_match('/^[a-zA-Z0-9_-]{20,100}$/', $token) === 1) {
            return true;
        }

        return false;
    }

    /**
     * Check if rate limit is exceeded
     */
    public function isRateLimited(): bool
    {
        if (!$this->rate_limit_reset_at) {
            return false;
        }

        if ($this->rate_limit_reset_at->isPast()) {
            // Reset rate limit
            $this->update([
                'rate_limit_remaining' => null,
                'rate_limit_reset_at' => null
            ]);
            return false;
        }

        return $this->rate_limit_remaining !== null && $this->rate_limit_remaining <= 0;
    }

    /**
     * Update rate limit information
     */
    public function updateRateLimit(int $remaining, \DateTime $resetAt): void
    {
        $this->update([
            'rate_limit_remaining' => $remaining,
            'rate_limit_reset_at' => $resetAt,
            'last_used_at' => now()
        ]);
    }

    /**
     * Get the active token
     */
    public static function getActiveToken(): ?self
    {
        return self::where('is_active', true)
                   ->whereNull('deleted_at')
                   ->where(function ($query) {
                       $query->whereNull('expires_at')
                             ->orWhere('expires_at', '>', now());
                   })
                   ->first();
    }

    /**
     * Deactivate all other tokens when setting this as active
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($token) {
            if ($token->is_active) {
                // Deactivate all other tokens
                self::where('id', '!=', $token->id)
                    ->update(['is_active' => false]);
            }
        });
    }
}
