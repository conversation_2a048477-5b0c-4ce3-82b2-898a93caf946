<?php

namespace Plugins\ClickUp\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Plugins\business\Models\Contact;
use App\Models\User;

class ClickUpTeamMember extends Model
{
    use HasFactory;

    protected $table = 'clickup_team_members';

    protected $fillable = [
        'contact_id',
        'contact_name',
        'contact_arabic_name',
        'contact_email',
        'contact_position',
        'role',
        'is_active',
        'joined_at',
        'added_by',
        'assigned_lists',
        'permissions',
        'notes',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'joined_at' => 'datetime',
        'assigned_lists' => 'array',
        'permissions' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the contact associated with this team member
     */
    public function contact(): BelongsTo
    {
        return $this->belongsTo(Contact::class);
    }

    /**
     * Get the user who added this team member
     */
    public function addedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'added_by');
    }

    /**
     * Scope to get active team members
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get team members by role
     */
    public function scopeByRole($query, string $role)
    {
        return $query->where('role', $role);
    }

    /**
     * Scope to search team members
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function($q) use ($search) {
            $q->where('contact_name', 'like', "%{$search}%")
              ->orWhere('contact_arabic_name', 'like', "%{$search}%")
              ->orWhere('contact_email', 'like', "%{$search}%")
              ->orWhere('contact_position', 'like', "%{$search}%");
        });
    }

    /**
     * Add a ClickUp list to this team member's assignments
     */
    public function assignList(string $listId): void
    {
        $assignedLists = $this->assigned_lists ?? [];
        
        if (!in_array($listId, $assignedLists)) {
            $assignedLists[] = $listId;
            $this->update(['assigned_lists' => $assignedLists]);
        }
    }

    /**
     * Remove a ClickUp list from this team member's assignments
     */
    public function unassignList(string $listId): void
    {
        $assignedLists = $this->assigned_lists ?? [];
        
        if (($key = array_search($listId, $assignedLists)) !== false) {
            unset($assignedLists[$key]);
            $this->update(['assigned_lists' => array_values($assignedLists)]);
        }
    }

    /**
     * Check if this team member is assigned to a specific list
     */
    public function isAssignedToList(string $listId): bool
    {
        return in_array($listId, $this->assigned_lists ?? []);
    }

    /**
     * Get the count of assigned lists
     */
    public function getAssignedListsCountAttribute(): int
    {
        return count($this->assigned_lists ?? []);
    }

    /**
     * Create or update team member from contact
     */
    public static function createFromContact(Contact $contact, User $addedBy, string $role = 'member'): self
    {
        return static::updateOrCreate(
            ['contact_id' => $contact->id],
            [
                'contact_name' => $contact->name,
                'contact_arabic_name' => $contact->arabic_name,
                'contact_email' => $contact->email,
                'contact_position' => $contact->position,
                'role' => $role,
                'is_active' => true,
                'joined_at' => now(),
                'added_by' => $addedBy->id,
            ]
        );
    }

    /**
     * Sync contact data (useful when contact info is updated)
     */
    public function syncContactData(): void
    {
        if ($this->contact) {
            $this->update([
                'contact_name' => $this->contact->name,
                'contact_arabic_name' => $this->contact->arabic_name,
                'contact_email' => $this->contact->email,
                'contact_position' => $this->contact->position,
            ]);
        }
    }

    /**
     * Deactivate team member
     */
    public function deactivate(): void
    {
        $this->update(['is_active' => false]);
    }

    /**
     * Activate team member
     */
    public function activate(): void
    {
        $this->update(['is_active' => true]);
    }

    /**
     * Get display name (with Arabic if available)
     */
    public function getDisplayNameAttribute(): string
    {
        if ($this->contact_arabic_name && $this->contact_arabic_name !== $this->contact_name) {
            return "{$this->contact_name} ({$this->contact_arabic_name})";
        }
        
        return $this->contact_name;
    }

    /**
     * Get role display name
     */
    public function getRoleDisplayAttribute(): string
    {
        return match($this->role) {
            'member' => 'Team Member',
            'lead' => 'Team Lead',
            'manager' => 'Team Manager',
            default => ucfirst($this->role)
        };
    }
}
