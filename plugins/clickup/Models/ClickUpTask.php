<?php

namespace Plugins\ClickUp\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ClickUpTask extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'clickup_id',
        'list_id',
        'name',
        'description',
        'status',
        'priority',
        'assignees',
        'watchers',
        'tags',
        'due_date',
        'start_date',
        'date_created',
        'date_updated',
        'date_closed',
        'creator_id',
        'url',
        'parent_task_id',
        'order_index',
        'points',
        'time_estimate',
        'time_spent',
        'custom_fields',
        'attachments',
        'dependencies',
        'last_synced_at',
        'sync_status',
        'metadata'
    ];

    protected $casts = [
        'assignees' => 'array',
        'watchers' => 'array',
        'tags' => 'array',
        'due_date' => 'datetime',
        'start_date' => 'datetime',
        'date_created' => 'datetime',
        'date_updated' => 'datetime',
        'date_closed' => 'datetime',
        'custom_fields' => 'array',
        'attachments' => 'array',
        'dependencies' => 'array',
        'last_synced_at' => 'datetime',
        'metadata' => 'array'
    ];

    /**
     * Get the list this task belongs to
     */
    public function list()
    {
        return $this->belongsTo(ClickUpList::class, 'list_id');
    }

    /**
     * Get the product manager through the list
     */
    public function productManager()
    {
        return $this->hasOneThrough(
            ClickUpProductManager::class,
            ClickUpList::class,
            'id',
            'id',
            'list_id',
            'product_manager_id'
        );
    }

    /**
     * Get parent task
     */
    public function parentTask()
    {
        return $this->belongsTo(self::class, 'parent_task_id');
    }

    /**
     * Get subtasks
     */
    public function subtasks()
    {
        return $this->hasMany(self::class, 'parent_task_id');
    }

    /**
     * Check if task is overdue
     */
    public function getIsOverdueAttribute(): bool
    {
        return $this->due_date && 
               $this->due_date->isPast() && 
               !in_array($this->status, ['complete', 'closed']);
    }

    /**
     * Check if task is completed
     */
    public function getIsCompletedAttribute(): bool
    {
        return in_array($this->status, ['complete', 'closed']);
    }

    /**
     * Get task age in days
     */
    public function getAgeInDaysAttribute(): int
    {
        if (!$this->date_created) {
            return 0;
        }

        return \Carbon\Carbon::parse($this->date_created)->diffInDays(now());
    }

    /**
     * Get completion time in hours
     */
    public function getCompletionTimeAttribute(): ?float
    {
        if (!$this->date_created || !$this->date_closed) {
            return null;
        }

        $created = \Carbon\Carbon::parse($this->date_created);
        $closed = \Carbon\Carbon::parse($this->date_closed);
        
        return round($created->diffInHours($closed), 2);
    }

    /**
     * Get time remaining until due date
     */
    public function getTimeRemainingAttribute(): ?string
    {
        if (!$this->due_date || $this->is_completed) {
            return null;
        }

        $now = now();
        $due = $this->due_date;

        if ($due->isPast()) {
            return 'Overdue by ' . $due->diffForHumans($now, true);
        }

        return 'Due in ' . $now->diffForHumans($due, true);
    }

    /**
     * Get priority level as number for sorting
     */
    public function getPriorityLevelAttribute(): int
    {
        $priorities = [
            'urgent' => 4,
            'high' => 3,
            'normal' => 2,
            'low' => 1,
        ];

        return $priorities[$this->priority] ?? 0;
    }

    /**
     * Get assigned user names
     */
    public function getAssigneeNamesAttribute(): array
    {
        if (!$this->assignees) {
            return [];
        }

        return array_column($this->assignees, 'username');
    }

    /**
     * Get task progress percentage
     */
    public function getProgressPercentageAttribute(): int
    {
        if ($this->is_completed) {
            return 100;
        }

        // Calculate based on subtasks if any
        $subtasks = $this->subtasks;
        if ($subtasks->count() > 0) {
            $completedSubtasks = $subtasks->where('is_completed', true)->count();
            return round(($completedSubtasks / $subtasks->count()) * 100);
        }

        // Calculate based on status
        $statusProgress = [
            'open' => 0,
            'in progress' => 50,
            'review' => 80,
            'complete' => 100,
            'closed' => 100,
        ];

        return $statusProgress[strtolower($this->status)] ?? 0;
    }

    /**
     * Check if task needs sync
     */
    public function needsSync(): bool
    {
        if (!$this->last_synced_at) {
            return true;
        }

        // Sync if last sync was more than 30 minutes ago
        return $this->last_synced_at->lt(now()->subMinutes(30));
    }

    /**
     * Mark as synced
     */
    public function markAsSynced(): void
    {
        $this->update([
            'last_synced_at' => now(),
            'sync_status' => 'success'
        ]);
    }

    /**
     * Mark sync as failed
     */
    public function markSyncFailed(?string $error = null): void
    {
        $metadata = $this->metadata ?? [];
        if ($error) {
            $metadata['last_sync_error'] = $error;
        }

        $this->update([
            'sync_status' => 'failed',
            'metadata' => $metadata
        ]);
    }

    /**
     * Scope for active tasks
     */
    public function scopeActive($query)
    {
        return $query->whereNotIn('status', ['complete', 'closed']);
    }

    /**
     * Scope for completed tasks
     */
    public function scopeCompleted($query)
    {
        return $query->whereIn('status', ['complete', 'closed']);
    }

    /**
     * Scope for overdue tasks
     */
    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
                     ->whereNotIn('status', ['complete', 'closed']);
    }

    /**
     * Scope for high priority tasks
     */
    public function scopeHighPriority($query)
    {
        return $query->whereIn('priority', ['urgent', 'high']);
    }

    /**
     * Scope for tasks assigned to specific user
     */
    public function scopeAssignedTo($query, string $username)
    {
        return $query->whereJsonContains('assignees', [['username' => $username]]);
    }
}
