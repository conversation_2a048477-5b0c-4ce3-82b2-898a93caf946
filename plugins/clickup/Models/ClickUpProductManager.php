<?php

namespace Plugins\ClickUp\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ClickUpProductManager extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'clickup_user_id',
        'name',
        'email',
        'avatar_url',
        'is_active',
        'timezone',
        'role',
        'department',
        'hire_date',
        'performance_goals',
        'metadata',
        'created_by'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'hire_date' => 'date',
        'performance_goals' => 'array',
        'metadata' => 'array'
    ];

    /**
     * Get the associated user
     */
    public function user()
    {
        return $this->belongsTo(\App\Models\User::class);
    }

    /**
     * Get the user who created this product manager
     */
    public function creator()
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * Get the ClickUp lists assigned to this product manager
     */
    public function clickupLists()
    {
        return $this->hasMany(ClickUpList::class, 'product_manager_id');
    }

    /**
     * Get tasks through assigned lists
     */
    public function tasks()
    {
        return $this->hasManyThrough(
            ClickUpTask::class,
            ClickUpList::class,
            'product_manager_id',
            'list_id',
            'id',
            'id'
        );
    }

    /**
     * Get active tasks
     */
    public function activeTasks()
    {
        return $this->tasks()->whereNotIn('click_up_tasks.status', ['complete', 'closed']);
    }

    /**
     * Get completed tasks
     */
    public function completedTasks()
    {
        return $this->tasks()->whereIn('click_up_tasks.status', ['complete', 'closed']);
    }

    /**
     * Calculate completion rate
     */
    public function getCompletionRateAttribute(): float
    {
        $totalTasks = $this->tasks()->count();
        if ($totalTasks === 0) {
            return 0;
        }

        $completedTasks = $this->completedTasks()->count();
        return round(($completedTasks / $totalTasks) * 100, 2);
    }

    /**
     * Get average task completion time in hours
     */
    public function getAverageCompletionTimeAttribute(): ?float
    {
        $completedTasks = $this->completedTasks()
            ->whereNotNull('date_created')
            ->whereNotNull('date_closed')
            ->get();

        if ($completedTasks->isEmpty()) {
            return null;
        }

        $totalHours = 0;
        foreach ($completedTasks as $task) {
            $created = \Carbon\Carbon::parse($task->date_created);
            $closed = \Carbon\Carbon::parse($task->date_closed);
            $totalHours += $created->diffInHours($closed);
        }

        return round($totalHours / $completedTasks->count(), 2);
    }

    /**
     * Get productivity score (tasks completed per week)
     */
    public function getProductivityScoreAttribute(): float
    {
        $weeksActive = max(1, $this->created_at->diffInWeeks(now()));
        $completedTasks = $this->completedTasks()->count();
        
        return round($completedTasks / $weeksActive, 2);
    }

    /**
     * Get performance metrics
     */
    public function getPerformanceMetrics(): array
    {
        return [
            'total_lists' => $this->clickupLists()->count(),
            'total_tasks' => $this->tasks()->count(),
            'active_tasks' => $this->activeTasks()->count(),
            'completed_tasks' => $this->completedTasks()->count(),
            'completion_rate' => $this->completion_rate,
            'average_completion_time' => $this->average_completion_time,
            'productivity_score' => $this->productivity_score,
            'overdue_tasks' => $this->tasks()->where('click_up_tasks.due_date', '<', now())->whereNotIn('click_up_tasks.status', ['complete', 'closed'])->count(),
        ];
    }

    /**
     * Scope for active product managers
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for product managers with lists
     */
    public function scopeWithLists($query)
    {
        return $query->has('clickupLists');
    }
}
