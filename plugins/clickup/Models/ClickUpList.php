<?php

namespace Plugins\ClickUp\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ClickUpList extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'clickup_id',
        'name',
        'description',
        'folder_id',
        'space_id',
        'team_id',
        'product_manager_id',
        'status',
        'priority',
        'color',
        'avatar_url',
        'is_private',
        'is_archived',
        'task_count',
        'due_date',
        'start_date',
        'permission_level',
        'last_synced_at',
        'sync_status',
        'metadata',
        'created_by'
    ];

    protected $casts = [
        'is_private' => 'boolean',
        'is_archived' => 'boolean',
        'due_date' => 'datetime',
        'start_date' => 'datetime',
        'last_synced_at' => 'datetime',
        'metadata' => 'array'
    ];

    /**
     * Get the assigned product manager
     */
    public function productManager()
    {
        return $this->belongsTo(ClickUpProductManager::class, 'product_manager_id');
    }

    /**
     * Get the user who created this list record
     */
    public function creator()
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * Get the tasks in this list
     */
    public function tasks()
    {
        return $this->hasMany(ClickUpTask::class, 'list_id');
    }

    /**
     * Get active tasks
     */
    public function activeTasks()
    {
        return $this->tasks()->whereNotIn('click_up_tasks.status', ['complete', 'closed']);
    }

    /**
     * Get completed tasks
     */
    public function completedTasks()
    {
        return $this->tasks()->whereIn('click_up_tasks.status', ['complete', 'closed']);
    }

    /**
     * Get overdue tasks
     */
    public function overdueTasks()
    {
        return $this->tasks()
            ->where('click_up_tasks.due_date', '<', now())
            ->whereNotIn('click_up_tasks.status', ['complete', 'closed']);
    }

    /**
     * Get high priority tasks
     */
    public function highPriorityTasks()
    {
        return $this->tasks()->whereIn('priority', ['urgent', 'high']);
    }

    /**
     * Calculate completion rate
     */
    public function getCompletionRateAttribute(): float
    {
        $totalTasks = $this->tasks()->count();
        if ($totalTasks === 0) {
            return 0;
        }

        $completedTasks = $this->completedTasks()->count();
        return round(($completedTasks / $totalTasks) * 100, 2);
    }

    /**
     * Get list performance metrics
     */
    public function getPerformanceMetrics(): array
    {
        return [
            'total_tasks' => $this->tasks()->count(),
            'active_tasks' => $this->activeTasks()->count(),
            'completed_tasks' => $this->completedTasks()->count(),
            'overdue_tasks' => $this->overdueTasks()->count(),
            'high_priority_tasks' => $this->highPriorityTasks()->count(),
            'completion_rate' => $this->completion_rate,
            'average_task_age' => $this->getAverageTaskAge(),
            'tasks_completed_this_week' => $this->getTasksCompletedThisWeek(),
            'tasks_completed_this_month' => $this->getTasksCompletedThisMonth(),
        ];
    }

    /**
     * Get average age of active tasks in days
     */
    public function getAverageTaskAge(): float
    {
        $activeTasks = $this->activeTasks()
            ->whereNotNull('date_created')
            ->get();

        if ($activeTasks->isEmpty()) {
            return 0;
        }

        $totalDays = 0;
        foreach ($activeTasks as $task) {
            $created = \Carbon\Carbon::parse($task->date_created);
            $totalDays += $created->diffInDays(now());
        }

        return round($totalDays / $activeTasks->count(), 1);
    }

    /**
     * Get tasks completed this week
     */
    public function getTasksCompletedThisWeek(): int
    {
        return $this->completedTasks()
            ->where('click_up_tasks.date_closed', '>=', now()->startOfWeek())
            ->count();
    }

    /**
     * Get tasks completed this month
     */
    public function getTasksCompletedThisMonth(): int
    {
        return $this->completedTasks()
            ->where('click_up_tasks.date_closed', '>=', now()->startOfMonth())
            ->count();
    }

    /**
     * Check if list needs sync
     */
    public function needsSync(): bool
    {
        if (!$this->last_synced_at) {
            return true;
        }

        // Sync if last sync was more than 1 hour ago
        return $this->last_synced_at->lt(now()->subHour());
    }

    /**
     * Mark as synced
     */
    public function markAsSynced(): void
    {
        $this->update([
            'last_synced_at' => now(),
            'sync_status' => 'success'
        ]);
    }

    /**
     * Mark sync as failed
     */
    public function markSyncFailed(?string $error = null): void
    {
        $metadata = $this->metadata ?? [];
        if ($error) {
            $metadata['last_sync_error'] = $error;
        }

        $this->update([
            'sync_status' => 'failed',
            'metadata' => $metadata
        ]);
    }

    /**
     * Scope for active lists
     */
    public function scopeActive($query)
    {
        return $query->where('is_archived', false);
    }

    /**
     * Scope for lists with assigned product managers
     */
    public function scopeWithManager($query)
    {
        return $query->whereNotNull('product_manager_id');
    }

    /**
     * Scope for lists that need sync
     */
    public function scopeNeedsSync($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('last_synced_at')
              ->orWhere('last_synced_at', '<', now()->subHour());
        });
    }
}
