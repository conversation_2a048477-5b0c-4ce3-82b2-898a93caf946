<?php

namespace Plugins\ClickUp\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ClickUpListAssignment extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'clickup_list_assignments';

    protected $fillable = [
        'clickup_list_id',
        'clickup_list_name',
        'clickup_space_id',
        'clickup_space_name',
        'clickup_workspace_id',
        'assigned_to_contact_id',
        'assigned_to_contact_name',
        'assigned_by_user_id',
        'assigned_at',
        'metadata',
        'is_active'
    ];

    protected $casts = [
        'assigned_at' => 'datetime',
        'metadata' => 'array',
        'is_active' => 'boolean'
    ];

    /**
     * Get the user who made the assignment
     */
    public function assignedByUser()
    {
        return $this->belongsTo(\App\Models\User::class, 'assigned_by_user_id');
    }

    /**
     * Get the assigned contact from business contacts system
     */
    public function assignedToContact()
    {
        return $this->belongsTo(\Plugins\business\Models\Contact::class, 'assigned_to_contact_id');
    }

    /**
     * Scope to get active assignments
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get assignments for a specific space
     */
    public function scopeForSpace($query, $spaceId)
    {
        return $query->where('clickup_space_id', $spaceId);
    }

    /**
     * Scope to get assignments for a specific contact
     */
    public function scopeForContact($query, $contactId)
    {
        return $query->where('assigned_to_contact_id', $contactId);
    }

    /**
     * Get assignment by ClickUp list ID
     */
    public static function getByListId($listId)
    {
        return static::where('clickup_list_id', $listId)->active()->first();
    }

    /**
     * Assign a list to a contact
     */
    public static function assignList($listData, $contactId, $contactName, $userId)
    {
        return static::updateOrCreate(
            ['clickup_list_id' => $listData['id']],
            [
                'clickup_list_name' => $listData['name'],
                'clickup_space_id' => $listData['space']['id'] ?? null,
                'clickup_space_name' => $listData['space']['name'] ?? null,
                'clickup_workspace_id' => $listData['space']['workspace_id'] ?? null,
                'assigned_to_contact_id' => $contactId,
                'assigned_to_contact_name' => $contactName,
                'assigned_by_user_id' => $userId,
                'assigned_at' => now(),
                'metadata' => [
                    'list_data' => $listData,
                    'assigned_at' => now()->toISOString()
                ],
                'is_active' => true
            ]
        );
    }

    /**
     * Unassign a list
     */
    public static function unassignList($listId)
    {
        return static::where('clickup_list_id', $listId)->update(['is_active' => false]);
    }

    /**
     * Get all assignments grouped by space
     */
    public static function getAssignmentsBySpace()
    {
        return static::active()
            ->orderBy('clickup_space_name')
            ->orderBy('clickup_list_name')
            ->get()
            ->groupBy('clickup_space_name');
    }

    /**
     * Get assignment statistics
     */
    public static function getAssignmentStats()
    {
        return [
            'total_assignments' => static::active()->count(),
            'unique_contacts' => static::active()->distinct('assigned_to_contact_id')->count('assigned_to_contact_id'),
            'unique_spaces' => static::active()->distinct('clickup_space_id')->count('clickup_space_id'),
            'recent_assignments' => static::active()->where('assigned_at', '>=', now()->subDays(7))->count()
        ];
    }
}
