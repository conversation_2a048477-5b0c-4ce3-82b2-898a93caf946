@extends('layouts.app')

@section('title', 'Bugs & Features Report')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Bugs & Features Report</h1>
        <div class="flex space-x-3">
            <a href="{{ route('clickup.reports.index') }}"
               class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-chart-bar mr-2"></i>
                All Reports
            </a>
            <a href="{{ route('clickup.dashboard') }}"
               class="bg-gray-600 dark:bg-gray-700 hover:bg-gray-700 dark:hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6 border border-gray-200 dark:border-gray-700">
        <form method="GET" action="{{ route('clickup.reports.bugs-features') }}">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="manager_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Product Manager</label>
                    <select name="manager_id" id="manager_id"
                            class="w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Managers</option>
                        @foreach($productManagers as $manager)
                            <option value="{{ $manager->id }}" {{ request('manager_id') == $manager->id ? 'selected' : '' }}>
                                {{ $manager->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div>
                    <label for="date_from" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">From Date</label>
                    <input type="date" name="date_from" id="date_from" value="{{ request('date_from', now()->subMonth()->format('Y-m-d')) }}"
                           class="w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label for="date_to" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">To Date</label>
                    <input type="date" name="date_to" id="date_to" value="{{ request('date_to', now()->format('Y-m-d')) }}"
                           class="w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div class="flex items-end">
                    <button type="submit" 
                            class="w-full bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-filter mr-2"></i>
                        Apply Filters
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Overall Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-bug text-red-500 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Total Bugs</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ number_format($bugsAndFeaturesData['overall_stats']['total_bugs_reported']) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-green-500 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Bugs Solved</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ number_format($bugsAndFeaturesData['overall_stats']['total_bugs_solved']) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-star text-blue-500 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Features Requested</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ number_format($bugsAndFeaturesData['overall_stats']['total_features_requested']) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-rocket text-purple-500 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Features Completed</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ number_format($bugsAndFeaturesData['overall_stats']['total_features_completed']) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Manager Reports -->
    <div class="space-y-8">
        @forelse($bugsAndFeaturesData['managers'] as $managerData)
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex justify-between items-center">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ $managerData['manager']['name'] }}</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                {{ $managerData['manager']['role'] }} • {{ $managerData['manager']['department'] }} • {{ $managerData['lists_count'] }} Lists
                            </p>
                        </div>
                        <div class="flex space-x-4 text-sm">
                            <div class="text-center">
                                <div class="text-lg font-bold text-red-600 dark:text-red-400">{{ $managerData['summary']['resolution_rate'] }}%</div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">Bug Resolution</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-bold text-blue-600 dark:text-blue-400">{{ $managerData['summary']['completion_rate'] }}%</div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">Feature Completion</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Bugs Section -->
                        <div>
                            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                                <i class="fas fa-bug text-red-500 mr-2"></i>
                                Bug Reports
                            </h4>
                            <div class="grid grid-cols-2 gap-4 mb-4">
                                <div class="bg-red-50 dark:bg-red-900 p-3 rounded-lg">
                                    <div class="text-lg font-bold text-red-600 dark:text-red-400">{{ $managerData['bugs']['total_reported'] }}</div>
                                    <div class="text-xs text-red-500 dark:text-red-300">Total Reported</div>
                                </div>
                                <div class="bg-green-50 dark:bg-green-900 p-3 rounded-lg">
                                    <div class="text-lg font-bold text-green-600 dark:text-green-400">{{ $managerData['bugs']['total_solved'] }}</div>
                                    <div class="text-xs text-green-500 dark:text-green-300">Solved</div>
                                </div>
                                <div class="bg-yellow-50 dark:bg-yellow-900 p-3 rounded-lg">
                                    <div class="text-lg font-bold text-yellow-600 dark:text-yellow-400">{{ $managerData['bugs']['in_progress'] }}</div>
                                    <div class="text-xs text-yellow-500 dark:text-yellow-300">In Progress</div>
                                </div>
                                <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                                    <div class="text-lg font-bold text-gray-600 dark:text-gray-400">{{ $managerData['bugs']['pending'] }}</div>
                                    <div class="text-xs text-gray-500 dark:text-gray-300">Pending</div>
                                </div>
                            </div>
                            @if($managerData['bugs']['average_resolution_time'])
                                <div class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                                    <i class="fas fa-clock mr-1"></i>
                                    Avg. Resolution Time: {{ $managerData['bugs']['average_resolution_time'] }} hours
                                </div>
                            @endif
                            @if(count($managerData['bugs']['recent_bugs']) > 0)
                                <div>
                                    <h5 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Recent Bugs</h5>
                                    <div class="space-y-2">
                                        @foreach($managerData['bugs']['recent_bugs'] as $bug)
                                            <div class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded">
                                                <div class="flex-1 min-w-0">
                                                    <div class="text-sm font-medium text-gray-900 dark:text-white truncate">{{ $bug['name'] }}</div>
                                                    <div class="text-xs text-gray-500 dark:text-gray-400">
                                                        {{ ucfirst($bug['status']) }} • {{ ucfirst($bug['priority']) ?? 'Normal' }}
                                                    </div>
                                                </div>
                                                @if($bug['url'])
                                                    <a href="{{ $bug['url'] }}" target="_blank" class="text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300">
                                                        <i class="fas fa-external-link-alt"></i>
                                                    </a>
                                                @endif
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif
                        </div>

                        <!-- Features Section -->
                        <div>
                            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                                <i class="fas fa-star text-blue-500 mr-2"></i>
                                Feature Requests
                            </h4>
                            <div class="grid grid-cols-2 gap-4 mb-4">
                                <div class="bg-blue-50 dark:bg-blue-900 p-3 rounded-lg">
                                    <div class="text-lg font-bold text-blue-600 dark:text-blue-400">{{ $managerData['features']['total_requested'] }}</div>
                                    <div class="text-xs text-blue-500 dark:text-blue-300">Total Requested</div>
                                </div>
                                <div class="bg-green-50 dark:bg-green-900 p-3 rounded-lg">
                                    <div class="text-lg font-bold text-green-600 dark:text-green-400">{{ $managerData['features']['total_completed'] }}</div>
                                    <div class="text-xs text-green-500 dark:text-green-300">Completed</div>
                                </div>
                                <div class="bg-purple-50 dark:bg-purple-900 p-3 rounded-lg">
                                    <div class="text-lg font-bold text-purple-600 dark:text-purple-400">{{ $managerData['features']['in_development'] }}</div>
                                    <div class="text-xs text-purple-500 dark:text-purple-300">In Development</div>
                                </div>
                                <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                                    <div class="text-lg font-bold text-gray-600 dark:text-gray-400">{{ $managerData['features']['pending_approval'] }}</div>
                                    <div class="text-xs text-gray-500 dark:text-gray-300">Pending</div>
                                </div>
                            </div>
                            @if($managerData['features']['average_development_time'])
                                <div class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                                    <i class="fas fa-clock mr-1"></i>
                                    Avg. Development Time: {{ $managerData['features']['average_development_time'] }} hours
                                </div>
                            @endif
                            @if(count($managerData['features']['recent_features']) > 0)
                                <div>
                                    <h5 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Recent Features</h5>
                                    <div class="space-y-2">
                                        @foreach($managerData['features']['recent_features'] as $feature)
                                            <div class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded">
                                                <div class="flex-1 min-w-0">
                                                    <div class="text-sm font-medium text-gray-900 dark:text-white truncate">{{ $feature['name'] }}</div>
                                                    <div class="text-xs text-gray-500 dark:text-gray-400">
                                                        {{ ucfirst($feature['status']) }} • {{ ucfirst($feature['priority']) ?? 'Normal' }}
                                                    </div>
                                                </div>
                                                @if($feature['url'])
                                                    <a href="{{ $feature['url'] }}" target="_blank" class="text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300">
                                                        <i class="fas fa-external-link-alt"></i>
                                                    </a>
                                                @endif
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        @empty
            <div class="text-center py-12">
                <i class="fas fa-chart-bar text-gray-400 text-6xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Data Available</h3>
                <p class="text-gray-500 dark:text-gray-400">No product managers with lists found for the selected criteria.</p>
            </div>
        @endforelse
    </div>
</div>
@endsection
