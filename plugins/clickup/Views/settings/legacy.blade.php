@extends('layouts.app')

@section('title', 'ClickUp Settings - Legacy Interface')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">ClickUp Settings - Legacy Interface</h1>
        <div class="flex space-x-3">
            <a href="{{ route('clickup.settings.index') }}"
               class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-arrow-up mr-2"></i>
                New Interface
            </a>
            <a href="{{ route('clickup.settings.sync') }}"
               class="bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-sync mr-2"></i>
                Sync Settings
            </a>
            <a href="{{ route('clickup.settings.general') }}"
               class="bg-purple-600 dark:bg-purple-700 hover:bg-purple-700 dark:hover:bg-purple-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-sliders-h mr-2"></i>
                General Settings
            </a>
            <a href="{{ route('clickup.settings.assignments') }}"
               class="bg-orange-600 dark:bg-orange-700 hover:bg-orange-700 dark:hover:bg-orange-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-user-cog mr-2"></i>
                List Assignments
            </a>
            <a href="{{ route('clickup.dashboard') }}"
               class="bg-gray-600 dark:bg-gray-700 hover:bg-gray-700 dark:hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-md p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-check-circle text-green-400"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-green-700 dark:text-green-300">{{ session('success') }}</p>
                </div>
            </div>
        </div>
    @endif

    @if($errors->any())
        <div class="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-circle text-red-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800 dark:text-red-200">There were errors with your submission:</h3>
                    <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                        <ul class="list-disc pl-5 space-y-1">
                            @foreach($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Workspace Selection -->
    @if($currentToken)
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700 mb-8">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Workspace & Space Selection</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Select which ClickUp workspace and spaces to sync data from.</p>
            </div>
            <div class="p-6">
                <form method="POST" action="{{ route('clickup.settings.update-workspace') }}" class="space-y-6">
                    @csrf

                    <!-- Workspace Selection -->
                    <div>
                        <label for="workspace_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            ClickUp Workspace
                        </label>
                        <select name="workspace_id" id="workspace_id" required
                                class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                onchange="this.form.submit()">
                            <option value="">Select a workspace...</option>
                            @foreach($workspaces as $workspace)
                                <option value="{{ $workspace['id'] }}" {{ $selectedWorkspace === $workspace['id'] ? 'selected' : '' }}>
                                    {{ $workspace['name'] }}
                                </option>
                            @endforeach
                        </select>
                        @error('workspace_id')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        @if(empty($workspaces))
                            <p class="mt-1 text-sm text-yellow-600 dark:text-yellow-400">
                                <i class="fas fa-exclamation-triangle mr-1"></i>
                                No workspaces found. Please check your API token.
                            </p>
                        @endif
                    </div>

                    <!-- Space Selection -->
                    @if($selectedWorkspace && !empty($spaces))
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                                ClickUp Spaces (Optional - leave empty to sync all spaces)
                            </label>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-48 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md p-4">
                                @foreach($spaces as $space)
                                    <div class="flex items-center">
                                        <input type="checkbox" name="space_ids[]" value="{{ $space['id'] }}" id="space_{{ $space['id'] }}"
                                               {{ in_array($space['id'], $selectedSpaces ?? []) ? 'checked' : '' }}
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                                        <label for="space_{{ $space['id'] }}" class="ml-2 block text-sm text-gray-900 dark:text-white">
                                            {{ $space['name'] }}
                                        </label>
                                    </div>
                                @endforeach
                            </div>
                            @error('space_ids')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="flex justify-end">
                            <button type="submit"
                                    class="bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                                <i class="fas fa-save mr-2"></i>
                                Save Workspace Settings
                            </button>
                        </div>
                    @elseif($selectedWorkspace && empty($spaces))
                        <div class="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-md p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                                        No Spaces Found
                                    </h3>
                                    <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                                        <p>No spaces were found in the selected workspace. This might be normal if your workspace doesn't use spaces.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </form>
            </div>
        </div>
    @endif

    <!-- API Token Configuration -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700 mb-8">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">API Token Configuration</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Configure your ClickUp API token to enable data synchronization.</p>
        </div>
        <div class="p-6">
            @if($currentToken)
                <!-- Current Token Info -->
                <div class="bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-md p-4 mb-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-green-800 dark:text-green-200">
                                API Token Configured
                            </h3>
                            <div class="mt-2 text-sm text-green-700 dark:text-green-300">
                                <p>Token Name: {{ $currentToken->name }}</p>
                                <p>Created: {{ $currentToken->created_at->format('M d, Y H:i') }}</p>
                                @if($currentToken->last_used_at)
                                    <p>Last Used: {{ $currentToken->last_used_at->diffForHumans() }}</p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Test Connection -->
                <div class="mb-6">
                    <button id="test-connection-btn"
                            class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-plug mr-2"></i>
                        Test Connection
                    </button>
                </div>

                <!-- Replace Token Form -->
                <form method="POST" action="{{ route('clickup.settings.save-token') }}" class="space-y-6">
                    @csrf
                    <div>
                        <label for="replace_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Token Name</label>
                        <input type="text" name="name" id="replace_name" value="{{ old('name', 'Default Token') }}"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        @error('name')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="replace_token" class="block text-sm font-medium text-gray-700 dark:text-gray-300">New API Token</label>
                        <input type="text" name="token" id="replace_token" placeholder="pk_707692_S2PIZXBJGVMVWOVZ5BGZ1U8LMNX0FONP"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                            Get your API token from ClickUp Settings → Apps → API Token
                        </p>
                        @error('token')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="flex space-x-3">
                        <button type="submit"
                                class="bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                            <i class="fas fa-save mr-2"></i>
                            Update Token
                        </button>

                        <button type="button" id="delete-token-btn"
                                class="bg-red-600 dark:bg-red-700 hover:bg-red-700 dark:hover:bg-red-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                            <i class="fas fa-trash mr-2"></i>
                            Delete Token
                        </button>
                    </div>
                </form>
            @else
                <!-- No Token Configured -->
                <div class="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-md p-4 mb-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                                No API Token Configured
                            </h3>
                            <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                                <p>Please configure your ClickUp API token to enable data synchronization.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Add Token Form -->
                <form method="POST" action="{{ route('clickup.settings.save-token') }}" class="space-y-6">
                    @csrf
                    <div>
                        <label for="add_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Token Name</label>
                        <input type="text" name="name" id="add_name" value="{{ old('name', 'Default Token') }}"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        @error('name')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="add_token" class="block text-sm font-medium text-gray-700 dark:text-gray-300">ClickUp API Token</label>
                        <input type="text" name="token" id="add_token" placeholder="pk_707692_S2PIZXBJGVMVWOVZ5BGZ1U8LMNX0FONP"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                            Get your API token from ClickUp Settings → Apps → API Token
                        </p>
                        @error('token')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <button type="submit"
                            class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-save mr-2"></i>
                        Save Token
                    </button>
                </form>
            @endif
        </div>
    </div>

    <!-- API Status -->
    @if($apiStatus['configured'])
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">API Status</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Rate Limit Status</h4>
                        @if($apiStatus['rate_limit_remaining'] !== null)
                            <p class="text-sm text-gray-900 dark:text-white">
                                Remaining: {{ $apiStatus['rate_limit_remaining'] }} requests
                            </p>
                            @if($apiStatus['rate_limit_reset_at'])
                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                    Resets: {{ \Carbon\Carbon::parse($apiStatus['rate_limit_reset_at'])->diffForHumans() }}
                                </p>
                            @endif
                        @else
                            <p class="text-sm text-gray-500 dark:text-gray-400">No rate limit data available</p>
                        @endif
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Last Activity</h4>
                        @if($apiStatus['last_used_at'])
                            <p class="text-sm text-gray-900 dark:text-white">
                                {{ \Carbon\Carbon::parse($apiStatus['last_used_at'])->diffForHumans() }}
                            </p>
                        @else
                            <p class="text-sm text-gray-500 dark:text-gray-400">Never used</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>

<!-- Delete Token Modal -->
<div id="delete-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900">
                <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mt-2">Delete API Token</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500 dark:text-gray-400">
                    Are you sure you want to delete the API token? This will disable ClickUp integration.
                </p>
            </div>
            <div class="items-center px-4 py-3">
                <form method="POST" action="{{ route('clickup.settings.delete-token') }}" class="inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit"
                            class="px-4 py-2 bg-red-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-300 mr-2">
                        Delete
                    </button>
                </form>
                <button id="cancel-delete"
                        class="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Test connection
    const testBtn = document.getElementById('test-connection-btn');
    if (testBtn) {
        testBtn.addEventListener('click', function() {
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Testing...';

            fetch('{{ route("clickup.settings.test-connection") }}', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Connection successful!');
                } else {
                    alert('Connection failed: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Connection test failed. Please try again.');
            })
            .finally(() => {
                this.disabled = false;
                this.innerHTML = '<i class="fas fa-plug mr-2"></i>Test Connection';
            });
        });
    }

    // Delete token modal
    const deleteBtn = document.getElementById('delete-token-btn');
    const deleteModal = document.getElementById('delete-modal');
    const cancelBtn = document.getElementById('cancel-delete');

    if (deleteBtn) {
        deleteBtn.addEventListener('click', function() {
            deleteModal.classList.remove('hidden');
        });
    }

    if (cancelBtn) {
        cancelBtn.addEventListener('click', function() {
            deleteModal.classList.add('hidden');
        });
    }

    // Close modal on outside click
    if (deleteModal) {
        deleteModal.addEventListener('click', function(e) {
            if (e.target === deleteModal) {
                deleteModal.classList.add('hidden');
            }
        });
    }
});
</script>
@endpush
@endsection
