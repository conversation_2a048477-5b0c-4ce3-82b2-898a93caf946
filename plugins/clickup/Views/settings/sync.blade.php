@extends('layouts.app')

@section('title', 'ClickUp Sync Settings')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Sync Settings</h1>
        <div class="flex space-x-3">
            <a href="{{ route('clickup.settings.index') }}"
               class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-key mr-2"></i>
                API Settings
            </a>
            <a href="{{ route('clickup.settings.general') }}"
               class="bg-purple-600 dark:bg-purple-700 hover:bg-purple-700 dark:hover:bg-purple-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-sliders-h mr-2"></i>
                General Settings
            </a>
            <a href="{{ route('clickup.dashboard') }}"
               class="bg-gray-600 dark:bg-gray-700 hover:bg-gray-700 dark:hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Dashboard
            </a>
        </div>
    </div>

    @if(!$currentToken)
        <div class="bg-yellow-100 dark:bg-yellow-900 border-l-4 border-yellow-500 text-yellow-700 dark:text-yellow-300 p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-yellow-500"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm">
                        No API token configured. Please configure your API token first.
                        <a href="{{ route('clickup.settings.index') }}" class="font-medium underline hover:text-yellow-600 dark:hover:text-yellow-200">
                            Configure API Token
                        </a>
                    </p>
                </div>
            </div>
        </div>
    @endif

    <!-- Sync Settings Form -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700 mb-8">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Synchronization Configuration</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Configure how and when data is synchronized from ClickUp.</p>
        </div>
        <div class="p-6">
            <form method="POST" action="{{ route('clickup.settings.update-sync') }}" class="space-y-6">
                @csrf

                <!-- Sync Interval -->
                <div>
                    <label for="sync_interval" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Sync Interval (seconds)
                    </label>
                    <select name="sync_interval" id="sync_interval"
                            class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        <option value="300" {{ $syncSettings['sync_interval'] == 300 ? 'selected' : '' }}>5 minutes</option>
                        <option value="900" {{ $syncSettings['sync_interval'] == 900 ? 'selected' : '' }}>15 minutes</option>
                        <option value="1800" {{ $syncSettings['sync_interval'] == 1800 ? 'selected' : '' }}>30 minutes</option>
                        <option value="3600" {{ $syncSettings['sync_interval'] == 3600 ? 'selected' : '' }}>1 hour</option>
                        <option value="7200" {{ $syncSettings['sync_interval'] == 7200 ? 'selected' : '' }}>2 hours</option>
                        <option value="21600" {{ $syncSettings['sync_interval'] == 21600 ? 'selected' : '' }}>6 hours</option>
                        <option value="43200" {{ $syncSettings['sync_interval'] == 43200 ? 'selected' : '' }}>12 hours</option>
                        <option value="86400" {{ $syncSettings['sync_interval'] == 86400 ? 'selected' : '' }}>24 hours</option>
                    </select>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        How often to automatically sync data from ClickUp
                    </p>
                    @error('sync_interval')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Batch Size -->
                <div>
                    <label for="batch_size" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Batch Size
                    </label>
                    <select name="batch_size" id="batch_size"
                            class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        <option value="10" {{ $syncSettings['batch_size'] == 10 ? 'selected' : '' }}>10 items</option>
                        <option value="25" {{ $syncSettings['batch_size'] == 25 ? 'selected' : '' }}>25 items</option>
                        <option value="50" {{ $syncSettings['batch_size'] == 50 ? 'selected' : '' }}>50 items</option>
                        <option value="100" {{ $syncSettings['batch_size'] == 100 ? 'selected' : '' }}>100 items</option>
                        <option value="200" {{ $syncSettings['batch_size'] == 200 ? 'selected' : '' }}>200 items</option>
                        <option value="500" {{ $syncSettings['batch_size'] == 500 ? 'selected' : '' }}>500 items</option>
                    </select>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        Number of items to process in each sync batch
                    </p>
                    @error('batch_size')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Auto Sync -->
                <div class="flex items-center">
                    <input type="checkbox" name="auto_sync" id="auto_sync" value="1" 
                           {{ $syncSettings['auto_sync'] ? 'checked' : '' }}
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                    <label for="auto_sync" class="ml-2 block text-sm text-gray-900 dark:text-white">
                        Enable automatic synchronization
                    </label>
                </div>
                <p class="text-sm text-gray-500 dark:text-gray-400 ml-6">
                    When enabled, data will be automatically synced at the specified interval
                </p>

                <!-- Sync on Startup -->
                <div class="flex items-center">
                    <input type="checkbox" name="sync_on_startup" id="sync_on_startup" value="1" 
                           {{ $syncSettings['sync_on_startup'] ? 'checked' : '' }}
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                    <label for="sync_on_startup" class="ml-2 block text-sm text-gray-900 dark:text-white">
                        Sync data on application startup
                    </label>
                </div>
                <p class="text-sm text-gray-500 dark:text-gray-400 ml-6">
                    Automatically sync data when the application starts
                </p>

                <!-- Submit Button -->
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="resetToDefaults()" 
                            class="bg-gray-600 dark:bg-gray-700 hover:bg-gray-700 dark:hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Reset to Defaults
                    </button>
                    <button type="submit" 
                            class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-save mr-2"></i>
                        Save Sync Settings
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Manual Sync Actions -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Manual Sync Actions</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Manually trigger synchronization processes.</p>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button id="sync-lists-btn" 
                        class="bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-bold py-3 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-list mr-2"></i>
                    Sync Lists
                </button>
                <button id="sync-tasks-btn" 
                        class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-3 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-tasks mr-2"></i>
                    Sync Tasks
                </button>
                <button id="sync-all-btn" 
                        class="bg-purple-600 dark:bg-purple-700 hover:bg-purple-700 dark:hover:bg-purple-600 text-white font-bold py-3 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-sync mr-2"></i>
                    Sync All Data
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Reset to defaults
    window.resetToDefaults = function() {
        document.getElementById('sync_interval').value = '3600';
        document.getElementById('batch_size').value = '100';
        document.getElementById('auto_sync').checked = false;
        document.getElementById('sync_on_startup').checked = true;
    };

    // Manual sync functions
    function performSync(endpoint, buttonId, successMessage) {
        const button = document.getElementById(buttonId);
        const originalText = button.innerHTML;
        
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Syncing...';
        
        fetch(endpoint, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(successMessage);
            } else {
                alert('Sync failed: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Sync failed. Please try again.');
        })
        .finally(() => {
            button.disabled = false;
            button.innerHTML = originalText;
        });
    }

    document.getElementById('sync-lists-btn').addEventListener('click', function() {
        performSync('{{ route("clickup.sync.lists") }}', 'sync-lists-btn', 'Lists synced successfully!');
    });

    document.getElementById('sync-tasks-btn').addEventListener('click', function() {
        performSync('{{ route("clickup.sync.tasks") }}', 'sync-tasks-btn', 'Tasks synced successfully!');
    });

    document.getElementById('sync-all-btn').addEventListener('click', function() {
        performSync('{{ route("clickup.sync.all") }}', 'sync-all-btn', 'All data synced successfully!');
    });
});
</script>
@endpush
@endsection
