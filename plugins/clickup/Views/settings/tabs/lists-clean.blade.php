<style>
    .table-row-assigned {
        background-color: #f0fdf4;
        border-left: 4px solid #16a34a;
    }

    .dark .table-row-assigned {
        background-color: #14532d;
        border-left: 4px solid #22c55e;
    }

    .table-row:hover {
        background-color: #f9fafb;
    }

    .dark .table-row:hover {
        background-color: #374151;
    }

    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
    }

    .dark .loading-overlay {
        background: rgba(31, 41, 55, 0.8);
    }

    .success-message, .error-message {
        animation: slideIn 0.3s ease-out;
    }

    @keyframes slideIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>

<div class="space-y-6">
    @if(empty($currentToken))
        <!-- No API Token -->
        <div class="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-md p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">No API Token Configured</h3>
                    <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                        <p>Please configure your ClickUp API token first to manage lists.</p>
                    </div>
                </div>
            </div>
        </div>
    @else
        <!-- Lists Management Header -->
        <div class="mb-8">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Lists Management</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400 mb-6">Manage ClickUp lists and assign them to contacts from your business directory.</p>

            <!-- Statistics Dashboard -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div class="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-list text-blue-600 dark:text-blue-400"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-blue-900 dark:text-blue-100">Total Lists</p>
                            <p class="text-lg font-semibold text-blue-600 dark:text-blue-400" id="total-lists-count">{{ $lists ? $lists->count() : 0 }}</p>
                        </div>
                    </div>
                </div>
                <div class="bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-user-check text-green-600 dark:text-green-400"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-green-900 dark:text-green-100">Assigned</p>
                            <p class="text-lg font-semibold text-green-600 dark:text-green-400" id="assigned-lists-count">{{ count($assignedLists) }}</p>
                        </div>
                    </div>
                </div>
                <div class="bg-orange-50 dark:bg-orange-900 border border-orange-200 dark:border-orange-700 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-user-times text-orange-600 dark:text-orange-400"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-orange-900 dark:text-orange-100">Unassigned</p>
                            <p class="text-lg font-semibold text-orange-600 dark:text-orange-400" id="unassigned-lists-count">{{ ($lists ? $lists->count() : 0) - count($assignedLists) }}</p>
                        </div>
                    </div>
                </div>
                <div class="bg-purple-50 dark:bg-purple-900 border border-purple-200 dark:border-purple-700 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-users text-purple-600 dark:text-purple-400"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-purple-900 dark:text-purple-100">Contacts</p>
                            <p class="text-lg font-semibold text-purple-600 dark:text-purple-400" id="unique-contacts-count">{{ collect($assignedLists)->pluck('assigned_to_contact_id')->unique()->count() }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @if(!empty($lists))
            <!-- Search and Filter Controls -->
            <div class="mb-6">
                <div class="flex flex-col sm:flex-row gap-4">
                    <div class="flex-1">
                        <input type="text" id="list-search" placeholder="Search lists by name, space, or workspace..."
                               class="w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div class="flex space-x-2">
                        <select id="space-filter" class="border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                            <option value="">All Spaces</option>
                            @if($lists && $lists->count() > 0)
                                @foreach($lists->groupBy('space_name') as $spaceName => $spaceList)
                                    <option value="{{ $spaceName }}">{{ $spaceName }}</option>
                                @endforeach
                            @endif
                        </select>
                        <select id="assignment-filter" class="border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                            <option value="">All Lists</option>
                            <option value="assigned">Assigned</option>
                            <option value="unassigned">Unassigned</option>
                        </select>
                        <!-- Cache Status Indicator -->
                        <div id="cache-status-indicator" class="flex items-center px-3 py-2 rounded text-xs font-medium bg-gray-100 dark:bg-gray-700">
                            <i class="fas fa-spinner fa-spin mr-1 text-gray-500"></i>
                            <span class="text-gray-600 dark:text-gray-400">Checking cache...</span>
                        </div>
                        <button type="button" 
                                class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out"
                                onclick="refreshLists()">
                            <i class="fas fa-sync mr-2"></i>
                            Refresh
                        </button>
                    </div>
                </div>
            </div>

            <!-- Success/Error Messages -->
            <div id="message-container" class="mb-4 hidden"></div>

            <!-- Lists Table -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                <div class="relative">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600" onclick="sortTable('name')">
                                    List Name
                                    <i class="fas fa-sort ml-1"></i>
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600" onclick="sortTable('space')">
                                    Space/Workspace
                                    <i class="fas fa-sort ml-1"></i>
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600" onclick="sortTable('status')">
                                    Status
                                    <i class="fas fa-sort ml-1"></i>
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600" onclick="sortTable('assigned')">
                                    Assigned Contact
                                    <i class="fas fa-sort ml-1"></i>
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody id="lists-table-body" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            @foreach($lists as $list)
                                @php
                                    // Check if this list is already assigned
                                    $assignment = collect($assignedLists)->firstWhere('clickup_list_id', $list['id']);
                                    $isAssigned = !is_null($assignment);
                                @endphp
                                <tr class="table-row {{ $isAssigned ? 'table-row-assigned' : '' }}" 
                                    data-list-id="{{ $list['id'] }}"
                                    data-space="{{ $list['space_name'] ?? '' }}"
                                    data-name="{{ strtolower($list['name']) }}"
                                    data-workspace="{{ $list['workspace_name'] ?? '' }}"
                                    data-assignment="{{ $isAssigned ? 'assigned' : 'unassigned' }}">
                                    
                                    <!-- List Name -->
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                    {{ $list['name'] }}
                                                </div>
                                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                                    {{ $list['task_count'] ?? 0 }} tasks
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    
                                    <!-- Space/Workspace -->
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900 dark:text-white">
                                            {{ $list['workspace_name'] ?? 'Unknown Workspace' }}
                                        </div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400">
                                            {{ $list['space_name'] ?? 'Unknown Space' }}
                                        </div>
                                        @if(isset($list['folder_name']))
                                            <div class="text-xs text-blue-600 dark:text-blue-400">
                                                <i class="fas fa-folder mr-1"></i>{{ $list['folder_name'] }}
                                            </div>
                                        @endif
                                    </td>
                                    
                                    <!-- Status -->
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        @if(isset($list['status']) && $list['status'] === 'active')
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                                                <i class="fas fa-check-circle mr-1"></i>
                                                Active
                                            </span>
                                        @else
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
                                                <i class="fas fa-pause-circle mr-1"></i>
                                                Inactive
                                            </span>
                                        @endif
                                    </td>
                                    
                                    <!-- Assigned Contact -->
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        @if($isAssigned)
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-8 w-8">
                                                    <div class="h-8 w-8 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
                                                        <i class="fas fa-user text-green-600 dark:text-green-400 text-sm"></i>
                                                    </div>
                                                </div>
                                                <div class="ml-3">
                                                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                        {{ $assignment['assigned_to_contact_name'] }}
                                                    </div>
                                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                                        Assigned {{ \Carbon\Carbon::parse($assignment['assigned_at'])->diffForHumans() }}
                                                    </div>
                                                </div>
                                            </div>
                                        @else
                                            <span class="text-sm text-gray-500 dark:text-gray-400 italic">Not assigned</span>
                                        @endif
                                    </td>
                                    
                                    <!-- Actions -->
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button type="button"
                                                    class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"
                                                    onclick="viewListDetails('{{ $list['id'] }}')"
                                                    title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            
                                            @if($isAssigned)
                                                <button type="button"
                                                        class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                                                        onclick="unassignList('{{ $list['id'] }}', '{{ $assignment['assigned_to_contact_name'] }}')"
                                                        title="Unassign from {{ $assignment['assigned_to_contact_name'] }}">
                                                    <i class="fas fa-user-times"></i>
                                                </button>
                                            @else
                                                <button type="button"
                                                        class="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300"
                                                        onclick="assignList('{{ $list['id'] }}', '{{ $list['name'] }}')"
                                                        title="Assign to Contact">
                                                    <i class="fas fa-user-plus"></i>
                                                </button>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        @else
            <!-- No Lists Found -->
            <div class="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-8 text-center">
                <i class="fas fa-list-ul text-gray-400 text-4xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Lists Found</h3>
                <p class="text-gray-500 dark:text-gray-400 mb-4">No ClickUp lists were found. This could be due to:</p>
                <div class="text-left max-w-md mx-auto">
                    <div class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                        <p><strong>1.</strong> No lists in your selected ClickUp spaces</p>
                        <p><strong>2.</strong> API token doesn't have access to lists</p>
                        <p><strong>3.</strong> Lists haven't been synced yet</p>
                        <p><strong>4.</strong> Network connectivity issues</p>
                    </div>
                    <div class="flex space-x-2 mt-4">
                        <button type="button"
                                class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out"
                                onclick="refreshLists()">
                            <i class="fas fa-sync mr-2"></i>
                            Refresh Lists
                        </button>
                    </div>
                </div>
            </div>
        @endif
    @endif
</div>

<!-- Assignment Modal -->
<div id="assignment-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-md shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white" id="assignment-modal-title">Assign List</h3>
                <button type="button" id="close-assignment-modal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="assignment-form">
                <input type="hidden" id="assignment-list-id" name="list_id">
                <input type="hidden" id="selected-contact-id" name="contact_id">
                <div class="mb-4">
                    <label for="contact-search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Search Contact
                    </label>
                    <div class="relative">
                        <input type="text" id="contact-search" placeholder="Search by name..."
                               class="w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 pl-10"
                               autocomplete="off">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <div id="contact-results" class="hidden absolute z-50 w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-y-auto mt-1 left-0 right-0">
                            <!-- Search results will appear here -->
                        </div>
                    </div>
                    <div id="selected-contact-display" class="hidden mt-2 p-2 bg-blue-50 dark:bg-blue-900 rounded border">
                        <span class="text-sm text-blue-800 dark:text-blue-200">Selected: </span>
                        <span id="selected-contact-name" class="font-medium text-blue-900 dark:text-blue-100"></span>
                        <button type="button" id="clear-selection" class="ml-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" id="cancel-assignment"
                            class="bg-gray-500 dark:bg-gray-600 hover:bg-gray-600 dark:hover:bg-gray-500 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Cancel
                    </button>
                    <button type="submit" id="confirm-assignment"
                            class="bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out" disabled>
                        <span class="button-text">Assign</span>
                        <i class="fas fa-spinner fa-spin ml-2 hidden loading-icon"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Unassign Confirmation Modal -->
<div id="unassign-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-md shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Unassign List</h3>
                <button type="button" id="close-unassign-modal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="mb-4">
                <p class="text-sm text-gray-600 dark:text-gray-400" id="unassign-confirmation-text">
                    Are you sure you want to unassign this list?
                </p>
            </div>

            <div class="flex justify-end space-x-3">
                <button type="button" id="cancel-unassign" class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-200 font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Cancel
                </button>
                <button type="button" id="confirm-unassign" class="bg-red-600 dark:bg-red-700 hover:bg-red-700 dark:hover:bg-red-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    <span class="button-text">Unassign</span>
                    <i class="fas fa-spinner fa-spin ml-2 hidden loading-icon"></i>
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Global variables
let allContacts = [];
let sortDirection = {};

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateCacheStatus();
    setInterval(updateCacheStatus, 30000); // Update every 30 seconds
    loadContacts();
});

// Cache status management
function updateCacheStatus() {
    const indicator = document.getElementById('cache-status-indicator');
    if (!indicator) return;

    fetch('{{ route("clickup.cache.status") }}')
        .then(response => response.json())
        .then(data => {
            const health = data.health || {};
            let statusClass = 'bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400';
            let icon = 'fas fa-check-circle';
            let text = 'Cache Healthy';

            if (health.status === 'warning') {
                statusClass = 'bg-yellow-100 dark:bg-yellow-900 text-yellow-600 dark:text-yellow-400';
                icon = 'fas fa-exclamation-triangle';
                text = 'Cache Warning';
            } else if (health.status === 'unhealthy') {
                statusClass = 'bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400';
                icon = 'fas fa-times-circle';
                text = 'Cache Unhealthy';
            }

            indicator.className = `flex items-center px-3 py-2 rounded text-xs font-medium ${statusClass}`;
            indicator.innerHTML = `<i class="${icon} mr-1"></i><span>${text}</span>`;
        })
        .catch(error => {
            indicator.className = 'flex items-center px-3 py-2 rounded text-xs font-medium bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400';
            indicator.innerHTML = '<i class="fas fa-exclamation-circle mr-1"></i><span>Cache Error</span>';
        });
}

// Load contacts for search
function loadContacts() {
    fetch('/clickup/api/contacts', {
        method: 'GET',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.contacts) {
            allContacts = data.contacts;
        }
    })
    .catch(error => {
        // Silently fail - contacts will be loaded when needed
    });
}

// Table sorting functionality
function sortTable(column) {
    const tbody = document.getElementById('lists-table-body');
    const rows = Array.from(tbody.querySelectorAll('tr'));

    // Toggle sort direction
    sortDirection[column] = sortDirection[column] === 'asc' ? 'desc' : 'asc';

    rows.sort((a, b) => {
        let aValue, bValue;

        switch(column) {
            case 'name':
                aValue = a.querySelector('td:first-child .text-sm.font-medium').textContent.toLowerCase();
                bValue = b.querySelector('td:first-child .text-sm.font-medium').textContent.toLowerCase();
                break;
            case 'space':
                aValue = a.querySelector('td:nth-child(2) .text-sm:first-child').textContent.toLowerCase();
                bValue = b.querySelector('td:nth-child(2) .text-sm:first-child').textContent.toLowerCase();
                break;
            case 'status':
                aValue = a.querySelector('td:nth-child(3) span').textContent.toLowerCase();
                bValue = b.querySelector('td:nth-child(3) span').textContent.toLowerCase();
                break;
            case 'assigned':
                const aAssigned = a.getAttribute('data-assignment') === 'assigned';
                const bAssigned = b.getAttribute('data-assignment') === 'assigned';
                aValue = aAssigned ? 'assigned' : 'unassigned';
                bValue = bAssigned ? 'assigned' : 'unassigned';
                break;
            default:
                return 0;
        }

        if (sortDirection[column] === 'asc') {
            return aValue.localeCompare(bValue);
        } else {
            return bValue.localeCompare(aValue);
        }
    });

    // Clear and re-append sorted rows
    tbody.innerHTML = '';
    rows.forEach(row => tbody.appendChild(row));

    // Update sort indicators
    document.querySelectorAll('th i.fas').forEach(icon => {
        icon.className = 'fas fa-sort ml-1';
    });

    const currentHeader = document.querySelector(`th[onclick="sortTable('${column}')"] i`);
    if (currentHeader) {
        currentHeader.className = `fas fa-sort-${sortDirection[column] === 'asc' ? 'up' : 'down'} ml-1`;
    }
}
</script>
@endpush
