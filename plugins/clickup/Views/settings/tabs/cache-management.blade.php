<!-- Cache Management Tab -->
<div class="space-y-6">
    <!-- Cache Status Overview -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Cache Status</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Current cache performance and health status.</p>
        </div>
        <div class="p-6">
            <div id="cache-status-container">
                <div class="flex items-center justify-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <span class="ml-3 text-gray-600 dark:text-gray-400">Loading cache status...</span>
                </div>
            </div>
        </div>
    </div>

    <!-- <PERSON>ache Statistics -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Cache Statistics</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Performance metrics and usage statistics.</p>
        </div>
        <div class="p-6">
            <div id="cache-stats-container">
                <div class="flex items-center justify-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <span class="ml-3 text-gray-600 dark:text-gray-400">Loading cache statistics...</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Cache Management Actions -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Cache Management</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Manually manage cache data and synchronization.</p>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- Refresh Cache -->
                <button id="refresh-cache-btn" 
                        class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-3 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-sync-alt mr-2"></i>
                    Refresh Cache
                </button>

                <!-- Warm Up Cache -->
                <button id="warmup-cache-btn" 
                        class="bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-bold py-3 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-fire mr-2"></i>
                    Warm Up Cache
                </button>

                <!-- Clear Cache -->
                <button id="clear-cache-btn" 
                        class="bg-orange-600 dark:bg-orange-700 hover:bg-orange-700 dark:hover:bg-orange-600 text-white font-bold py-3 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-trash mr-2"></i>
                    Clear Cache
                </button>

                <!-- Sync from ClickUp -->
                <button id="sync-from-clickup-btn" 
                        class="bg-purple-600 dark:bg-purple-700 hover:bg-purple-700 dark:hover:bg-purple-600 text-white font-bold py-3 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-cloud-download-alt mr-2"></i>
                    Sync from ClickUp
                </button>
            </div>

            <!-- Action Results -->
            <div id="cache-action-results" class="mt-4 hidden">
                <div class="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-md p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-info-circle text-blue-400"></i>
                        </div>
                        <div class="ml-3">
                            <p id="cache-action-message" class="text-sm text-blue-700 dark:text-blue-300"></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cache Configuration -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Cache Configuration</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Configure cache behavior and settings.</p>
        </div>
        <div class="p-6">
            <form id="cache-config-form" method="POST" action="{{ route('clickup.settings.update-cache-config') }}">
                @csrf
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Cache Duration -->
                    <div>
                        <label for="cache_duration" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Cache Duration
                        </label>
                        <select name="cache_duration" id="cache_duration" 
                                class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                            <option value="300" {{ ($generalSettings['cache_duration'] ?? 1800) == 300 ? 'selected' : '' }}>5 minutes</option>
                            <option value="900" {{ ($generalSettings['cache_duration'] ?? 1800) == 900 ? 'selected' : '' }}>15 minutes</option>
                            <option value="1800" {{ ($generalSettings['cache_duration'] ?? 1800) == 1800 ? 'selected' : '' }}>30 minutes</option>
                            <option value="3600" {{ ($generalSettings['cache_duration'] ?? 1800) == 3600 ? 'selected' : '' }}>1 hour</option>
                            <option value="7200" {{ ($generalSettings['cache_duration'] ?? 1800) == 7200 ? 'selected' : '' }}>2 hours</option>
                            <option value="14400" {{ ($generalSettings['cache_duration'] ?? 1800) == 14400 ? 'selected' : '' }}>4 hours</option>
                        </select>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">How long to cache ClickUp data</p>
                    </div>

                    <!-- Auto Warm-up -->
                    <div>
                        <label for="auto_warmup" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Auto Warm-up
                        </label>
                        <div class="mt-1">
                            <label class="inline-flex items-center">
                                <input type="checkbox" name="auto_warmup" id="auto_warmup" value="1"
                                       {{ ($generalSettings['auto_warmup'] ?? false) ? 'checked' : '' }}
                                       class="rounded border-gray-300 dark:border-gray-600 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Enable automatic cache warm-up</span>
                            </label>
                        </div>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Automatically warm cache for frequently used lists</p>
                    </div>
                </div>

                <div class="mt-6">
                    <button type="submit" 
                            class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-save mr-2"></i>
                        Save Configuration
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load initial cache status and stats
    loadCacheStatus();
    loadCacheStats();

    // Auto-refresh every 30 seconds
    setInterval(function() {
        loadCacheStatus();
        loadCacheStats();
    }, 30000);

    // Cache management buttons
    document.getElementById('refresh-cache-btn').addEventListener('click', function() {
        performCacheAction('refresh', this, 'Cache refreshed successfully!');
    });

    document.getElementById('warmup-cache-btn').addEventListener('click', function() {
        performCacheAction('warmup', this, 'Cache warmed up successfully!');
    });

    document.getElementById('clear-cache-btn').addEventListener('click', function() {
        if (confirm('Are you sure you want to clear all cache? This may temporarily slow down the system.')) {
            performCacheAction('clear', this, 'Cache cleared successfully!');
        }
    });

    document.getElementById('sync-from-clickup-btn').addEventListener('click', function() {
        performCacheAction('sync', this, 'Sync from ClickUp completed successfully!');
    });

    function loadCacheStatus() {
        fetch('{{ route("clickup.cache.status") }}')
            .then(response => response.json())
            .then(data => {
                displayCacheStatus(data);
            })
            .catch(error => {
                console.error('Error loading cache status:', error);
                document.getElementById('cache-status-container').innerHTML = 
                    '<div class="text-red-600 dark:text-red-400">Failed to load cache status</div>';
            });
    }

    function loadCacheStats() {
        fetch('{{ route("clickup.cache.stats") }}')
            .then(response => response.json())
            .then(data => {
                displayCacheStats(data);
            })
            .catch(error => {
                console.error('Error loading cache stats:', error);
                document.getElementById('cache-stats-container').innerHTML = 
                    '<div class="text-red-600 dark:text-red-400">Failed to load cache statistics</div>';
            });
    }

    function displayCacheStatus(data) {
        const container = document.getElementById('cache-status-container');
        const health = data.health || {};
        
        let statusColor = 'green';
        let statusIcon = 'check-circle';
        
        if (health.status === 'warning') {
            statusColor = 'yellow';
            statusIcon = 'exclamation-triangle';
        } else if (health.status === 'unhealthy') {
            statusColor = 'red';
            statusIcon = 'times-circle';
        }
        
        let html = `
            <div class="flex items-center mb-4">
                <i class="fas fa-${statusIcon} text-${statusColor}-500 text-2xl mr-3"></i>
                <div>
                    <h4 class="text-lg font-medium text-gray-900 dark:text-white">
                        Cache Status: ${health.status ? health.status.charAt(0).toUpperCase() + health.status.slice(1) : 'Unknown'}
                    </h4>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Last checked: ${new Date().toLocaleTimeString()}</p>
                </div>
            </div>
        `;
        
        if (health.issues && health.issues.length > 0) {
            html += '<div class="mt-4"><h5 class="font-medium text-gray-900 dark:text-white mb-2">Issues:</h5><ul class="list-disc list-inside text-sm text-gray-600 dark:text-gray-400">';
            health.issues.forEach(issue => {
                html += `<li>${issue}</li>`;
            });
            html += '</ul></div>';
        }
        
        container.innerHTML = html;
    }

    function displayCacheStats(data) {
        const container = document.getElementById('cache-stats-container');
        const stats = data.stats || {};
        
        const html = `
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">${stats.today?.hit_rate || 0}%</div>
                    <div class="text-sm text-blue-700 dark:text-blue-300">Today's Hit Rate</div>
                </div>
                <div class="bg-green-50 dark:bg-green-900 p-4 rounded-lg">
                    <div class="text-2xl font-bold text-green-600 dark:text-green-400">${stats.today?.total_requests || 0}</div>
                    <div class="text-sm text-green-700 dark:text-green-300">Today's Requests</div>
                </div>
                <div class="bg-purple-50 dark:bg-purple-900 p-4 rounded-lg">
                    <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">${stats.cache_info?.active_lists_in_db || 0}</div>
                    <div class="text-sm text-purple-700 dark:text-purple-300">Active Lists</div>
                </div>
                <div class="bg-orange-50 dark:bg-orange-900 p-4 rounded-lg">
                    <div class="text-2xl font-bold text-orange-600 dark:text-orange-400">${Math.round((stats.cache_info?.ttl || 0) / 60)}m</div>
                    <div class="text-sm text-orange-700 dark:text-orange-300">Cache TTL</div>
                </div>
            </div>
        `;
        
        container.innerHTML = html;
    }

    function performCacheAction(action, button, successMessage) {
        const originalText = button.innerHTML;
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';
        
        const endpoints = {
            'refresh': '{{ route("clickup.cache.refresh") }}',
            'warmup': '{{ route("clickup.cache.warmup") }}',
            'clear': '{{ route("clickup.cache.clear") }}',
            'sync': '{{ route("clickup.sync.all") }}'
        };
        
        fetch(endpoints[action], {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showActionResult(successMessage, 'success');
                // Refresh status and stats
                setTimeout(() => {
                    loadCacheStatus();
                    loadCacheStats();
                }, 1000);
            } else {
                showActionResult('Action failed: ' + (data.message || 'Unknown error'), 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showActionResult('Action failed. Please try again.', 'error');
        })
        .finally(() => {
            button.disabled = false;
            button.innerHTML = originalText;
        });
    }

    function showActionResult(message, type) {
        const container = document.getElementById('cache-action-results');
        const messageEl = document.getElementById('cache-action-message');
        
        messageEl.textContent = message;
        container.className = container.className.replace(/bg-\w+-50|border-\w+-200|text-\w+-700/g, '');
        
        if (type === 'success') {
            container.className += ' bg-green-50 dark:bg-green-900 border-green-200 dark:border-green-700';
            messageEl.className = 'text-sm text-green-700 dark:text-green-300';
        } else {
            container.className += ' bg-red-50 dark:bg-red-900 border-red-200 dark:border-red-700';
            messageEl.className = 'text-sm text-red-700 dark:text-red-300';
        }
        
        container.classList.remove('hidden');
        
        // Hide after 5 seconds
        setTimeout(() => {
            container.classList.add('hidden');
        }, 5000);
    }
});
</script>
@endpush
