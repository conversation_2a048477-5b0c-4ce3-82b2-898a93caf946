@if(!$currentToken)
    <div class="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-md p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas fa-exclamation-triangle text-yellow-400"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                    API Token Required
                </h3>
                <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                    <p>Please configure your ClickUp API token in the Workspace tab to manage lists.</p>
                </div>
            </div>
        </div>
    </div>
@else
    <!-- List Management Header -->
    <div class="mb-8">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">List Management</h3>
        <p class="text-sm text-gray-500 dark:text-gray-400 mb-6">Manage ClickUp lists and assign them to product managers from your contacts.</p>
        
        <!-- Assignment Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-list text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-blue-900 dark:text-blue-100">Total Lists</p>
                        <p class="text-lg font-semibold text-blue-600 dark:text-blue-400" id="total-lists-count">{{ count($lists) }}</p>
                    </div>
                </div>
            </div>
            <div class="bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-check text-green-600 dark:text-green-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-green-900 dark:text-green-100">Assigned</p>
                        <p class="text-lg font-semibold text-green-600 dark:text-green-400" id="assigned-lists-count">{{ count($assignedLists) }}</p>
                    </div>
                </div>
            </div>
            <div class="bg-orange-50 dark:bg-orange-900 border border-orange-200 dark:border-orange-700 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-times text-orange-600 dark:text-orange-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-orange-900 dark:text-orange-100">Unassigned</p>
                        <p class="text-lg font-semibold text-orange-600 dark:text-orange-400" id="unassigned-lists-count">{{ count($lists) - count($assignedLists) }}</p>
                    </div>
                </div>
            </div>
            <div class="bg-purple-50 dark:bg-purple-900 border border-purple-200 dark:border-purple-700 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-users text-purple-600 dark:text-purple-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-purple-900 dark:text-purple-100">Managers</p>
                        <p class="text-lg font-semibold text-purple-600 dark:text-purple-400" id="unique-managers-count">{{ collect($assignedLists)->pluck('assigned_to_contact_id')->unique()->count() }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if(!empty($lists))
        <!-- Search and Filter Controls -->
        <div class="mb-6">
            <div class="flex flex-col sm:flex-row gap-4">
                <div class="flex-1">
                    <input type="text" id="list-search" placeholder="Search lists by name..."
                           class="w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div class="flex space-x-2">
                    <select id="space-filter" class="border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Spaces</option>
                        @foreach($lists->groupBy('space_name') as $spaceName => $spaceList)
                            <option value="{{ $spaceName }}">{{ $spaceName }}</option>
                        @endforeach
                    </select>
                    <select id="assignment-filter" class="border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Lists</option>
                        <option value="assigned">Assigned</option>
                        <option value="unassigned">Unassigned</option>
                    </select>
                    <button type="button" 
                            class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out"
                            onclick="refreshListManagement()">
                        <i class="fas fa-sync mr-2"></i>
                        Refresh
                    </button>
                </div>
            </div>
        </div>

        <!-- Lists Grid -->
        <div id="lists-management-container" class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            @foreach($lists as $list)
                @php
                    $assignment = collect($assignedLists)->firstWhere('clickup_list_id', $list['id']);
                    $isAssigned = !is_null($assignment);
                @endphp
                <div class="list-management-item bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-6 hover:shadow-lg transition duration-150 ease-in-out"
                     data-space="{{ $list['space_name'] ?? '' }}"
                     data-name="{{ strtolower($list['name']) }}"
                     data-assignment="{{ $isAssigned ? 'assigned' : 'unassigned' }}">
                    
                    <!-- List Header -->
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex-1">
                            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-1">{{ $list['name'] }}</h4>
                            <p class="text-sm text-gray-500 dark:text-gray-400">{{ $list['space_name'] ?? 'Unknown Space' }}</p>
                            <p class="text-xs text-gray-400 dark:text-gray-500">ID: {{ $list['id'] }}</p>
                        </div>
                        <div class="flex-shrink-0">
                            @if($isAssigned)
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                                    <i class="fas fa-user-check mr-1"></i>
                                    Assigned
                                </span>
                            @else
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
                                    <i class="fas fa-user-times mr-1"></i>
                                    Unassigned
                                </span>
                            @endif
                        </div>
                    </div>

                    <!-- Assignment Info -->
                    @if($isAssigned)
                        <div class="bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-md p-3 mb-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-user text-green-600 dark:text-green-400"></i>
                                </div>
                                <div class="ml-3 flex-1">
                                    <p class="text-sm font-medium text-green-900 dark:text-green-100">
                                        {{ $assignment['assigned_to_contact_name'] ?? 'Unknown Contact' }}
                                    </p>
                                    <p class="text-xs text-green-700 dark:text-green-300">
                                        Assigned {{ \Carbon\Carbon::parse($assignment['assigned_at'])->diffForHumans() }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- List Stats -->
                    @if(isset($list['task_count']) || isset($list['member_count']))
                        <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-4">
                            @if(isset($list['task_count']))
                                <span><i class="fas fa-tasks mr-1"></i>{{ $list['task_count'] }} tasks</span>
                            @endif
                            @if(isset($list['member_count']))
                                <span><i class="fas fa-users mr-1"></i>{{ $list['member_count'] }} members</span>
                            @endif
                        </div>
                    @endif

                    <!-- Action Buttons -->
                    <div class="flex space-x-2">
                        @if($isAssigned)
                            <button type="button" 
                                    class="flex-1 bg-orange-600 dark:bg-orange-700 hover:bg-orange-700 dark:hover:bg-orange-600 text-white font-bold py-2 px-3 rounded text-sm transition duration-150 ease-in-out"
                                    onclick="reassignList('{{ $list['id'] }}', '{{ $list['name'] }}')">
                                <i class="fas fa-user-edit mr-1"></i>
                                Reassign
                            </button>
                            <button type="button" 
                                    class="flex-1 bg-red-600 dark:bg-red-700 hover:bg-red-700 dark:hover:bg-red-600 text-white font-bold py-2 px-3 rounded text-sm transition duration-150 ease-in-out"
                                    onclick="unassignList('{{ $list['id'] }}', '{{ $list['name'] }}')">
                                <i class="fas fa-user-times mr-1"></i>
                                Unassign
                            </button>
                        @else
                            <button type="button" 
                                    class="flex-1 bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-bold py-2 px-3 rounded text-sm transition duration-150 ease-in-out"
                                    onclick="assignList('{{ $list['id'] }}', '{{ $list['name'] }}')">
                                <i class="fas fa-user-plus mr-1"></i>
                                Assign Manager
                            </button>
                        @endif
                        <button type="button" 
                                class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-3 rounded text-sm transition duration-150 ease-in-out"
                                onclick="viewListDetails('{{ $list['id'] }}')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
            @endforeach
        </div>
    @else
        <div class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-8 text-center">
            <div class="flex flex-col items-center">
                <div class="w-16 h-16 bg-gray-100 dark:bg-gray-600 rounded-full flex items-center justify-center mb-4">
                    <i class="fas fa-list text-gray-400 text-2xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Lists Found</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
                    No lists were found in your selected ClickUp spaces. Please check your workspace configuration in the Workspace tab.
                </p>
                <button type="button" 
                        class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out"
                        onclick="refreshListManagement()">
                    <i class="fas fa-sync mr-2"></i>
                    Refresh Lists
                </button>
            </div>
        </div>
    @endif
@endif

<!-- Assignment Modal -->
<div id="assignment-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-md shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white" id="assignment-modal-title">Assign List</h3>
                <button type="button" id="close-assignment-modal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="assignment-form">
                <input type="hidden" id="assignment-list-id" name="list_id">
                <div class="mb-4">
                    <label for="contact-select" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Select Product Manager
                    </label>
                    <select id="contact-select" name="contact_id" required
                            class="w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Loading contacts...</option>
                    </select>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" id="cancel-assignment"
                            class="bg-gray-500 dark:bg-gray-600 hover:bg-gray-600 dark:hover:bg-gray-500 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Cancel
                    </button>
                    <button type="submit"
                            class="bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-save mr-2"></i>
                        Assign
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- List Details Modal -->
<div id="list-details-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">List Details</h3>
                <button type="button" id="close-list-details-modal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="list-details-content" class="space-y-4">
                <!-- List details will be loaded here -->
            </div>
        </div>
    </div>
</div>

<script>
let availableContacts = [];

// Load contacts when the page loads
document.addEventListener('DOMContentLoaded', function() {
    loadContacts();
    initializeFilters();
});

// Load available contacts for assignment
function loadContacts() {
    fetch('/clickup/api/contacts')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                availableContacts = data.contacts;
                populateContactSelect();
            } else {
                console.error('Failed to load contacts:', data.message);
            }
        })
        .catch(error => {
            console.error('Error loading contacts:', error);
        });
}

// Populate the contact select dropdown
function populateContactSelect() {
    const select = document.getElementById('contact-select');
    select.innerHTML = '<option value="">Select a contact...</option>';

    availableContacts.forEach(contact => {
        const option = document.createElement('option');
        option.value = contact.id;
        option.textContent = `${contact.name}${contact.position ? ' - ' + contact.position : ''}`;
        select.appendChild(option);
    });
}

// Initialize search and filter functionality
function initializeFilters() {
    // List search functionality
    document.getElementById('list-search')?.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        filterLists();
    });

    // Space filter functionality
    document.getElementById('space-filter')?.addEventListener('change', function() {
        filterLists();
    });

    // Assignment filter functionality
    document.getElementById('assignment-filter')?.addEventListener('change', function() {
        filterLists();
    });
}

// Filter lists based on search and filter criteria
function filterLists() {
    const searchTerm = document.getElementById('list-search')?.value.toLowerCase() || '';
    const selectedSpace = document.getElementById('space-filter')?.value || '';
    const assignmentFilter = document.getElementById('assignment-filter')?.value || '';

    const listItems = document.querySelectorAll('.list-management-item');

    listItems.forEach(item => {
        const name = item.getAttribute('data-name');
        const space = item.getAttribute('data-space');
        const assignment = item.getAttribute('data-assignment');

        let show = true;

        // Apply search filter
        if (searchTerm && !name.includes(searchTerm)) {
            show = false;
        }

        // Apply space filter
        if (selectedSpace && space !== selectedSpace) {
            show = false;
        }

        // Apply assignment filter
        if (assignmentFilter && assignment !== assignmentFilter) {
            show = false;
        }

        item.style.display = show ? 'block' : 'none';
    });
}

// Assign a list to a contact
function assignList(listId, listName) {
    document.getElementById('assignment-modal-title').textContent = `Assign "${listName}"`;
    document.getElementById('assignment-list-id').value = listId;
    document.getElementById('assignment-modal').classList.remove('hidden');
}

// Reassign a list to a different contact
function reassignList(listId, listName) {
    document.getElementById('assignment-modal-title').textContent = `Reassign "${listName}"`;
    document.getElementById('assignment-list-id').value = listId;
    document.getElementById('assignment-modal').classList.remove('hidden');
}

// Unassign a list
function unassignList(listId, listName) {
    if (confirm(`Are you sure you want to unassign "${listName}"?`)) {
        fetch('/clickup/settings/unassign-list-api', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                list_id: listId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Failed to unassign list: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error unassigning list');
        });
    }
}

// View list details
function viewListDetails(listId) {
    document.getElementById('list-details-modal').classList.remove('hidden');

    // Load list details via AJAX
    fetch(`/clickup/api/lists/${listId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayListDetails(data.list);
            } else {
                document.getElementById('list-details-content').innerHTML =
                    '<p class="text-red-600 dark:text-red-400">Failed to load list details.</p>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('list-details-content').innerHTML =
                '<p class="text-red-600 dark:text-red-400">Error loading list details.</p>';
        });
}

// Display list details in modal
function displayListDetails(list) {
    const content = `
        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h4 class="font-medium text-gray-900 dark:text-white mb-2">${list.name}</h4>
            <p class="text-sm text-gray-600 dark:text-gray-400">Space: ${list.space?.name || 'Unknown'}</p>
            <p class="text-sm text-gray-600 dark:text-gray-400">List ID: ${list.id}</p>
            ${list.description ? `<p class="text-sm text-gray-600 dark:text-gray-400 mt-2">${list.description}</p>` : ''}
        </div>
    `;
    document.getElementById('list-details-content').innerHTML = content;
}

// Refresh list management
function refreshListManagement() {
    location.reload();
}

// Handle assignment form submission
document.getElementById('assignment-form')?.addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const listId = formData.get('list_id');
    const contactId = formData.get('contact_id');

    if (!contactId) {
        alert('Please select a contact.');
        return;
    }

    fetch('/clickup/settings/assign-list', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            list_id: listId,
            contact_id: contactId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Failed to assign list: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error assigning list');
    });
});

// Modal close functionality
document.getElementById('close-assignment-modal')?.addEventListener('click', function() {
    document.getElementById('assignment-modal').classList.add('hidden');
});

document.getElementById('cancel-assignment')?.addEventListener('click', function() {
    document.getElementById('assignment-modal').classList.add('hidden');
});

document.getElementById('close-list-details-modal')?.addEventListener('click', function() {
    document.getElementById('list-details-modal').classList.add('hidden');
});

// Close modals on outside click
document.getElementById('assignment-modal')?.addEventListener('click', function(e) {
    if (e.target === this) {
        this.classList.add('hidden');
    }
});

document.getElementById('list-details-modal')?.addEventListener('click', function(e) {
    if (e.target === this) {
        this.classList.add('hidden');
    }
});
</script>
