<style>
    /* Modern UI Enhancements */
    .lists-container {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border-radius: 16px;
        padding: 24px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .dark .lists-container {
        background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    }

    .stats-card {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    }

    .dark .stats-card {
        background: rgba(31, 41, 55, 0.9);
        border: 1px solid rgba(75, 85, 99, 0.3);
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
    }

    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    .dark .stats-card:hover {
        box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
    }

    .table-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .dark .table-container {
        background: rgba(31, 41, 55, 0.95);
        border: 1px solid rgba(75, 85, 99, 0.3);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    }

    .table-row-assigned {
        background: linear-gradient(90deg, rgba(34, 197, 94, 0.1) 0%, rgba(34, 197, 94, 0.05) 100%);
        border-left: 4px solid #22c55e;
    }

    .dark .table-row-assigned {
        background: linear-gradient(90deg, rgba(34, 197, 94, 0.2) 0%, rgba(34, 197, 94, 0.1) 100%);
        border-left: 4px solid #16a34a;
    }

    .table-row {
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .table-row:hover {
        background: linear-gradient(90deg, rgba(59, 130, 246, 0.05) 0%, rgba(59, 130, 246, 0.02) 100%);
        transform: translateX(2px);
    }

    .dark .table-row:hover {
        background: linear-gradient(90deg, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.05) 100%);
    }

    .action-button {
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        border-radius: 8px;
        padding: 8px;
    }

    .action-button:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .modal-container {
        backdrop-filter: blur(8px);
        background: rgba(0, 0, 0, 0.4);
    }

    .modal-content {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }

    .dark .modal-content {
        background: rgba(31, 41, 55, 0.95);
        border: 1px solid rgba(75, 85, 99, 0.3);
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
    }

    .search-input {
        border-radius: 12px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border: 2px solid transparent;
        background: rgba(255, 255, 255, 0.9);
    }

    .search-input:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        background: rgba(255, 255, 255, 1);
    }

    .dark .search-input {
        background: rgba(55, 65, 81, 0.9);
    }

    .dark .search-input:focus {
        background: rgba(55, 65, 81, 1);
    }

    .button-modern {
        border-radius: 12px;
        font-weight: 600;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    }

    .button-modern:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .success-message, .error-message {
        animation: slideInDown 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        border-radius: 12px;
        backdrop-filter: blur(10px);
    }

    @keyframes slideInDown {
        from { 
            opacity: 0; 
            transform: translateY(-20px) scale(0.95); 
        }
        to { 
            opacity: 1; 
            transform: translateY(0) scale(1); 
        }
    }

    .loading-spinner {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    .contact-result-item {
        transition: all 0.2s ease;
        border-radius: 8px;
    }

    .contact-result-item:hover {
        background: rgba(59, 130, 246, 0.1);
        transform: translateX(4px);
    }
</style>

<div class="space-y-6">
    @if(empty($currentToken))
        <!-- No API Token -->
        <div class="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-md p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">No API Token Configured</h3>
                    <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                        <p>Please configure your ClickUp API token first to manage lists.</p>
                    </div>
                </div>
            </div>
        </div>
    @else
        <div class="lists-container">
            <!-- Lists Management Header -->
            <div class="mb-8">
                <div class="text-center mb-6">
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">Lists Management</h3>
                    <p class="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">Manage your ClickUp lists and assign them to contacts from your business directory with our enhanced caching system for lightning-fast performance.</p>
                </div>

                <!-- Statistics Dashboard -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="stats-card p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-list text-blue-600 dark:text-blue-400 text-xl"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Lists</p>
                                <p class="text-2xl font-bold text-gray-900 dark:text-white" id="total-lists-count">{{ $lists ? $lists->count() : 0 }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="stats-card p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-user-check text-green-600 dark:text-green-400 text-xl"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Assigned</p>
                                <p class="text-2xl font-bold text-gray-900 dark:text-white" id="assigned-lists-count">{{ count($assignedLists) }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="stats-card p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-user-times text-orange-600 dark:text-orange-400 text-xl"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Unassigned</p>
                                <p class="text-2xl font-bold text-gray-900 dark:text-white" id="unassigned-lists-count">{{ ($lists ? $lists->count() : 0) - count($assignedLists) }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="stats-card p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-users text-purple-600 dark:text-purple-400 text-xl"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Contacts</p>
                                <p class="text-2xl font-bold text-gray-900 dark:text-white" id="unique-contacts-count">{{ collect($assignedLists)->pluck('assigned_to_contact_id')->unique()->count() }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            @if(!empty($lists))
                <!-- Search and Filter Controls -->
                <div class="mb-8">
                    <div class="flex flex-col lg:flex-row gap-4">
                        <div class="flex-1">
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                    <i class="fas fa-search text-gray-400"></i>
                                </div>
                                <input type="text" id="list-search" placeholder="Search lists by name, space, or workspace..."
                                       class="search-input w-full pl-12 pr-4 py-3 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none">
                            </div>
                        </div>
                        <div class="flex flex-wrap gap-3">
                            <select id="space-filter" class="search-input px-4 py-3 text-gray-900 dark:text-white focus:outline-none min-w-[140px]">
                                <option value="">All Spaces</option>
                                @if($lists && $lists->count() > 0)
                                    @foreach($lists->groupBy('space_name') as $spaceName => $spaceList)
                                        <option value="{{ $spaceName }}">{{ $spaceName }}</option>
                                    @endforeach
                                @endif
                            </select>
                            <select id="assignment-filter" class="search-input px-4 py-3 text-gray-900 dark:text-white focus:outline-none min-w-[140px]">
                                <option value="">All Lists</option>
                                <option value="assigned">Assigned</option>
                                <option value="unassigned">Unassigned</option>
                            </select>
                            <!-- Cache Status Indicator -->
                            <div id="cache-status-indicator" class="flex items-center px-4 py-3 rounded-xl text-sm font-medium bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600">
                                <i class="fas fa-spinner loading-spinner mr-2 text-gray-500"></i>
                                <span class="text-gray-600 dark:text-gray-400">Checking cache...</span>
                            </div>
                            <button type="button" 
                                    class="button-modern bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white px-6 py-3 flex items-center space-x-2"
                                    onclick="refreshLists()">
                                <i class="fas fa-sync"></i>
                                <span>Refresh</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Success/Error Messages -->
                <div id="message-container" class="mb-6 hidden"></div>

                <!-- Lists Table -->
                <div class="table-container">
                    <div class="relative">
                        <table class="min-w-full">
                            <thead>
                                <tr class="border-b border-gray-200 dark:border-gray-700">
                                    <th scope="col" class="px-8 py-4 text-left text-sm font-semibold text-gray-700 dark:text-gray-300 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 rounded-tl-lg" onclick="sortTable('name')">
                                        <div class="flex items-center space-x-2">
                                            <span>List Name</span>
                                            <i class="fas fa-sort text-gray-400"></i>
                                        </div>
                                    </th>
                                    <th scope="col" class="px-8 py-4 text-left text-sm font-semibold text-gray-700 dark:text-gray-300 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200" onclick="sortTable('space')">
                                        <div class="flex items-center space-x-2">
                                            <span>Space/Workspace</span>
                                            <i class="fas fa-sort text-gray-400"></i>
                                        </div>
                                    </th>
                                    <th scope="col" class="px-8 py-4 text-left text-sm font-semibold text-gray-700 dark:text-gray-300 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200" onclick="sortTable('status')">
                                        <div class="flex items-center space-x-2">
                                            <span>Status</span>
                                            <i class="fas fa-sort text-gray-400"></i>
                                        </div>
                                    </th>
                                    <th scope="col" class="px-8 py-4 text-left text-sm font-semibold text-gray-700 dark:text-gray-300 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200" onclick="sortTable('assigned')">
                                        <div class="flex items-center space-x-2">
                                            <span>Assigned Contact</span>
                                            <i class="fas fa-sort text-gray-400"></i>
                                        </div>
                                    </th>
                                    <th scope="col" class="px-8 py-4 text-left text-sm font-semibold text-gray-700 dark:text-gray-300 rounded-tr-lg">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="lists-table-body" class="divide-y divide-gray-100 dark:divide-gray-700">
                                @foreach($lists as $list)
                                    @php
                                        // Check if this list is already assigned
                                        $assignment = collect($assignedLists)->firstWhere('clickup_list_id', $list['id']);
                                        $isAssigned = !is_null($assignment);
                                    @endphp
                                    <tr class="table-row {{ $isAssigned ? 'table-row-assigned' : '' }}"
                                        data-list-id="{{ $list['id'] }}"
                                        data-space="{{ $list['space_name'] ?? '' }}"
                                        data-name="{{ strtolower($list['name']) }}"
                                        data-workspace="{{ $list['workspace_name'] ?? '' }}"
                                        data-assignment="{{ $isAssigned ? 'assigned' : 'unassigned' }}">

                                        <!-- List Name -->
                                        <td class="px-8 py-6">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                                                    <i class="fas fa-list text-white text-sm"></i>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-semibold text-gray-900 dark:text-white">
                                                        {{ $list['name'] }}
                                                    </div>
                                                    <div class="text-sm text-gray-500 dark:text-gray-400 flex items-center space-x-2">
                                                        <i class="fas fa-tasks text-xs"></i>
                                                        <span>{{ $list['task_count'] ?? 0 }} tasks</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>

                                        <!-- Space/Workspace -->
                                        <td class="px-8 py-6">
                                            <div class="space-y-1">
                                                <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                    {{ $list['workspace_name'] ?? 'Unknown Workspace' }}
                                                </div>
                                                <div class="text-sm text-gray-500 dark:text-gray-400 flex items-center space-x-1">
                                                    <i class="fas fa-layer-group text-xs"></i>
                                                    <span>{{ $list['space_name'] ?? 'Unknown Space' }}</span>
                                                </div>
                                                @if(isset($list['folder_name']))
                                                    <div class="text-xs text-blue-600 dark:text-blue-400 flex items-center space-x-1">
                                                        <i class="fas fa-folder text-xs"></i>
                                                        <span>{{ $list['folder_name'] }}</span>
                                                    </div>
                                                @endif
                                            </div>
                                        </td>

                                        <!-- Status -->
                                        <td class="px-8 py-6">
                                            @if(isset($list['status']) && $list['status'] === 'active')
                                                <span class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-semibold bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 border border-green-200 dark:border-green-700">
                                                    <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                                                    Active
                                                </span>
                                            @else
                                                <span class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-semibold bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 border border-gray-200 dark:border-gray-600">
                                                    <div class="w-2 h-2 bg-gray-500 rounded-full mr-2"></div>
                                                    Inactive
                                                </span>
                                            @endif
                                        </td>

                                        <!-- Assigned Contact -->
                                        <td class="px-8 py-6">
                                            @if($isAssigned)
                                                <div class="flex items-center">
                                                    <div class="flex-shrink-0 w-10 h-10">
                                                        <div class="w-10 h-10 rounded-full bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center">
                                                            <i class="fas fa-user text-white text-sm"></i>
                                                        </div>
                                                    </div>
                                                    <div class="ml-4">
                                                        <div class="text-sm font-semibold text-gray-900 dark:text-white">
                                                            {{ $assignment['assigned_to_contact_name'] }}
                                                        </div>
                                                        <div class="text-xs text-gray-500 dark:text-gray-400 flex items-center space-x-1">
                                                            <i class="fas fa-clock text-xs"></i>
                                                            <span>{{ \Carbon\Carbon::parse($assignment['assigned_at'])->diffForHumans() }}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            @else
                                                <div class="flex items-center">
                                                    <div class="w-10 h-10 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                                                        <i class="fas fa-user-slash text-gray-400 text-sm"></i>
                                                    </div>
                                                    <div class="ml-4">
                                                        <span class="text-sm text-gray-500 dark:text-gray-400 italic">Not assigned</span>
                                                    </div>
                                                </div>
                                            @endif
                                        </td>

                                        <!-- Actions -->
                                        <td class="px-8 py-6">
                                            <div class="flex items-center space-x-3">
                                                <button type="button"
                                                        class="action-button text-blue-600 dark:text-blue-400 hover:text-white hover:bg-blue-600 dark:hover:bg-blue-500 transition-all duration-200"
                                                        onclick="viewListDetails('{{ $list['id'] }}')"
                                                        title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </button>

                                                @if($isAssigned)
                                                    <button type="button"
                                                            class="action-button text-orange-600 dark:text-orange-400 hover:text-white hover:bg-orange-600 dark:hover:bg-orange-500 transition-all duration-200"
                                                            onclick="reassignList('{{ $list['id'] }}', '{{ $list['name'] }}', '{{ $assignment['assigned_to_contact_name'] }}')"
                                                            title="Reassign to Different Contact">
                                                        <i class="fas fa-user-edit"></i>
                                                    </button>
                                                    <button type="button"
                                                            class="action-button text-red-600 dark:text-red-400 hover:text-white hover:bg-red-600 dark:hover:bg-red-500 transition-all duration-200"
                                                            onclick="unassignList('{{ $list['id'] }}', '{{ $assignment['assigned_to_contact_name'] }}')"
                                                            title="Unassign from {{ $assignment['assigned_to_contact_name'] }}">
                                                        <i class="fas fa-user-times"></i>
                                                    </button>
                                                @else
                                                    <button type="button"
                                                            class="action-button text-green-600 dark:text-green-400 hover:text-white hover:bg-green-600 dark:hover:bg-green-500 transition-all duration-200"
                                                            onclick="assignList('{{ $list['id'] }}', '{{ $list['name'] }}')"
                                                            title="Assign to Contact">
                                                        <i class="fas fa-user-plus"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            @else
                <!-- No Lists Found -->
                <div class="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-8 text-center">
                    <i class="fas fa-list-ul text-gray-400 text-4xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Lists Found</h3>
                    <p class="text-gray-500 dark:text-gray-400 mb-4">No ClickUp lists were found. This could be due to:</p>
                    <div class="text-left max-w-md mx-auto">
                        <div class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                            <p><strong>1.</strong> No lists in your selected ClickUp spaces</p>
                            <p><strong>2.</strong> API token doesn't have access to lists</p>
                            <p><strong>3.</strong> Lists haven't been synced yet</p>
                            <p><strong>4.</strong> Network connectivity issues</p>
                        </div>
                        <div class="flex space-x-2 mt-4">
                            <button type="button"
                                    class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out"
                                    onclick="refreshLists()">
                                <i class="fas fa-sync mr-2"></i>
                                Refresh Lists
                            </button>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    @endif
</div>

<!-- Assignment Modal -->
<div id="assignment-modal" class="modal-container fixed inset-0 overflow-y-auto h-full w-full hidden z-50">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div class="modal-content inline-block align-bottom text-left overflow-hidden transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="px-8 pt-8 pb-6">
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                            <i class="fas fa-user-plus text-white"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white" id="assignment-modal-title">Assign List</h3>
                    </div>
                    <button type="button" id="close-assignment-modal" class="action-button text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <form id="assignment-form">
                    <input type="hidden" id="assignment-list-id" name="list_id">
                    <input type="hidden" id="selected-contact-id" name="contact_id">

                    <div class="mb-6">
                        <label for="contact-search" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                            Search Contact
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                            <input type="text" id="contact-search" placeholder="Type to search contacts..."
                                   class="search-input w-full pl-12 pr-4 py-4 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none"
                                   autocomplete="off">
                            <div id="contact-results" class="hidden absolute z-50 w-full bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-xl shadow-xl max-h-60 overflow-y-auto mt-2">
                                <!-- Search results will appear here -->
                            </div>
                        </div>

                        <div id="selected-contact-display" class="hidden mt-4 p-4 bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900 dark:to-blue-800 rounded-xl border border-blue-200 dark:border-blue-700">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                                        <i class="fas fa-user text-white text-sm"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-semibold text-blue-900 dark:text-blue-100">Selected Contact:</div>
                                        <div id="selected-contact-name" class="text-sm text-blue-800 dark:text-blue-200"></div>
                                    </div>
                                </div>
                                <button type="button" id="clear-selection" class="action-button text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 hover:bg-blue-200 dark:hover:bg-blue-700">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-4">
                        <button type="button" id="cancel-assignment"
                                class="button-modern bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-6 py-3">
                            Cancel
                        </button>
                        <button type="submit" id="confirm-assignment"
                                class="button-modern bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-8 py-3 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            <span class="button-text">Assign List</span>
                            <i class="fas fa-spinner loading-spinner ml-2 hidden loading-icon"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Unassign Confirmation Modal -->
<div id="unassign-modal" class="modal-container fixed inset-0 overflow-y-auto h-full w-full hidden z-50">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div class="modal-content inline-block align-bottom text-left overflow-hidden transform transition-all sm:my-8 sm:align-middle sm:max-w-md sm:w-full">
            <div class="px-8 pt-8 pb-6">
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center">
                            <i class="fas fa-user-times text-white"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white">Unassign List</h3>
                    </div>
                    <button type="button" id="close-unassign-modal" class="action-button text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="mb-6">
                    <p class="text-gray-600 dark:text-gray-400" id="unassign-confirmation-text">
                        Are you sure you want to unassign this list?
                    </p>
                </div>

                <div class="flex justify-end space-x-4">
                    <button type="button" id="cancel-unassign" class="button-modern bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-6 py-3">
                        Cancel
                    </button>
                    <button type="button" id="confirm-unassign" class="button-modern bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-8 py-3">
                        <span class="button-text">Unassign</span>
                        <i class="fas fa-spinner loading-spinner ml-2 hidden loading-icon"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Global variables
let allContacts = [];
let sortDirection = {};
let searchTimeout = null;

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateCacheStatus();
    setInterval(updateCacheStatus, 30000); // Update every 30 seconds
    loadContacts();
    setupEventListeners();
});

// Cache status management
function updateCacheStatus() {
    const indicator = document.getElementById('cache-status-indicator');
    if (!indicator) return;

    fetch('{{ route("clickup.cache.status") }}')
        .then(response => response.json())
        .then(data => {
            const health = data.health || {};
            let statusClass = 'bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400';
            let icon = 'fas fa-check-circle';
            let text = 'Cache Healthy';

            if (health.status === 'warning') {
                statusClass = 'bg-yellow-100 dark:bg-yellow-900 text-yellow-600 dark:text-yellow-400';
                icon = 'fas fa-exclamation-triangle';
                text = 'Cache Warning';
            } else if (health.status === 'unhealthy') {
                statusClass = 'bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400';
                icon = 'fas fa-times-circle';
                text = 'Cache Unhealthy';
            }

            indicator.className = `flex items-center px-4 py-3 rounded-xl text-sm font-medium border border-gray-200 dark:border-gray-600 ${statusClass}`;
            indicator.innerHTML = `<i class="${icon} mr-2"></i><span>${text}</span>`;
        })
        .catch(error => {
            indicator.className = 'flex items-center px-4 py-3 rounded-xl text-sm font-medium bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400 border border-red-200 dark:border-red-700';
            indicator.innerHTML = '<i class="fas fa-exclamation-circle mr-2"></i><span>Cache Error</span>';
        });
}

// Load contacts for search
function loadContacts() {
    fetch('/clickup/api/contacts', {
        method: 'GET',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success && data.contacts) {
            allContacts = data.contacts;
        } else {
            console.warn('Failed to load contacts:', data.message || 'Unknown error');
        }
    })
    .catch(error => {
        console.error('Error loading contacts:', error);
        // Contacts will be loaded when assignment modal is opened if needed
    });
}

// Setup event listeners
function setupEventListeners() {
    // Search and filter functionality
    const listSearch = document.getElementById('list-search');
    const spaceFilter = document.getElementById('space-filter');
    const assignmentFilter = document.getElementById('assignment-filter');

    if (listSearch) {
        listSearch.addEventListener('input', filterLists);
    }

    if (spaceFilter) {
        spaceFilter.addEventListener('change', filterLists);
    }

    if (assignmentFilter) {
        assignmentFilter.addEventListener('change', filterLists);
    }

    // Modal event listeners
    setupModalEventListeners();
}

// Setup modal event listeners
function setupModalEventListeners() {
    // Assignment modal
    const assignmentModal = document.getElementById('assignment-modal');
    const closeAssignmentModal = document.getElementById('close-assignment-modal');
    const cancelAssignment = document.getElementById('cancel-assignment');
    const assignmentForm = document.getElementById('assignment-form');
    const contactSearch = document.getElementById('contact-search');
    const clearSelection = document.getElementById('clear-selection');

    if (closeAssignmentModal) {
        closeAssignmentModal.addEventListener('click', () => hideModal('assignment-modal'));
    }

    if (cancelAssignment) {
        cancelAssignment.addEventListener('click', () => hideModal('assignment-modal'));
    }

    if (assignmentForm) {
        assignmentForm.addEventListener('submit', handleAssignmentSubmit);
    }

    if (contactSearch) {
        contactSearch.addEventListener('input', handleContactSearch);
        contactSearch.addEventListener('focus', handleContactSearch);
    }

    if (clearSelection) {
        clearSelection.addEventListener('click', clearContactSelection);
    }

    // Unassign modal
    const closeUnassignModal = document.getElementById('close-unassign-modal');
    const cancelUnassign = document.getElementById('cancel-unassign');
    const confirmUnassign = document.getElementById('confirm-unassign');

    if (closeUnassignModal) {
        closeUnassignModal.addEventListener('click', () => hideModal('unassign-modal'));
    }

    if (cancelUnassign) {
        cancelUnassign.addEventListener('click', () => hideModal('unassign-modal'));
    }

    if (confirmUnassign) {
        confirmUnassign.addEventListener('click', handleUnassignConfirm);
    }

    // Click outside modal to close
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal-container')) {
            hideModal(e.target.id);
        }
    });
}

// Contact search functionality
function handleContactSearch(e) {
    const query = e.target.value.trim();

    if (searchTimeout) {
        clearTimeout(searchTimeout);
    }

    searchTimeout = setTimeout(() => {
        if (query.length >= 2) {
            searchContacts(query);
        } else {
            hideContactResults();
        }
    }, 300);
}

function searchContacts(query) {
    const resultsDiv = document.getElementById('contact-results');
    if (!resultsDiv) return;

    // If no contacts loaded yet, try to load them
    if (!allContacts || allContacts.length === 0) {
        resultsDiv.innerHTML = '<div class="p-4 text-center text-gray-500 dark:text-gray-400"><i class="fas fa-spinner fa-spin mr-2"></i>Loading contacts...</div>';
        resultsDiv.classList.remove('hidden');

        // Try to load contacts
        fetch('/clickup/api/contacts', {
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success && data.contacts) {
                allContacts = data.contacts;
                // Retry search with loaded contacts
                searchContacts(query);
            } else {
                resultsDiv.innerHTML = '<div class="p-4 text-center text-red-500 dark:text-red-400">Failed to load contacts</div>';
            }
        })
        .catch(error => {
            console.error('Error loading contacts:', error);
            resultsDiv.innerHTML = '<div class="p-4 text-center text-red-500 dark:text-red-400">Error loading contacts. Please try again.</div>';
        });
        return;
    }

    const normalizedQuery = query.toLowerCase();
    const filteredContacts = allContacts.filter(contact => {
        try {
            return (contact.name && contact.name.toLowerCase().includes(normalizedQuery)) ||
                   (contact.arabic_name && contact.arabic_name.toLowerCase().includes(normalizedQuery)) ||
                   (contact.email && contact.email.toLowerCase().includes(normalizedQuery)) ||
                   (contact.position && contact.position.toLowerCase().includes(normalizedQuery));
        } catch (error) {
            console.warn('Error filtering contact:', contact, error);
            return false;
        }
    });

    if (filteredContacts.length === 0) {
        resultsDiv.innerHTML = '<div class="p-4 text-center text-gray-500 dark:text-gray-400">No contacts found matching your search</div>';
    } else {
        try {
            resultsDiv.innerHTML = filteredContacts.map(contact => {
                const safeName = (contact.name || 'Unknown').replace(/'/g, "\\'").replace(/"/g, '\\"');
                return `
                    <div class="contact-result-item p-4 cursor-pointer border-b border-gray-100 dark:border-gray-600 last:border-b-0"
                         onclick="selectContact(${contact.id}, '${safeName}')">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-blue-600 dark:text-blue-400 text-sm"></i>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-900 dark:text-white">${contact.name || 'Unknown'}</div>
                                ${contact.position ? `<div class="text-xs text-gray-500 dark:text-gray-400">${contact.position}</div>` : ''}
                                ${contact.email ? `<div class="text-xs text-blue-600 dark:text-blue-400">${contact.email}</div>` : ''}
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        } catch (error) {
            console.error('Error rendering contact results:', error);
            resultsDiv.innerHTML = '<div class="p-4 text-center text-red-500 dark:text-red-400">Error displaying contacts</div>';
        }
    }

    resultsDiv.classList.remove('hidden');
}

function selectContact(contactId, contactName) {
    document.getElementById('selected-contact-id').value = contactId;
    document.getElementById('selected-contact-name').textContent = contactName;
    document.getElementById('selected-contact-display').classList.remove('hidden');
    document.getElementById('contact-search').value = '';
    document.getElementById('confirm-assignment').disabled = false;
    hideContactResults();
}

function clearContactSelection() {
    document.getElementById('selected-contact-id').value = '';
    document.getElementById('selected-contact-name').textContent = '';
    document.getElementById('selected-contact-display').classList.add('hidden');
    document.getElementById('confirm-assignment').disabled = true;
}

function hideContactResults() {
    const resultsDiv = document.getElementById('contact-results');
    if (resultsDiv) {
        resultsDiv.classList.add('hidden');
    }
}

// List assignment functions
function assignList(listId, listName) {
    document.getElementById('assignment-modal-title').textContent = `Assign "${listName}"`;
    document.getElementById('assignment-list-id').value = listId;
    clearContactSelection();
    showModal('assignment-modal');

    // Focus on search input
    setTimeout(() => {
        const searchInput = document.getElementById('contact-search');
        if (searchInput) {
            searchInput.focus();
        }
    }, 100);
}

function reassignList(listId, listName, currentContact) {
    document.getElementById('assignment-modal-title').textContent = `Reassign "${listName}" (currently assigned to ${currentContact})`;
    document.getElementById('assignment-list-id').value = listId;
    clearContactSelection();
    showModal('assignment-modal');

    // Focus on search input
    setTimeout(() => {
        const searchInput = document.getElementById('contact-search');
        if (searchInput) {
            searchInput.focus();
        }
    }, 100);
}

function unassignList(listId, contactName) {
    document.getElementById('unassign-confirmation-text').textContent =
        `Are you sure you want to unassign this list from ${contactName}?`;

    // Store the list ID for the unassign action
    document.getElementById('confirm-unassign').setAttribute('data-list-id', listId);

    showModal('unassign-modal');
}

function handleAssignmentSubmit(e) {
    e.preventDefault();

    const listId = document.getElementById('assignment-list-id').value;
    const contactId = document.getElementById('selected-contact-id').value;

    if (!contactId) {
        showMessage('Please select a contact first.', 'error');
        return;
    }

    const submitButton = document.getElementById('confirm-assignment');
    const buttonText = submitButton.querySelector('.button-text');
    const loadingIcon = submitButton.querySelector('.loading-icon');

    // Show loading state
    submitButton.disabled = true;
    buttonText.textContent = 'Assigning...';
    loadingIcon.classList.remove('hidden');

    fetch('{{ route("clickup.settings.assign-list-api") }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            list_id: listId,
            contact_id: contactId
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            showMessage('List assigned successfully!', 'success');
            hideModal('assignment-modal');
            // Refresh the page to show updated assignments
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showMessage(data.message || 'Failed to assign list. Please try again.', 'error');
        }
    })
    .catch(error => {
        console.error('Assignment error:', error);
        let errorMessage = 'An error occurred while assigning the list';

        if (error.message.includes('401')) {
            errorMessage = 'Authentication required. Please refresh the page and try again.';
        } else if (error.message.includes('403')) {
            errorMessage = 'You do not have permission to assign lists.';
        } else if (error.message.includes('404')) {
            errorMessage = 'Assignment endpoint not found. Please contact support.';
        } else if (error.message.includes('500')) {
            errorMessage = 'Server error occurred. Please try again later.';
        }

        showMessage(errorMessage, 'error');
    })
    .finally(() => {
        // Reset loading state
        submitButton.disabled = false;
        buttonText.textContent = 'Assign List';
        loadingIcon.classList.add('hidden');
    });
}

function handleUnassignConfirm() {
    const listId = document.getElementById('confirm-unassign').getAttribute('data-list-id');

    const submitButton = document.getElementById('confirm-unassign');
    const buttonText = submitButton.querySelector('.button-text');
    const loadingIcon = submitButton.querySelector('.loading-icon');

    // Show loading state
    submitButton.disabled = true;
    buttonText.textContent = 'Unassigning...';
    loadingIcon.classList.remove('hidden');

    fetch('{{ route("clickup.settings.unassign-list-api") }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            list_id: listId
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            showMessage('List unassigned successfully!', 'success');
            hideModal('unassign-modal');
            // Refresh the page to show updated assignments
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showMessage(data.message || 'Failed to unassign list. Please try again.', 'error');
        }
    })
    .catch(error => {
        console.error('Unassignment error:', error);
        let errorMessage = 'An error occurred while unassigning the list';

        if (error.message.includes('401')) {
            errorMessage = 'Authentication required. Please refresh the page and try again.';
        } else if (error.message.includes('403')) {
            errorMessage = 'You do not have permission to unassign lists.';
        } else if (error.message.includes('404')) {
            errorMessage = 'Unassignment endpoint not found. Please contact support.';
        } else if (error.message.includes('500')) {
            errorMessage = 'Server error occurred. Please try again later.';
        }

        showMessage(errorMessage, 'error');
    })
    .finally(() => {
        // Reset loading state
        submitButton.disabled = false;
        buttonText.textContent = 'Unassign';
        loadingIcon.classList.add('hidden');
    });
}

// Modal utility functions
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    }
}

function hideModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('hidden');
        document.body.style.overflow = 'auto';
    }
}

// Message display function
function showMessage(message, type) {
    const container = document.getElementById('message-container');
    if (!container) return;

    const alertClass = type === 'success' ?
        'bg-green-50 dark:bg-green-900 border-green-200 dark:border-green-700 text-green-800 dark:text-green-200' :
        'bg-red-50 dark:bg-red-900 border-red-200 dark:border-red-700 text-red-800 dark:text-red-200';

    const icon = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-circle';

    container.innerHTML = `
        <div class="${type}-message border ${alertClass} rounded-xl p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="${icon}"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium">${message}</p>
                </div>
            </div>
        </div>
    `;

    container.classList.remove('hidden');

    // Auto-hide after 5 seconds
    setTimeout(() => {
        container.classList.add('hidden');
    }, 5000);
}

// Table filtering functionality
function filterLists() {
    const searchTerm = document.getElementById('list-search').value.toLowerCase();
    const spaceFilter = document.getElementById('space-filter').value;
    const assignmentFilter = document.getElementById('assignment-filter').value;

    const rows = document.querySelectorAll('#lists-table-body tr');

    rows.forEach(row => {
        const name = row.getAttribute('data-name') || '';
        const space = row.getAttribute('data-space') || '';
        const workspace = row.getAttribute('data-workspace') || '';
        const assignment = row.getAttribute('data-assignment') || '';

        const matchesSearch = !searchTerm ||
            name.includes(searchTerm) ||
            space.toLowerCase().includes(searchTerm) ||
            workspace.toLowerCase().includes(searchTerm);

        const matchesSpace = !spaceFilter || space === spaceFilter;
        const matchesAssignment = !assignmentFilter || assignment === assignmentFilter;

        if (matchesSearch && matchesSpace && matchesAssignment) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// Table sorting functionality
function sortTable(column) {
    const tbody = document.getElementById('lists-table-body');
    const rows = Array.from(tbody.querySelectorAll('tr'));

    // Toggle sort direction
    sortDirection[column] = sortDirection[column] === 'asc' ? 'desc' : 'asc';

    rows.sort((a, b) => {
        let aValue, bValue;

        switch(column) {
            case 'name':
                aValue = a.querySelector('td:first-child .text-sm.font-semibold').textContent.toLowerCase();
                bValue = b.querySelector('td:first-child .text-sm.font-semibold').textContent.toLowerCase();
                break;
            case 'space':
                aValue = a.querySelector('td:nth-child(2) .text-sm.font-medium').textContent.toLowerCase();
                bValue = b.querySelector('td:nth-child(2) .text-sm.font-medium').textContent.toLowerCase();
                break;
            case 'status':
                aValue = a.querySelector('td:nth-child(3) span').textContent.toLowerCase();
                bValue = b.querySelector('td:nth-child(3) span').textContent.toLowerCase();
                break;
            case 'assigned':
                const aAssigned = a.getAttribute('data-assignment') === 'assigned';
                const bAssigned = b.getAttribute('data-assignment') === 'assigned';
                aValue = aAssigned ? 'assigned' : 'unassigned';
                bValue = bAssigned ? 'assigned' : 'unassigned';
                break;
            default:
                return 0;
        }

        if (sortDirection[column] === 'asc') {
            return aValue.localeCompare(bValue);
        } else {
            return bValue.localeCompare(aValue);
        }
    });

    // Clear and re-append sorted rows
    tbody.innerHTML = '';
    rows.forEach(row => tbody.appendChild(row));

    // Update sort indicators
    document.querySelectorAll('th i.fas').forEach(icon => {
        icon.className = 'fas fa-sort text-gray-400';
    });

    const currentHeader = document.querySelector(`th[onclick="sortTable('${column}')"] i`);
    if (currentHeader) {
        currentHeader.className = `fas fa-sort-${sortDirection[column] === 'asc' ? 'up' : 'down'} text-gray-400`;
    }
}

// Refresh lists functionality
function refreshLists() {
    window.location.reload();
}

// View list details (placeholder - can be implemented later)
function viewListDetails(listId) {
    showMessage('List details functionality coming soon!', 'success');
}

// Global functions for backward compatibility
window.assignList = assignList;
window.reassignList = reassignList;
window.unassignList = unassignList;
window.refreshLists = refreshLists;
window.viewListDetails = viewListDetails;
window.sortTable = sortTable;
window.selectContact = selectContact;
</script>
