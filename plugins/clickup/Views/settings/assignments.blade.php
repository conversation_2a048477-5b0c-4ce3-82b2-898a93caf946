@extends('layouts.app')

@section('title', 'ClickUp List Assignments')

@section('content')
<div class="container mx-auto px-4 py-6">
    <!-- Debug Information -->
    @if(config('app.debug'))
        <div class="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg p-4 mb-6">
            <h4 class="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">Debug Info:</h4>
            <div class="text-xs text-blue-700 dark:text-blue-300">
                <p>Product Managers: {{ $productManagers->count() }}</p>
                <p>All Lists: {{ $allLists->count() }}</p>
                <p>Unassigned Lists: {{ $unassignedLists->count() }}</p>
            </div>
        </div>
    @endif

    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">List Assignments</h1>
        <div class="flex space-x-3">
            <a href="{{ route('clickup.settings.index') }}"
               class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-key mr-2"></i>
                API Settings
            </a>
            <a href="{{ route('clickup.settings.sync') }}"
               class="bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-sync mr-2"></i>
                Sync Settings
            </a>
            <a href="{{ route('clickup.dashboard') }}"
               class="bg-gray-600 dark:bg-gray-700 hover:bg-gray-700 dark:hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Assign Lists Form -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700 mb-8">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Assign Lists to Product Manager</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Select a product manager and assign one or more lists to them.</p>
        </div>
        <div class="p-6">
            @if($productManagers->count() === 0)
                <div class="text-center py-8">
                    <i class="fas fa-users text-gray-400 text-4xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Product Managers Found</h3>
                    <p class="text-gray-500 dark:text-gray-400 mb-4">You need to create product managers before you can assign lists to them.</p>
                    <a href="{{ route('clickup.product-managers.create') }}"
                       class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600">
                        <i class="fas fa-plus mr-2"></i>
                        Create Product Manager
                    </a>
                </div>
            @elseif($allLists->count() === 0)
                <div class="text-center py-8">
                    <i class="fas fa-list text-gray-400 text-4xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Lists Found</h3>
                    <p class="text-gray-500 dark:text-gray-400 mb-4">You need to sync your ClickUp data first to get lists.</p>
                    <a href="{{ route('clickup.dashboard') }}"
                       class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-600">
                        <i class="fas fa-sync mr-2"></i>
                        Go to Dashboard & Sync
                    </a>
                </div>
            @else
                <form method="POST" action="{{ route('clickup.settings.assign-lists') }}" class="space-y-6">
                    @csrf

                <!-- Product Manager Selection -->
                <div>
                    <label for="product_manager_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Product Manager
                    </label>
                    <select name="product_manager_id" id="product_manager_id" required
                            class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Select a product manager...</option>
                        @forelse($productManagers as $manager)
                            <option value="{{ $manager->id }}" {{ old('product_manager_id') == $manager->id ? 'selected' : '' }}>
                                {{ $manager->name }} ({{ $manager->email }})
                                @if($manager->clickupLists->count() > 0)
                                    - Currently managing {{ $manager->clickupLists->count() }} lists
                                @endif
                            </option>
                        @empty
                            <option value="" disabled>No product managers available</option>
                        @endforelse
                    </select>
                    @error('product_manager_id')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Lists Selection -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                        Select Lists to Assign
                    </label>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-64 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md p-4">
                        @forelse($allLists as $list)
                            <div class="flex items-center">
                                <input type="checkbox" name="list_ids[]" value="{{ $list->id }}" id="list_{{ $list->id }}"
                                       {{ in_array($list->id, old('list_ids', [])) ? 'checked' : '' }}
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                                <label for="list_{{ $list->id }}" class="ml-2 block text-sm text-gray-900 dark:text-white">
                                    {{ $list->name }}
                                    @if($list->productManager)
                                        <span class="text-xs text-orange-600 dark:text-orange-400">
                                            (assigned to {{ $list->productManager->name }})
                                        </span>
                                    @else
                                        <span class="text-xs text-green-600 dark:text-green-400">(unassigned)</span>
                                    @endif
                                </label>
                            </div>
                        @empty
                            <p class="text-gray-500 dark:text-gray-400 text-sm">No lists available. Please sync your ClickUp data first.</p>
                        @endforelse
                    </div>
                    @error('list_ids')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                    @error('list_ids.*')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                    <!-- Submit Button -->
                    <div class="flex justify-end">
                        <button type="submit"
                                class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                            <i class="fas fa-user-plus mr-2"></i>
                            Assign Lists
                        </button>
                    </div>
                </form>
            @endif
        </div>
    </div>

    <!-- Current Assignments -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700 mb-8">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Current Assignments</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">View and manage current list assignments for each product manager.</p>
        </div>
        <div class="p-6">
            @forelse($productManagers as $manager)
                <div class="mb-6 last:mb-0">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            @if($manager->avatar_url)
                                <img src="{{ $manager->avatar_url }}" alt="{{ $manager->name }}" 
                                     class="w-8 h-8 rounded-full mr-3">
                            @else
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-white font-bold text-sm">
                                        {{ substr($manager->name, 0, 1) }}
                                    </span>
                                </div>
                            @endif
                            <div>
                                <h4 class="text-md font-medium text-gray-900 dark:text-white">{{ $manager->name }}</h4>
                                <p class="text-sm text-gray-500 dark:text-gray-400">{{ $manager->email }}</p>
                            </div>
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                            {{ $manager->clickupLists->count() }} lists assigned
                        </div>
                    </div>

                    @if($manager->clickupLists->count() > 0)
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                            @foreach($manager->clickupLists as $list)
                                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <div class="flex items-center">
                                        <i class="fas fa-list text-blue-500 mr-2"></i>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900 dark:text-white">{{ $list->name }}</div>
                                            <div class="text-xs text-gray-500 dark:text-gray-400">
                                                {{ $list->tasks->count() }} tasks • {{ ucfirst($list->status) }}
                                            </div>
                                        </div>
                                    </div>
                                    <form method="POST" action="{{ route('clickup.settings.unassign-list') }}" class="inline">
                                        @csrf
                                        <input type="hidden" name="list_id" value="{{ $list->id }}">
                                        <button type="submit" 
                                                onclick="return confirm('Are you sure you want to unassign this list?')"
                                                class="text-red-600 dark:text-red-400 hover:text-red-500 dark:hover:text-red-300 text-sm">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </form>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <i class="fas fa-inbox text-gray-400 text-2xl mb-2"></i>
                            <p class="text-sm text-gray-500 dark:text-gray-400">No lists assigned to this product manager</p>
                        </div>
                    @endif
                </div>
            @empty
                <div class="text-center py-8">
                    <i class="fas fa-users text-gray-400 text-4xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Product Managers Found</h3>
                    <p class="text-gray-500 dark:text-gray-400 mb-4">Create product managers first to assign lists to them.</p>
                    <a href="{{ route('clickup.product-managers.create') }}"
                       class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600">
                        <i class="fas fa-plus mr-2"></i>
                        Add Product Manager
                    </a>
                </div>
            @endforelse
        </div>
    </div>

    <!-- Unassigned Lists -->
    @if($unassignedLists->count() > 0)
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Unassigned Lists</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Lists that are not currently assigned to any product manager.</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    @foreach($unassignedLists as $list)
                        <div class="p-4 bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-list text-yellow-600 dark:text-yellow-400 mr-3"></i>
                                <div>
                                    <h4 class="text-sm font-medium text-gray-900 dark:text-white">{{ $list->name }}</h4>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">
                                        {{ $list->tasks->count() }} tasks • {{ ucfirst($list->status) }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    @endif
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add select all functionality
    const selectAllBtn = document.createElement('button');
    selectAllBtn.type = 'button';
    selectAllBtn.className = 'text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 text-sm mb-2';
    selectAllBtn.innerHTML = '<i class="fas fa-check-square mr-1"></i>Select All Unassigned';
    
    const listsContainer = document.querySelector('.grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3.gap-3');
    if (listsContainer) {
        listsContainer.parentNode.insertBefore(selectAllBtn, listsContainer);
        
        selectAllBtn.addEventListener('click', function() {
            const checkboxes = listsContainer.querySelectorAll('input[type="checkbox"]');
            const unassignedCheckboxes = Array.from(checkboxes).filter(cb => {
                const label = cb.nextElementSibling;
                return label && label.textContent.includes('(unassigned)');
            });
            
            const allChecked = unassignedCheckboxes.every(cb => cb.checked);
            unassignedCheckboxes.forEach(cb => cb.checked = !allChecked);
            
            this.innerHTML = allChecked ? 
                '<i class="fas fa-check-square mr-1"></i>Select All Unassigned' : 
                '<i class="fas fa-square mr-1"></i>Deselect All';
        });
    }
});
</script>
@endpush
@endsection
