@extends('layouts.app')

@section('title', 'List: ' . $list->name)

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ $list->name }}</h1>
        <div class="flex space-x-3">
            @if(auth()->user()->hasPermission('manage_clickup_lists'))
                <a href="{{ route('clickup.lists.edit', $list) }}"
                   class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-edit mr-2"></i>
                    Edit List
                </a>
            @endif
            @if($list->url)
                <a href="{{ $list->url }}" target="_blank"
                   class="bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-external-link-alt mr-2"></i>
                    View in ClickUp
                </a>
            @endif
            <a href="{{ route('clickup.lists.index') }}"
               class="bg-gray-600 dark:bg-gray-700 hover:bg-gray-700 dark:hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Lists
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2">
            <!-- List Details -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700 mb-6">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">List Details</h3>
                </div>
                <div class="p-6">
                    @if($list->description)
                        <div class="mb-6">
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Description</h4>
                            <div class="text-sm text-gray-900 dark:text-white">{{ $list->description }}</div>
                        </div>
                    @endif

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Status</h4>
                            <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full 
                                {{ $list->status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300' }}">
                                {{ ucfirst($list->status) }}
                            </span>
                        </div>

                        @if($list->priority)
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Priority</h4>
                                <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full 
                                    @switch($list->priority)
                                        @case('urgent')
                                            bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100
                                            @break
                                        @case('high')
                                            bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-100
                                            @break
                                        @case('normal')
                                            bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100
                                            @break
                                        @case('low')
                                            bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300
                                            @break
                                    @endswitch">
                                    {{ ucfirst($list->priority) }}
                                </span>
                            </div>
                        @endif

                        @if($list->due_date)
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Due Date</h4>
                                <div class="text-sm text-gray-900 dark:text-white">
                                    {{ $list->due_date->format('M d, Y') }}
                                </div>
                            </div>
                        @endif

                        @if($list->start_date)
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Start Date</h4>
                                <div class="text-sm text-gray-900 dark:text-white">
                                    {{ $list->start_date->format('M d, Y') }}
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Recent Tasks -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Recent Tasks</h3>
                        @if(auth()->user()->hasPermission('view_clickup_tasks'))
                            <a href="{{ route('clickup.tasks.index', ['list_id' => $list->id]) }}" 
                               class="text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 text-sm">
                                View All Tasks →
                            </a>
                        @endif
                    </div>
                </div>
                <div class="p-6">
                    @if($list->tasks->count() > 0)
                        <div class="space-y-4">
                            @foreach($list->tasks as $task)
                                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-{{ $task->is_completed ? 'check-circle text-green-500' : 'circle text-gray-400' }}"></i>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900 dark:text-white {{ $task->is_completed ? 'line-through' : '' }}">
                                                {{ $task->name }}
                                            </div>
                                            <div class="text-xs text-gray-500 dark:text-gray-400">
                                                {{ ucfirst($task->status) }}
                                                @if($task->priority)
                                                    • {{ ucfirst($task->priority) }} Priority
                                                @endif
                                                @if($task->due_date)
                                                    • Due {{ $task->due_date->format('M d') }}
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex space-x-2">
                                        @if(auth()->user()->hasPermission('view_clickup_tasks'))
                                            <a href="{{ route('clickup.tasks.show', $task) }}" 
                                               class="text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        @endif
                                        @if($task->url)
                                            <a href="{{ $task->url }}" target="_blank" 
                                               class="text-green-600 dark:text-green-400 hover:text-green-500 dark:hover:text-green-300">
                                                <i class="fas fa-external-link-alt"></i>
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-8">
                            <i class="fas fa-tasks text-gray-400 text-4xl mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Tasks Found</h3>
                            <p class="text-gray-500 dark:text-gray-400">This list doesn't have any tasks yet.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">
            <!-- Product Manager -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700 mb-6">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Product Manager</h3>
                </div>
                <div class="p-6">
                    @if($list->productManager)
                        <div class="flex items-center space-x-3">
                            @if($list->productManager->avatar_url)
                                <img src="{{ $list->productManager->avatar_url }}" alt="{{ $list->productManager->name }}" 
                                     class="w-10 h-10 rounded-full">
                            @else
                                <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                                    <span class="text-white font-bold">
                                        {{ substr($list->productManager->name, 0, 1) }}
                                    </span>
                                </div>
                            @endif
                            <div>
                                <div class="text-sm font-medium text-gray-900 dark:text-white">
                                    {{ $list->productManager->name }}
                                </div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    {{ $list->productManager->email }}
                                </div>
                                @if($list->productManager->role)
                                    <div class="text-xs text-gray-500 dark:text-gray-400">
                                        {{ $list->productManager->role }}
                                    </div>
                                @endif
                            </div>
                        </div>
                        @if(auth()->user()->hasPermission('view_clickup_product_managers'))
                            <div class="mt-4">
                                <a href="{{ route('clickup.product-managers.show', $list->productManager) }}" 
                                   class="text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 text-sm">
                                    View Profile →
                                </a>
                            </div>
                        @endif
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-user-slash text-gray-400 text-2xl mb-2"></i>
                            <p class="text-sm text-gray-500 dark:text-gray-400">No product manager assigned</p>
                            @if(auth()->user()->hasPermission('manage_clickup_lists'))
                                <button class="mt-2 text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 text-sm">
                                    Assign Manager
                                </button>
                            @endif
                        </div>
                    @endif
                </div>
            </div>

            <!-- Performance Metrics -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700 mb-6">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Performance Metrics</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-500 dark:text-gray-400">Total Tasks</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $metrics['total_tasks'] }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-500 dark:text-gray-400">Active Tasks</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $metrics['active_tasks'] }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-500 dark:text-gray-400">Completed Tasks</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $metrics['completed_tasks'] }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-500 dark:text-gray-400">Overdue Tasks</span>
                            <span class="text-sm font-medium text-red-600 dark:text-red-400">{{ $metrics['overdue_tasks'] }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-500 dark:text-gray-400">Completion Rate</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $metrics['completion_rate'] }}%</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-500 dark:text-gray-400">Avg Task Age</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $metrics['average_task_age'] }} days</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sync Information -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Sync Information</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Last Synced</h4>
                            <div class="text-sm text-gray-900 dark:text-white">
                                {{ $list->last_synced_at ? $list->last_synced_at->diffForHumans() : 'Never' }}
                            </div>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Sync Status</h4>
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {{ $list->sync_status === 'success' ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 
                                   ($list->sync_status === 'failed' ? 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100' : 
                                    'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100') }}">
                                {{ ucfirst($list->sync_status) }}
                            </span>
                        </div>
                        @if($list->clickup_id)
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">ClickUp ID</h4>
                                <div class="text-sm text-gray-900 dark:text-white font-mono">{{ $list->clickup_id }}</div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
