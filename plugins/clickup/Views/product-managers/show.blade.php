@extends('layouts.app')

@section('title', 'Product Manager Details')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ $manager->name }}</h1>
        <div class="flex space-x-3">
            @if(auth()->user()->hasPermission('manage_clickup_product_managers'))
                <a href="{{ route('clickup.product-managers.edit', $manager) }}"
                   class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-edit mr-2"></i>
                    Edit
                </a>
            @endif
            <a href="{{ route('clickup.product-managers.index') }}"
               class="bg-gray-600 dark:bg-gray-700 hover:bg-gray-700 dark:hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Product Managers
            </a>
        </div>
    </div>

    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="bg-green-100 dark:bg-green-900 border border-green-400 dark:border-green-600 text-green-700 dark:text-green-300 px-4 py-3 rounded mb-6">
            <i class="fas fa-check-circle mr-2"></i>
            {{ session('success') }}
        </div>
    @endif

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Manager Information -->
        <div class="lg:col-span-2">
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700 mb-6">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Manager Information</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Name</label>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $manager->name }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Email</label>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white">
                                <a href="mailto:{{ $manager->email }}" class="text-blue-600 dark:text-blue-400 hover:underline">
                                    {{ $manager->email }}
                                </a>
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Role</label>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $manager->role ?: 'Not specified' }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Department</label>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $manager->department ?: 'Not specified' }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Phone</label>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white">
                                @if($manager->phone)
                                    <a href="tel:{{ $manager->phone }}" class="text-blue-600 dark:text-blue-400 hover:underline">
                                        {{ $manager->phone }}
                                    </a>
                                @else
                                    Not specified
                                @endif
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Timezone</label>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $manager->timezone ?: 'UTC' }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Status</label>
                            <p class="mt-1">
                                @if($manager->is_active)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                                        <i class="fas fa-check-circle mr-1"></i>
                                        Active
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200">
                                        <i class="fas fa-times-circle mr-1"></i>
                                        Inactive
                                    </span>
                                @endif
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Associated User</label>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white">
                                @if($manager->user)
                                    {{ $manager->user->name }}
                                @else
                                    Standalone manager
                                @endif
                            </p>
                        </div>
                        @if($manager->notes)
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Notes</label>
                                <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $manager->notes }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Assigned Lists -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Assigned Lists</h3>
                </div>
                <div class="p-6">
                    @if($manager->clickupLists && $manager->clickupLists->count() > 0)
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            @foreach($manager->clickupLists as $list)
                                <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                                    <div class="flex items-center justify-between">
                                        <h4 class="font-medium text-gray-900 dark:text-white">{{ $list->name }}</h4>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium" 
                                              style="background-color: {{ $list->color }}20; color: {{ $list->color }};">
                                            {{ ucfirst($list->status) }}
                                        </span>
                                    </div>
                                    @if($list->description)
                                        <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">{{ $list->description }}</p>
                                    @endif
                                    <div class="flex items-center justify-between mt-3 text-xs text-gray-500 dark:text-gray-400">
                                        <span>{{ $list->tasks_count ?? 0 }} tasks</span>
                                        <a href="{{ route('clickup.lists.show', $list) }}" 
                                           class="text-blue-600 dark:text-blue-400 hover:underline">
                                            View Details
                                        </a>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-8">
                            <i class="fas fa-list-ul text-4xl text-gray-400 dark:text-gray-600 mb-4"></i>
                            <p class="text-gray-500 dark:text-gray-400">No lists assigned to this product manager yet.</p>
                            @if(auth()->user()->hasPermission('manage_clickup_settings'))
                                <a href="{{ route('clickup.settings.assignments') }}" 
                                   class="mt-4 inline-flex items-center text-blue-600 dark:text-blue-400 hover:underline">
                                    <i class="fas fa-plus mr-2"></i>
                                    Assign Lists
                                </a>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Performance Metrics & Actions -->
        <div class="lg:col-span-1">
            <!-- Performance Metrics -->
            @if(isset($metrics) && !empty($metrics))
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700 mb-6">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Performance Metrics</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            @foreach($metrics as $key => $value)
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500 dark:text-gray-400">{{ ucwords(str_replace('_', ' ', $key)) }}</span>
                                    <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $value }}</span>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif

            <!-- Quick Actions -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700 mb-6">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Quick Actions</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-3">
                        @if(auth()->user()->hasPermission('manage_clickup_settings'))
                            <a href="{{ route('clickup.settings.assignments') }}" 
                               class="w-full bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out text-center block">
                                <i class="fas fa-tasks mr-2"></i>
                                Manage List Assignments
                            </a>
                        @endif
                        
                        <a href="mailto:{{ $manager->email }}" 
                           class="w-full bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out text-center block">
                            <i class="fas fa-envelope mr-2"></i>
                            Send Email
                        </a>
                        
                        @if($manager->phone)
                            <a href="tel:{{ $manager->phone }}" 
                               class="w-full bg-purple-600 dark:bg-purple-700 hover:bg-purple-700 dark:hover:bg-purple-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out text-center block">
                                <i class="fas fa-phone mr-2"></i>
                                Call
                            </a>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Manager Details -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Details</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-3 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-500 dark:text-gray-400">Created</span>
                            <span class="text-gray-900 dark:text-white">{{ $manager->created_at->format('M j, Y') }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-500 dark:text-gray-400">Last Updated</span>
                            <span class="text-gray-900 dark:text-white">{{ $manager->updated_at->format('M j, Y') }}</span>
                        </div>
                        @if($manager->creator)
                            <div class="flex justify-between">
                                <span class="text-gray-500 dark:text-gray-400">Created By</span>
                                <span class="text-gray-900 dark:text-white">{{ $manager->creator->name }}</span>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
