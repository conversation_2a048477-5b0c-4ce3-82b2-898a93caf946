@extends('layouts.app')

@section('title', 'Task: ' . $task->name)

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ $task->name }}</h1>
        <div class="flex space-x-3">
            @if(auth()->user()->hasPermission('sync_clickup_data'))
                <button onclick="syncTask({{ $task->id }})" 
                        class="bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-sync mr-2"></i>
                    Sync Task
                </button>
            @endif
            @if($task->url)
                <a href="{{ $task->url }}" target="_blank"
                   class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-external-link-alt mr-2"></i>
                    View in ClickUp
                </a>
            @endif
            <a href="{{ route('clickup.tasks.index') }}"
               class="bg-gray-600 dark:bg-gray-700 hover:bg-gray-700 dark:hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Tasks
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Task Information -->
        <div class="lg:col-span-2">
            <!-- Task Details -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700 mb-6">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Task Details</h3>
                </div>
                <div class="p-6">
                    @if($task->description)
                        <div class="mb-6">
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Description</h4>
                            <div class="text-sm text-gray-900 dark:text-white whitespace-pre-wrap">{{ $task->description }}</div>
                        </div>
                    @endif

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Status</h4>
                            <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full 
                                @switch($task->status)
                                    @case('complete')
                                    @case('closed')
                                        bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100
                                        @break
                                    @case('in progress')
                                        bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100
                                        @break
                                    @case('review')
                                        bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100
                                        @break
                                    @default
                                        bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300
                                @endswitch">
                                {{ ucfirst($task->status) }}
                            </span>
                        </div>

                        @if($task->priority)
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Priority</h4>
                                <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full 
                                    @switch($task->priority)
                                        @case('urgent')
                                            bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100
                                            @break
                                        @case('high')
                                            bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-100
                                            @break
                                        @case('normal')
                                            bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100
                                            @break
                                        @case('low')
                                            bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300
                                            @break
                                    @endswitch">
                                    {{ ucfirst($task->priority) }}
                                </span>
                            </div>
                        @endif

                        @if($task->due_date)
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Due Date</h4>
                                <div class="text-sm {{ $task->is_overdue ? 'text-red-600 dark:text-red-400' : 'text-gray-900 dark:text-white' }}">
                                    {{ $task->due_date->format('M d, Y H:i') }}
                                    @if($task->is_overdue)
                                        <span class="ml-2 text-red-600 dark:text-red-400">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            Overdue
                                        </span>
                                    @endif
                                </div>
                            </div>
                        @endif

                        @if($task->time_remaining)
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Time Remaining</h4>
                                <div class="text-sm text-gray-900 dark:text-white">{{ $task->time_remaining }}</div>
                            </div>
                        @endif
                    </div>

                    @if($task->assignee_names && count($task->assignee_names) > 0)
                        <div class="mt-6">
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Assignees</h4>
                            <div class="flex flex-wrap gap-2">
                                @foreach($task->assignee_names as $assignee)
                                    <span class="px-3 py-1 bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-100 text-sm rounded-full">
                                        {{ $assignee }}
                                    </span>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    @if($task->tags && count($task->tags) > 0)
                        <div class="mt-6">
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Tags</h4>
                            <div class="flex flex-wrap gap-2">
                                @foreach($task->tags as $tag)
                                    <span class="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300 text-sm rounded-full">
                                        {{ $tag['name'] ?? $tag }}
                                    </span>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Subtasks -->
            @if($task->subtasks->count() > 0)
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Subtasks ({{ $task->subtasks->count() }})</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-3">
                            @foreach($task->subtasks as $subtask)
                                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-{{ $subtask->is_completed ? 'check-circle text-green-500' : 'circle text-gray-400' }}"></i>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900 dark:text-white {{ $subtask->is_completed ? 'line-through' : '' }}">
                                                {{ $subtask->name }}
                                            </div>
                                            <div class="text-xs text-gray-500 dark:text-gray-400">
                                                {{ ucfirst($subtask->status) }}
                                            </div>
                                        </div>
                                    </div>
                                    @if($subtask->url)
                                        <a href="{{ $subtask->url }}" target="_blank" 
                                           class="text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300">
                                            <i class="fas fa-external-link-alt"></i>
                                        </a>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">
            <!-- List Information -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700 mb-6">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">List Information</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">List</h4>
                            <div class="text-sm text-gray-900 dark:text-white">{{ $task->list->name }}</div>
                        </div>
                        @if($task->list->productManager)
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Product Manager</h4>
                                <div class="text-sm text-gray-900 dark:text-white">{{ $task->list->productManager->name }}</div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">{{ $task->list->productManager->email }}</div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Task Metrics -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700 mb-6">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Task Metrics</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Progress</h4>
                            <div class="flex items-center">
                                <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 mr-2">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $task->progress_percentage }}%"></div>
                                </div>
                                <span class="text-sm text-gray-900 dark:text-white">{{ $task->progress_percentage }}%</span>
                            </div>
                        </div>

                        @if($task->age_in_days)
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Age</h4>
                                <div class="text-sm text-gray-900 dark:text-white">{{ $task->age_in_days }} days</div>
                            </div>
                        @endif

                        @if($task->completion_time)
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Completion Time</h4>
                                <div class="text-sm text-gray-900 dark:text-white">{{ $task->completion_time }} hours</div>
                            </div>
                        @endif

                        @if($task->points)
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Points</h4>
                                <div class="text-sm text-gray-900 dark:text-white">{{ $task->points }}</div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Timestamps -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Timeline</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        @if($task->date_created)
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Created</h4>
                                <div class="text-sm text-gray-900 dark:text-white">
                                    {{ \Carbon\Carbon::parse($task->date_created)->format('M d, Y H:i') }}
                                </div>
                            </div>
                        @endif

                        @if($task->date_updated)
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Last Updated</h4>
                                <div class="text-sm text-gray-900 dark:text-white">
                                    {{ \Carbon\Carbon::parse($task->date_updated)->diffForHumans() }}
                                </div>
                            </div>
                        @endif

                        @if($task->date_closed)
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Completed</h4>
                                <div class="text-sm text-gray-900 dark:text-white">
                                    {{ \Carbon\Carbon::parse($task->date_closed)->format('M d, Y H:i') }}
                                </div>
                            </div>
                        @endif

                        @if($task->last_synced_at)
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Last Synced</h4>
                                <div class="text-sm text-gray-900 dark:text-white">
                                    {{ $task->last_synced_at->diffForHumans() }}
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Sync individual task
window.syncTask = function(taskId) {
    fetch(`/clickup/tasks/${taskId}/sync`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Task marked for sync successfully');
            location.reload();
        } else {
            alert('Sync failed: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Sync failed. Please try again.');
    });
};
</script>
@endpush
@endsection
