<?php

/**
 * ClickUp Cache Implementation Validation Script
 * 
 * This script validates that the caching implementation is working correctly
 * and provides performance benchmarks.
 */

require_once __DIR__ . '/../../../bootstrap/app.php';

use Illuminate\Support\Facades\Cache;
use Plugins\ClickUp\Services\ClickUpApiService;
use Plugins\ClickUp\Services\ClickUpCacheService;
use Plugins\ClickUp\Services\ClickUpMonitoringService;
use Plugins\ClickUp\Models\ClickUpApiToken;
use Plugins\ClickUp\Models\ClickUpList;

class CacheValidation
{
    private $results = [];
    
    public function run()
    {
        echo "🚀 ClickUp Cache Implementation Validation\n";
        echo "==========================================\n\n";
        
        $this->testCacheBasicFunctionality();
        $this->testCacheService();
        $this->testMonitoringService();
        $this->testPerformanceImprovement();
        $this->displayResults();
    }
    
    private function testCacheBasicFunctionality()
    {
        echo "📋 Testing Basic Cache Functionality...\n";
        
        try {
            // Test basic cache operations
            $testKey = 'clickup_validation_test';
            $testValue = ['test' => 'data', 'timestamp' => time()];
            
            Cache::put($testKey, $testValue, 60);
            $retrieved = Cache::get($testKey);
            Cache::forget($testKey);
            
            if ($retrieved === $testValue) {
                $this->results['cache_basic'] = '✅ PASS - Basic cache operations working';
            } else {
                $this->results['cache_basic'] = '❌ FAIL - Basic cache operations failed';
            }
            
        } catch (Exception $e) {
            $this->results['cache_basic'] = '❌ FAIL - Cache error: ' . $e->getMessage();
        }
    }
    
    private function testCacheService()
    {
        echo "🔧 Testing ClickUp Cache Service...\n";
        
        try {
            // Get active token or create a test one
            $token = ClickUpApiToken::getActiveToken();
            if (!$token) {
                $this->results['cache_service'] = '⚠️  SKIP - No active ClickUp API token found';
                return;
            }
            
            $apiService = new ClickUpApiService($token);
            $cacheService = $apiService->getCacheServiceInstance();
            
            // Test cache health check
            $health = $cacheService->checkCacheHealth();
            if (isset($health['status'])) {
                $this->results['cache_health'] = "✅ PASS - Cache health check: {$health['status']}";
            } else {
                $this->results['cache_health'] = '❌ FAIL - Cache health check failed';
            }
            
            // Test cache statistics
            $stats = $cacheService->getCacheStats();
            if (isset($stats['cache_info'])) {
                $this->results['cache_stats'] = '✅ PASS - Cache statistics working';
            } else {
                $this->results['cache_stats'] = '❌ FAIL - Cache statistics failed';
            }
            
            // Test cache operations if we have lists
            $listCount = ClickUpList::count();
            if ($listCount > 0) {
                $cacheResult = $cacheService->cacheAllLists();
                if ($cacheResult['success']) {
                    $this->results['cache_operations'] = "✅ PASS - Cached {$cacheResult['cached']} lists";
                } else {
                    $this->results['cache_operations'] = '❌ FAIL - Cache operations failed';
                }
            } else {
                $this->results['cache_operations'] = '⚠️  SKIP - No lists found to cache';
            }
            
        } catch (Exception $e) {
            $this->results['cache_service'] = '❌ FAIL - Cache service error: ' . $e->getMessage();
        }
    }
    
    private function testMonitoringService()
    {
        echo "📊 Testing Monitoring Service...\n";
        
        try {
            $monitoringService = new ClickUpMonitoringService();
            
            // Test performance metric recording
            $monitoringService->recordPerformanceMetric('test_operation', 0.123, true);
            $this->results['monitoring_record'] = '✅ PASS - Performance metric recording';
            
            // Test performance metrics retrieval
            $metrics = $monitoringService->getPerformanceMetrics(1);
            if (isset($metrics['assignment'])) {
                $this->results['monitoring_metrics'] = '✅ PASS - Performance metrics retrieval';
            } else {
                $this->results['monitoring_metrics'] = '❌ FAIL - Performance metrics retrieval failed';
            }
            
            // Test system health metrics
            $health = $monitoringService->getSystemHealthMetrics();
            if (isset($health['status']) && isset($health['metrics'])) {
                $this->results['monitoring_health'] = "✅ PASS - System health: {$health['status']}";
            } else {
                $this->results['monitoring_health'] = '❌ FAIL - System health metrics failed';
            }
            
            // Test efficiency report
            $efficiency = $monitoringService->getCacheEfficiencyReport();
            if (isset($efficiency['summary']) && isset($efficiency['trends'])) {
                $this->results['monitoring_efficiency'] = '✅ PASS - Cache efficiency report';
            } else {
                $this->results['monitoring_efficiency'] = '❌ FAIL - Cache efficiency report failed';
            }
            
        } catch (Exception $e) {
            $this->results['monitoring_service'] = '❌ FAIL - Monitoring service error: ' . $e->getMessage();
        }
    }
    
    private function testPerformanceImprovement()
    {
        echo "⚡ Testing Performance Improvement...\n";
        
        try {
            // Create a test list if none exist
            $testList = ClickUpList::first();
            if (!$testList) {
                $testList = ClickUpList::create([
                    'clickup_id' => 'validation_test_' . time(),
                    'name' => 'Validation Test List',
                    'description' => 'Created for validation testing',
                    'space_id' => 'test_space',
                    'team_id' => 'test_team',
                    'status' => 'active',
                    'is_private' => false,
                    'is_archived' => false,
                    'task_count' => 0,
                    'last_synced_at' => now(),
                    'sync_status' => 'success',
                    'created_by' => 1
                ]);
            }
            
            $token = ClickUpApiToken::getActiveToken();
            if (!$token) {
                $this->results['performance'] = '⚠️  SKIP - No active API token for performance test';
                return;
            }
            
            $apiService = new ClickUpApiService($token);
            $cacheService = $apiService->getCacheServiceInstance();
            
            // Pre-cache the list
            $cacheService->cacheAllLists();
            
            // Measure cached access time
            $iterations = 10;
            $totalTime = 0;
            
            for ($i = 0; $i < $iterations; $i++) {
                $startTime = microtime(true);
                $result = $cacheService->getList($testList->clickup_id);
                $endTime = microtime(true);
                
                if ($result['success']) {
                    $totalTime += ($endTime - $startTime);
                }
            }
            
            $avgTime = $totalTime / $iterations;
            $avgTimeMs = round($avgTime * 1000, 2);
            
            if ($avgTimeMs < 50) { // Should be under 50ms for cached access
                $this->results['performance'] = "✅ PASS - Average cached access: {$avgTimeMs}ms";
            } else {
                $this->results['performance'] = "⚠️  SLOW - Average cached access: {$avgTimeMs}ms (expected <50ms)";
            }
            
        } catch (Exception $e) {
            $this->results['performance'] = '❌ FAIL - Performance test error: ' . $e->getMessage();
        }
    }
    
    private function displayResults()
    {
        echo "\n📋 Validation Results\n";
        echo "====================\n";
        
        $passed = 0;
        $failed = 0;
        $skipped = 0;
        
        foreach ($this->results as $test => $result) {
            echo "{$result}\n";
            
            if (strpos($result, '✅ PASS') === 0) {
                $passed++;
            } elseif (strpos($result, '❌ FAIL') === 0) {
                $failed++;
            } elseif (strpos($result, '⚠️  SKIP') === 0 || strpos($result, '⚠️  SLOW') === 0) {
                $skipped++;
            }
        }
        
        echo "\n📊 Summary\n";
        echo "==========\n";
        echo "✅ Passed: {$passed}\n";
        echo "❌ Failed: {$failed}\n";
        echo "⚠️  Skipped/Warnings: {$skipped}\n";
        
        if ($failed === 0) {
            echo "\n🎉 All critical tests passed! Cache implementation is working correctly.\n";
        } else {
            echo "\n⚠️  Some tests failed. Please check the implementation.\n";
        }
        
        echo "\n💡 Next Steps:\n";
        echo "- Visit http://business.test/clickup/settings to see the new Cache Management tab\n";
        echo "- Test contact assignment to see performance improvements\n";
        echo "- Monitor cache statistics in the Cache Management interface\n";
    }
}

// Run the validation
$validation = new CacheValidation();
$validation->run();
