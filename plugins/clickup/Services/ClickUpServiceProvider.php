<?php

namespace Plugins\ClickUp\Services;

use Illuminate\Support\ServiceProvider;
use Plugins\ClickUp\Services\ClickUpApiService;
use Plugins\ClickUp\Services\ClickUpSyncService;

class ClickUpServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register ClickUp API Service as singleton
        $this->app->singleton(ClickUpApiService::class, function ($app) {
            return new ClickUpApiService();
        });

        // Register ClickUp Sync Service
        $this->app->bind(ClickUpSyncService::class, function ($app) {
            return new ClickUpSyncService($app->make(ClickUpApiService::class));
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register any additional services or configurations here
    }
}
