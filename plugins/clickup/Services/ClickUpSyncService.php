<?php

namespace Plugins\ClickUp\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Plugins\ClickUp\Models\ClickUpApiToken;
use Plugins\ClickUp\Models\ClickUpList;
use Plugins\ClickUp\Models\ClickUpTask;
use Plugins\ClickUp\Models\ClickUpProductManager;

class ClickUpSyncService
{
    private ClickUpApiService $apiService;

    public function __construct(ClickUpApiService $apiService)
    {
        $this->apiService = $apiService;
    }

    /**
     * Sync all data from ClickUp
     */
    public function syncAll(): array
    {
        if (!$this->apiService->isConfigured()) {
            return [
                'success' => false,
                'message' => 'ClickUp API not configured'
            ];
        }

        $results = [
            'lists' => $this->syncLists(),
            'tasks' => $this->syncTasks()
        ];

        $totalSynced = array_sum(array_column($results, 'synced'));
        $totalErrors = array_sum(array_column($results, 'errors'));

        // Consider sync successful if we synced items and errors are less than 20% of total
        $totalItems = $totalSynced + $totalErrors;
        $errorRate = $totalItems > 0 ? ($totalErrors / $totalItems) * 100 : 0;
        $isSuccessful = $totalSynced > 0 && $errorRate < 20;

        $message = "Sync completed. {$totalSynced} items synced";
        if ($totalErrors > 0) {
            $message .= ", {$totalErrors} errors";
        }
        $message .= ".";

        return [
            'success' => $isSuccessful,
            'message' => $message,
            'results' => $results,
            'stats' => [
                'total_synced' => $totalSynced,
                'total_errors' => $totalErrors,
                'error_rate' => round($errorRate, 1)
            ]
        ];
    }

    /**
     * Sync lists from ClickUp
     */
    public function syncLists(): array
    {
        try {
            // Get selected workspace and spaces from settings
            $token = ClickUpApiToken::getActiveToken();
            $selectedWorkspace = $token && isset($token->metadata['selected_workspace'])
                ? $token->metadata['selected_workspace']
                : null;
            $selectedSpaces = $token && isset($token->metadata['selected_spaces'])
                ? $token->metadata['selected_spaces']
                : [];

            if (!$selectedWorkspace) {
                return [
                    'success' => false,
                    'message' => 'No workspace selected. Please select a workspace in settings first.',
                    'synced' => 0,
                    'errors' => 1
                ];
            }

            $synced = 0;
            $errors = 0;
            $allLists = [];

            // Get lists from selected workspace/spaces
            if (empty($selectedSpaces)) {
                // Get all spaces in workspace if none selected
                $spacesResponse = $this->apiService->getSpaces($selectedWorkspace);
                if ($spacesResponse['success']) {
                    $spaces = $spacesResponse['data']['spaces'] ?? [];
                    foreach ($spaces as $space) {
                        $listsResponse = $this->apiService->getListsInSpace($space['id']);
                        if ($listsResponse['success']) {
                            $allLists = array_merge($allLists, $listsResponse['data']['lists'] ?? []);
                        } else {
                            $errors++;
                        }
                    }
                } else {
                    return [
                        'success' => false,
                        'message' => 'Failed to fetch spaces: ' . $spacesResponse['message'],
                        'synced' => 0,
                        'errors' => 1
                    ];
                }
            } else {
                // Get lists from selected spaces only
                foreach ($selectedSpaces as $spaceId) {
                    $listsResponse = $this->apiService->getListsInSpace($spaceId);
                    if ($listsResponse['success']) {
                        $allLists = array_merge($allLists, $listsResponse['data']['lists'] ?? []);
                    } else {
                        $errors++;
                    }
                }
            }

            // Process all collected lists
            foreach ($allLists as $listData) {
                if ($this->syncList($listData, $selectedWorkspace, $listData['space']['id'] ?? null)) {
                    $synced++;
                } else {
                    $errors++;
                }
            }

            return [
                'success' => true,
                'message' => "Lists sync completed. {$synced} lists synced, {$errors} errors.",
                'synced' => $synced,
                'errors' => $errors
            ];

        } catch (\Exception $e) {
            Log::error('ClickUp lists sync failed', ['error' => $e->getMessage()]);
            
            return [
                'success' => false,
                'message' => 'Lists sync failed: ' . $e->getMessage(),
                'synced' => 0,
                'errors' => 1
            ];
        }
    }

    /**
     * Sync a single list
     */
    private function syncList(array $listData, string $teamId, string $spaceId): bool
    {
        try {
            $list = ClickUpList::updateOrCreate(
                ['clickup_id' => $listData['id']],
                [
                    'name' => $listData['name'],
                    'description' => $listData['content'] ?? null,
                    'folder_id' => $listData['folder']['id'] ?? null,
                    'space_id' => $spaceId,
                    'team_id' => $teamId,
                    'status' => $listData['status']['status'] ?? 'active',
                    'priority' => $listData['priority']['priority'] ?? null,
                    'color' => $listData['priority']['color'] ?? null,
                    'avatar_url' => $listData['avatar']['url'] ?? null,
                    'is_private' => $listData['private'] ?? false,
                    'is_archived' => $listData['archived'] ?? false,
                    'task_count' => $listData['task_count'] ?? 0,
                    'due_date' => isset($listData['due_date']) ? 
                        \Carbon\Carbon::createFromTimestampMs($listData['due_date']) : null,
                    'start_date' => isset($listData['start_date']) ? 
                        \Carbon\Carbon::createFromTimestampMs($listData['start_date']) : null,
                    'permission_level' => $listData['permission_level'] ?? null,
                    'metadata' => [
                        'clickup_data' => $listData,
                        'last_sync' => now()->toISOString()
                    ],
                    'created_by' => auth()->id() ?? 1
                ]
            );

            $list->markAsSynced();
            return true;

        } catch (\Exception $e) {
            Log::error('Failed to sync ClickUp list', [
                'list_id' => $listData['id'],
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Sync tasks from ClickUp
     */
    public function syncTasks(): array
    {
        try {
            $lists = ClickUpList::active()->get();
            $synced = 0;
            $errors = 0;

            foreach ($lists as $list) {
                $tasksResponse = $this->apiService->getTasks($list->clickup_id);
                
                if (!$tasksResponse['success']) {
                    $errors++;
                    $list->markSyncFailed($tasksResponse['message']);
                    continue;
                }

                $tasks = $tasksResponse['data']['tasks'] ?? [];
                
                foreach ($tasks as $taskData) {
                    if ($this->syncTask($taskData, $list->id)) {
                        $synced++;
                    } else {
                        $errors++;
                    }
                }

                // Update list task count
                $list->update(['task_count' => count($tasks)]);
                $list->markAsSynced();
            }

            return [
                'success' => true,
                'message' => "Tasks sync completed. {$synced} tasks synced, {$errors} errors.",
                'synced' => $synced,
                'errors' => $errors
            ];

        } catch (\Exception $e) {
            Log::error('ClickUp tasks sync failed', ['error' => $e->getMessage()]);
            
            return [
                'success' => false,
                'message' => 'Tasks sync failed: ' . $e->getMessage(),
                'synced' => 0,
                'errors' => 1
            ];
        }
    }

    /**
     * Sync a single task
     */
    private function syncTask(array $taskData, int $listId): bool
    {
        try {
            $task = ClickUpTask::updateOrCreate(
                ['clickup_id' => $taskData['id']],
                [
                    'list_id' => $listId,
                    'name' => $taskData['name'],
                    'description' => $taskData['description'] ?? null,
                    'status' => $taskData['status']['status'] ?? 'open',
                    'priority' => $taskData['priority']['priority'] ?? null,
                    'assignees' => $taskData['assignees'] ?? [],
                    'watchers' => $taskData['watchers'] ?? [],
                    'tags' => $taskData['tags'] ?? [],
                    'due_date' => isset($taskData['due_date']) ? 
                        \Carbon\Carbon::createFromTimestampMs($taskData['due_date']) : null,
                    'start_date' => isset($taskData['start_date']) ? 
                        \Carbon\Carbon::createFromTimestampMs($taskData['start_date']) : null,
                    'date_created' => isset($taskData['date_created']) ? 
                        \Carbon\Carbon::createFromTimestampMs($taskData['date_created']) : null,
                    'date_updated' => isset($taskData['date_updated']) ? 
                        \Carbon\Carbon::createFromTimestampMs($taskData['date_updated']) : null,
                    'date_closed' => isset($taskData['date_closed']) ? 
                        \Carbon\Carbon::createFromTimestampMs($taskData['date_closed']) : null,
                    'creator_id' => $taskData['creator']['id'] ?? null,
                    'url' => $taskData['url'] ?? null,
                    'parent_task_id' => $taskData['parent'] ?? null,
                    'order_index' => $taskData['orderindex'] ?? null,
                    'points' => $taskData['points'] ?? null,
                    'time_estimate' => $taskData['time_estimate'] ?? null,
                    'time_spent' => $taskData['time_spent'] ?? null,
                    'custom_fields' => $taskData['custom_fields'] ?? [],
                    'attachments' => $taskData['attachments'] ?? [],
                    'dependencies' => $taskData['dependencies'] ?? [],
                    'metadata' => [
                        'clickup_data' => $taskData,
                        'last_sync' => now()->toISOString()
                    ]
                ]
            );

            $task->markAsSynced();
            return true;

        } catch (\Exception $e) {
            Log::error('Failed to sync ClickUp task', [
                'task_id' => $taskData['id'],
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get sync status
     */
    public function getSyncStatus(): array
    {
        $lists = ClickUpList::count();
        $tasks = ClickUpTask::count();
        $lastSync = ClickUpList::max('last_synced_at');
        $failedSyncs = ClickUpList::where('sync_status', 'failed')->count();

        return [
            'total_lists' => $lists,
            'total_tasks' => $tasks,
            'last_sync' => $lastSync,
            'failed_syncs' => $failedSyncs,
            'api_configured' => $this->apiService->isConfigured(),
            'api_stats' => $this->apiService->getApiUsageStats()
        ];
    }
}
