{"name": "clickup", "version": "1.0.0", "enabled": true, "dependencies": ["business"], "description": "ClickUp integration for project management, task tracking, and performance reporting with product manager assignments", "system": false, "category": "Project Management", "permissions": ["manage_clickup_integration", "view_clickup_data", "manage_clickup_lists", "view_clickup_lists", "manage_clickup_tasks", "view_clickup_tasks", "manage_clickup_product_managers", "view_clickup_product_managers", "view_clickup_reports", "manage_clickup_settings", "sync_clickup_data"], "author": "Business Application", "license": "MIT", "requirements": {"php": ">=8.1", "laravel": ">=10.0"}, "settings": {"api_token_required": true, "sync_interval": 3600, "rate_limit": 100, "cache_duration": 1800}, "navigation": {"label": "ClickUp", "icon": "fas fa-tasks", "route": "clickup.dashboard", "permissions": ["view_clickup_data"], "subnav": [{"label": "Dashboard", "icon": "fas fa-tachometer-alt", "route": "clickup.dashboard", "permissions": ["view_clickup_data"]}, {"label": "Lists", "icon": "fas fa-list", "route": "clickup.lists.index", "permissions": ["view_clickup_lists"]}, {"label": "Tasks", "icon": "fas fa-check-square", "route": "clickup.tasks.index", "permissions": ["view_clickup_tasks"]}, {"label": "Product Managers", "icon": "fas fa-users", "route": "clickup.product-managers.index", "permissions": ["view_clickup_product_managers"]}, {"label": "Reports", "icon": "fas fa-chart-bar", "route": "clickup.reports.index", "permissions": ["view_clickup_reports"], "subnav": [{"label": "Overview", "icon": "fas fa-tachometer-alt", "route": "clickup.reports.index", "permissions": ["view_clickup_reports"]}, {"label": "Performance", "icon": "fas fa-chart-line", "route": "clickup.reports.performance", "permissions": ["view_clickup_reports"]}, {"label": "Productivity", "icon": "fas fa-rocket", "route": "clickup.reports.productivity", "permissions": ["view_clickup_reports"]}, {"label": "Bugs & Features", "icon": "fas fa-bug", "route": "clickup.reports.bugs-features", "permissions": ["view_clickup_reports"]}, {"label": "Time Tracking", "icon": "fas fa-clock", "route": "clickup.reports.time-tracking", "permissions": ["view_clickup_reports"]}]}, {"label": "Settings", "icon": "fas fa-cog", "route": "clickup.settings.index", "permissions": ["manage_clickup_settings"], "subnav": [{"label": "API Configuration", "icon": "fas fa-key", "route": "clickup.settings.index", "permissions": ["manage_clickup_settings"]}, {"label": "Sync Settings", "icon": "fas fa-sync", "route": "clickup.settings.sync", "permissions": ["manage_clickup_settings"]}, {"label": "General Settings", "icon": "fas fa-sliders-h", "route": "clickup.settings.general", "permissions": ["manage_clickup_settings"]}, {"label": "List Assignments", "icon": "fas fa-user-cog", "route": "clickup.settings.assignments", "permissions": ["manage_clickup_settings"]}]}]}}