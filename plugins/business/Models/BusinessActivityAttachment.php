<?php

namespace Plugins\Business\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;
use App\Models\User;

class BusinessActivityAttachment extends Model
{
    use HasFactory;

    protected $fillable = [
        'activity_id',
        'business_id',
        'uploaded_by',
        'filename',
        'original_filename',
        'file_path',
        'file_type',
        'file_extension',
        'file_size',
        'attachment_type',
        'thumbnail_path',
        'has_preview',
        'metadata',
        'is_public',
        'allowed_users',
        'download_count',
        'last_downloaded_at',
    ];

    protected $casts = [
        'metadata' => 'array',
        'is_public' => 'boolean',
        'allowed_users' => 'array',
        'has_preview' => 'boolean',
        'download_count' => 'integer',
        'file_size' => 'integer',
        'last_downloaded_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the activity this attachment belongs to
     */
    public function activity(): BelongsTo
    {
        return $this->belongsTo(BusinessActivity::class, 'activity_id');
    }

    /**
     * Get the business this attachment belongs to
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get the user who uploaded this attachment
     */
    public function uploader(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Get file type categories
     */
    public static function getFileTypeCategories(): array
    {
        return [
            'document' => [
                'label' => 'Document',
                'icon' => 'fas fa-file-alt',
                'color' => 'blue',
                'extensions' => ['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt']
            ],
            'image' => [
                'label' => 'Image',
                'icon' => 'fas fa-image',
                'color' => 'green',
                'extensions' => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp']
            ],
            'video' => [
                'label' => 'Video',
                'icon' => 'fas fa-video',
                'color' => 'red',
                'extensions' => ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm']
            ],
            'audio' => [
                'label' => 'Audio',
                'icon' => 'fas fa-music',
                'color' => 'purple',
                'extensions' => ['mp3', 'wav', 'flac', 'aac', 'ogg']
            ],
            'archive' => [
                'label' => 'Archive',
                'icon' => 'fas fa-file-archive',
                'color' => 'orange',
                'extensions' => ['zip', 'rar', '7z', 'tar', 'gz']
            ],
            'other' => [
                'label' => 'Other',
                'icon' => 'fas fa-file',
                'color' => 'gray',
                'extensions' => []
            ],
        ];
    }

    /**
     * Determine file type category from extension
     */
    public static function determineFileType(string $extension): string
    {
        $extension = strtolower($extension);
        $categories = static::getFileTypeCategories();

        foreach ($categories as $type => $info) {
            if (in_array($extension, $info['extensions'])) {
                return $type;
            }
        }

        return 'other';
    }

    /**
     * Get file type information
     */
    public function getTypeInfoAttribute(): array
    {
        $categories = static::getFileTypeCategories();
        return $categories[$this->attachment_type] ?? $categories['other'];
    }

    /**
     * Get original name (alias for original_filename for backward compatibility)
     */
    public function getOriginalNameAttribute(): string
    {
        return $this->original_filename;
    }

    /**
     * Get formatted file size
     */
    public function getFormattedSizeAttribute(): string
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get download URL
     */
    public function getDownloadUrlAttribute(): string
    {
        return route('business.activity.attachment.download', [
            'business' => $this->business_id,
            'attachment' => $this->id
        ]);
    }

    /**
     * Get preview URL if available
     */
    public function getPreviewUrlAttribute(): ?string
    {
        if (!$this->has_preview || !$this->thumbnail_path) {
            return null;
        }

        return Storage::url($this->thumbnail_path);
    }

    /**
     * Check if file is an image
     */
    public function isImage(): bool
    {
        return $this->attachment_type === 'image';
    }

    /**
     * Check if file has a preview
     */
    public function hasPreview(): bool
    {
        return $this->has_preview && $this->thumbnail_path;
    }

    /**
     * Check if user can access this attachment
     */
    public function canAccess(User $user): bool
    {
        // Public files can be accessed by anyone
        if ($this->is_public) {
            return true;
        }

        // Uploader can always access
        if ($this->uploaded_by === $user->id) {
            return true;
        }

        // Check if user is in allowed users list
        if (!empty($this->allowed_users) && in_array($user->id, $this->allowed_users)) {
            return true;
        }

        // Users with manage_businesses permission can access any file
        return $user->hasPermission('manage_businesses');
    }

    /**
     * Increment download count
     */
    public function incrementDownloadCount(): void
    {
        $this->increment('download_count');
        $this->update(['last_downloaded_at' => now()]);
    }

    /**
     * Delete file from storage
     */
    public function deleteFile(): bool
    {
        $deleted = true;

        // Delete main file
        if (Storage::exists($this->file_path)) {
            $deleted = Storage::delete($this->file_path);
        }

        // Delete thumbnail if exists
        if ($this->thumbnail_path && Storage::exists($this->thumbnail_path)) {
            Storage::delete($this->thumbnail_path);
        }

        return $deleted;
    }

    /**
     * Generate thumbnail for supported file types
     */
    public function generateThumbnail(): bool
    {
        if (!$this->isImage()) {
            return false;
        }

        // This would typically use an image processing library
        // For now, we'll just mark it as having a preview
        $this->update([
            'has_preview' => true,
            'thumbnail_path' => $this->file_path, // Use original for now
        ]);

        return true;
    }

    /**
     * Create attachment from uploaded file
     */
    public static function createFromUpload($file, BusinessActivity $activity, array $options = []): self
    {
        $filename = uniqid() . '.' . $file->getClientOriginalExtension();
        $path = $file->storeAs('business_attachments/' . $activity->business_id, $filename);

        $attachment = static::create([
            'activity_id' => $activity->id,
            'business_id' => $activity->business_id,
            'uploaded_by' => auth()->id(),
            'filename' => $filename,
            'original_filename' => $file->getClientOriginalName(),
            'file_path' => $path,
            'file_type' => $file->getMimeType(),
            'file_extension' => $file->getClientOriginalExtension(),
            'file_size' => $file->getSize(),
            'attachment_type' => static::determineFileType($file->getClientOriginalExtension()),
            'is_public' => $options['is_public'] ?? false,
            'allowed_users' => $options['allowed_users'] ?? [],
            'metadata' => $options['metadata'] ?? [],
        ]);

        // Generate thumbnail for images
        if ($attachment->isImage()) {
            $attachment->generateThumbnail();
        }

        return $attachment;
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        // Delete file when model is deleted
        static::deleting(function ($attachment) {
            $attachment->deleteFile();
        });
    }
}
