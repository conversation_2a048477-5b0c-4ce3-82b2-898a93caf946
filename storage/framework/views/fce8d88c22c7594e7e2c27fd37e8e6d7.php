<?php $__env->startSection('title', 'Announcements'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="max-w-6xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Announcements</h1>
                <p class="text-gray-600 dark:text-gray-400 dark:text-gray-500 mt-1">Manage system announcements and notifications</p>
            </div>
            <div class="flex space-x-3">
                <?php if(auth()->user()->hasPermission('manage_announcements')): ?>
                    <a href="<?php echo e(route('announcements.create')); ?>"
                       class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 dark:bg-blue-700 dark:hover:bg-blue-600 dark:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-plus mr-2"></i>Create Announcement
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <!-- Search and Filter Form -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6 border border-gray-200 dark:border-gray-700 transition duration-150 ease-in-out">
            <form method="GET" action="<?php echo e(route('announcements.index')); ?>">
                <!-- Unified Search Box -->
                <div class="mb-6">
                    <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search Announcements</label>
                    <div class="relative">
                        <input type="text" name="search" id="search" value="<?php echo e(request('search')); ?>"
                               placeholder="Search by title or content..."
                               class="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400 dark:focus:border-blue-400 transition duration-150 ease-in-out dark:bg-gray-700 dark:text-white">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500"></i>
                        </div>
                    </div>
                </div>

                <!-- Filter Options -->
                <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <!-- Priority Filter -->
                    <div>
                        <label for="priority" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Priority</label>
                        <select name="priority" id="priority" class="w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400 dark:focus:border-blue-400 transition duration-150 ease-in-out dark:bg-gray-700 dark:text-white">
                            <option value="">All Priorities</option>
                            <option value="urgent" <?php echo e(request('priority') === 'urgent' ? 'selected' : ''); ?>>Urgent</option>
                            <option value="high" <?php echo e(request('priority') === 'high' ? 'selected' : ''); ?>>High</option>
                            <option value="normal" <?php echo e(request('priority') === 'normal' ? 'selected' : ''); ?>>Normal</option>
                            <option value="low" <?php echo e(request('priority') === 'low' ? 'selected' : ''); ?>>Low</option>
                        </select>
                    </div>

                    <!-- Type Filter -->
                    <div>
                        <label for="type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Type</label>
                        <select name="type" id="type" class="w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400 dark:focus:border-blue-400 transition duration-150 ease-in-out dark:bg-gray-700 dark:text-white">
                            <option value="">All Types</option>
                            <option value="info" <?php echo e(request('type') === 'info' ? 'selected' : ''); ?>>Info</option>
                            <option value="warning" <?php echo e(request('type') === 'warning' ? 'selected' : ''); ?>>Warning</option>
                            <option value="success" <?php echo e(request('type') === 'success' ? 'selected' : ''); ?>>Success</option>
                            <option value="error" <?php echo e(request('type') === 'error' ? 'selected' : ''); ?>>Error</option>
                        </select>
                    </div>

                    <!-- Status Filter -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Status</label>
                        <select name="status" id="status" class="w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400 dark:focus:border-blue-400 transition duration-150 ease-in-out dark:bg-gray-700 dark:text-white">
                            <option value="">All Statuses</option>
                            <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>Active</option>
                            <option value="inactive" <?php echo e(request('status') === 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                        </select>
                    </div>

                    <!-- Acknowledgment Filter (Admin Only) -->
                    <?php if(auth()->user()->hasPermission('manage_announcements')): ?>
                        <div>
                            <label for="acknowledgment" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Acknowledgment</label>
                            <select name="acknowledgment" id="acknowledgment" class="w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400 dark:focus:border-blue-400 transition duration-150 ease-in-out dark:bg-gray-700 dark:text-white">
                                <option value="">All Announcements</option>
                                <option value="unacknowledged" <?php echo e(request('acknowledgment') === 'unacknowledged' ? 'selected' : ''); ?>>Pending Acknowledgments</option>
                                <option value="fully_acknowledged" <?php echo e(request('acknowledgment') === 'fully_acknowledged' ? 'selected' : ''); ?>>Fully Acknowledged</option>
                            </select>
                            <p class="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">Filter by acknowledgment status</p>
                        </div>
                    <?php endif; ?>

                    <!-- Filter Actions -->
                    <div class="flex items-end space-x-2">
                        <button type="submit" class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 dark:bg-blue-700 dark:hover:bg-blue-600 dark:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                            <i class="fas fa-filter mr-2"></i>Filter
                        </button>
                        <a href="<?php echo e(route('announcements.index')); ?>" class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500 dark:bg-gray-600 dark:hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-7000 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                            <i class="fas fa-times mr-2"></i>Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>



        <!-- Announcements Table -->
        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg border border-gray-200 dark:border-gray-700 transition duration-150 ease-in-out">
            <?php if($announcements->count() > 0): ?>
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700 transition duration-150 ease-in-out">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Announcement</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Priority</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Type</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Read Stats</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700 transition duration-150 ease-in-out">
                        <?php $__currentLoopData = $announcements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $announcement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr class="hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-700 transition duration-150 ease-in-out">
                                <td class="px-6 py-4">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                                            <a href="<?php echo e(route('announcements.show', $announcement)); ?>" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:text-blue-200 dark:hover:text-blue-300 hover:underline">
                                                <?php echo e($announcement->title); ?>

                                            </a>
                                        </div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">
                                            <?php echo e(Str::limit($announcement->content, 100)); ?>

                                        </div>
                                        <div class="text-xs text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">
                                            Created <?php echo e($announcement->created_at->diffForHumans()); ?> by <?php echo e($announcement->creator->name); ?>

                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($announcement->getPriorityBadgeClass()); ?>">
                                        <?php echo e(ucfirst($announcement->priority)); ?>

                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($announcement->getTypeBadgeClass()); ?>">
                                        <?php echo e(ucfirst($announcement->type)); ?>

                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php if($announcement->is_active): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 transition duration-150 ease-in-out">
                                            Active
                                        </span>
                                    <?php else: ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 transition duration-150 ease-in-out">
                                            Inactive
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                    <?php if(auth()->id() === $announcement->created_by): ?>
                                        <div class="flex flex-col">
                                            <span><?php echo e($announcement->read_stats['read_count']); ?>/<?php echo e($announcement->read_stats['total_users']); ?> read (<?php echo e($announcement->read_stats['read_percentage']); ?>%)</span>
                                            <?php if($announcement->requires_acknowledgment): ?>
                                                <span class="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500"><?php echo e($announcement->read_stats['acknowledged_count']); ?> acknowledged (<?php echo e($announcement->read_stats['acknowledged_percentage']); ?>%)</span>
                                                <?php if($announcement->read_stats['pending_acknowledgments'] > 0): ?>
                                                    <span class="text-xs text-orange-600 dark:text-orange-400 font-medium"><?php echo e($announcement->read_stats['pending_acknowledgments']); ?> pending acknowledgments</span>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        </div>
                                    <?php else: ?>
                                        <div class="flex flex-col">
                                            <span class="text-gray-500 dark:text-gray-400 dark:text-gray-500 italic">Statistics visible to creator only</span>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <a href="<?php echo e(route('announcements.show', $announcement)); ?>"
                                           class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 transition duration-150 ease-in-out" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <?php if(auth()->user()->hasPermission('manage_announcements')): ?>
                                            <a href="<?php echo e(route('announcements.edit', $announcement)); ?>"
                                               class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 transition duration-150 ease-in-out" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form method="POST" action="<?php echo e(route('announcements.destroy', $announcement)); ?>"
                                                  class="inline" onsubmit="return confirm('Are you sure you want to delete this announcement?')">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 transition duration-150 ease-in-out" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>

                <!-- Pagination -->
                <div class="bg-white dark:bg-gray-800 px-4 py-3 border-t border-gray-200 dark:border-gray-700 sm:px-6 transition duration-150 ease-in-out">
                    <?php echo e($announcements->links()); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-12">
                    <i class="fas fa-bullhorn text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 text-6xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No announcements found</h3>
                    <p class="text-gray-500 dark:text-gray-400 dark:text-gray-500 mb-6">
                        <?php if(request()->hasAny(['search', 'priority', 'type', 'status'])): ?>
                            No announcements match your current filters.
                        <?php else: ?>
                            Get started by creating your first announcement.
                        <?php endif; ?>
                    </p>
                    <?php if(auth()->user()->hasPermission('manage_announcements')): ?>
                        <?php if(request()->hasAny(['search', 'priority', 'type', 'status'])): ?>
                            <a href="<?php echo e(route('announcements.index')); ?>"
                               class="bg-gray-500 hover:bg-gray-700 dark:bg-gray-600 dark:hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-7000 text-white font-bold py-2 px-4 rounded mr-3 transition duration-150 ease-in-out">
                                Clear Filters
                            </a>
                        <?php endif; ?>
                        <a href="<?php echo e(route('announcements.create')); ?>"
                           class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 dark:bg-blue-700 dark:hover:bg-blue-600 dark:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                            Create First Announcement
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/announcements/Views/index.blade.php ENDPATH**/ ?>