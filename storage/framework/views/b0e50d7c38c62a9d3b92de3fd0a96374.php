<?php $__env->startSection('title', 'Edit Activity'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Edit Activity</h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">Edit activity for <?php echo e($business->name); ?></p>
            </div>
            <div class="flex space-x-3">
                <a href="<?php echo e(route('business.activities.show', [$business, $activity])); ?>" 
                   class="bg-gray-500 dark:bg-gray-700 hover:bg-gray-600 dark:hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Back to Activity
                </a>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
            <form method="POST" action="<?php echo e(route('business.activities.update', [$business, $activity])); ?>" class="p-6">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>

                <div class="grid grid-cols-1 gap-6">
                    <!-- Activity Type -->
                    <div>
                        <label for="message_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Activity Type <span class="text-red-500 dark:text-red-400">*</span>
                        </label>
                        <select name="message_type" id="message_type" required
                                class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 <?php $__errorArgs = ['message_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 dark:border-red-600 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?> dark:bg-gray-700 dark:text-white">
                            <option value="">Select activity type</option>
                            <?php $__currentLoopData = $messageTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($key); ?>" <?php echo e((old('message_type', $activity->metadata['communication_type'] ?? $activity->type) === $key) ? 'selected' : ''); ?>>
                                    <?php echo e($label); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <?php $__errorArgs = ['message_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Message -->
                    <div>
                        <label for="message" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Message <span class="text-red-500 dark:text-red-400">*</span>
                        </label>
                        <textarea name="message" id="message" rows="4" required
                                  class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 <?php $__errorArgs = ['message'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 dark:border-red-600 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?> dark:bg-gray-700 dark:text-white"
                                  placeholder="Enter your message or activity details..."><?php echo e(old('message', $activity->description)); ?></textarea>
                        <?php $__errorArgs = ['message'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                            Maximum 1000 characters.
                        </p>
                    </div>

                    <!-- Edit History -->
                    <?php if(isset($activity->metadata['edit_history']) && count($activity->metadata['edit_history']) > 0): ?>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Edit History
                        </label>
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-md p-4 max-h-40 overflow-y-auto">
                            <?php $__currentLoopData = $activity->metadata['edit_history']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $edit): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="mb-3 last:mb-0 pb-3 last:pb-0 border-b last:border-b-0 border-gray-200 dark:border-gray-600">
                                    <div class="text-xs text-gray-500 dark:text-gray-400 mb-1">
                                        Edited <?php echo e(\Carbon\Carbon::parse($edit['edited_at'])->format('M j, Y g:i A')); ?>

                                    </div>
                                    <div class="text-sm text-gray-700 dark:text-gray-300">
                                        <?php echo e($edit['content']); ?>

                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Current Attachments -->
                    <?php if($activity->attachments && $activity->attachments->count() > 0): ?>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Current Attachments
                        </label>
                        <div class="space-y-2">
                            <?php $__currentLoopData = $activity->attachments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attachment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                                    <i class="fas fa-file text-gray-400 dark:text-gray-500 mr-3"></i>
                                    <div class="flex-1">
                                        <div class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e($attachment->original_name); ?></div>
                                        <div class="text-xs text-gray-500 dark:text-gray-400"><?php echo e(number_format($attachment->file_size / 1024, 1)); ?> KB</div>
                                    </div>
                                    <a href="<?php echo e(route('business.activities.downloadAttachment', [$business, $activity, $attachment])); ?>" 
                                       class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 mr-3">
                                        <i class="fas fa-download"></i>
                                    </a>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Submit Buttons -->
                <div class="mt-8 flex justify-end space-x-3">
                    <a href="<?php echo e(route('business.activities.show', [$business, $activity])); ?>" 
                       class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Update Activity
                    </button>
                </div>
            </form>
        </div>

        <!-- Activity Info -->
        <div class="mt-6 bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-md p-4 transition duration-150 ease-in-out">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-info-circle text-yellow-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">Activity Information</h3>
                    <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                        <ul class="list-disc pl-5 space-y-1">
                            <li>Created: <?php echo e($activity->created_at->format('M j, Y g:i A')); ?></li>
                            <li>Created by: <?php echo e($activity->user->name ?? 'Unknown'); ?></li>
                            <?php if($activity->updated_at != $activity->created_at): ?>
                                <li>Last updated: <?php echo e($activity->updated_at->format('M j, Y g:i A')); ?></li>
                            <?php endif; ?>
                            <li>Editing will create a history record of the original content</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/business/Views/activities/edit.blade.php ENDPATH**/ ?>