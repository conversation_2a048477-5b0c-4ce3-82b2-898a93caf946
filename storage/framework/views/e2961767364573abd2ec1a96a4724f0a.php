<?php $__env->startSection('title', 'Releases - ' . $product->name); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Product Releases</h1>
                <p class="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1"><?php echo e($product->name); ?></p>
            </div>
            <div class="flex space-x-3">
                <a href="<?php echo e(route('products.show', $product)); ?>" 
                   class="bg-gray-50 dark:bg-gray-7000 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Back to Product
                </a>
                <?php if(auth()->user()->hasPermission('manage_product_releases')): ?>
                    <a href="<?php echo e(route('products.releases.create', $product)); ?>" 
                       class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-plus mr-2"></i>Create Release
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-tag text-blue-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Total Releases</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($stats['total']); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Published</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($stats['published']); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-clock text-yellow-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Draft</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($stats['draft']); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-code-branch text-purple-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Latest Version</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($stats['latest_version'] ?? 'N/A'); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 transition duration-150 ease-in-out">
            <div class="p-6">
                <form method="GET" action="<?php echo e(route('products.releases.index', $product)); ?>" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <!-- Search -->
                        <div>
                            <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Search</label>
                            <input type="text" name="search" id="search" value="<?php echo e(request('search')); ?>"
                                   placeholder="Search releases..."
                                   class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:bg-gray-700 dark:text-white">
                        </div>

                        <!-- Status Filter -->
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
                            <select name="status" id="status"
                                    class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:bg-gray-700 dark:text-white">
                                <option value="">All Statuses</option>
                                <option value="published" <?php echo e(request('status') === 'published' ? 'selected' : ''); ?>>Published</option>
                                <option value="draft" <?php echo e(request('status') === 'draft' ? 'selected' : ''); ?>>Draft</option>
                            </select>
                        </div>

                        <!-- Release Type Filter -->
                        <div>
                            <label for="release_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Release Type</label>
                            <select name="release_type" id="release_type"
                                    class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:bg-gray-700 dark:text-white">
                                <option value="">All Types</option>
                                <?php $__currentLoopData = $releaseTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $typeKey => $typeLabel): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($typeKey); ?>" <?php echo e(request('release_type') === $typeKey ? 'selected' : ''); ?>>
                                        <?php echo e($typeLabel); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>

                        <!-- Actions -->
                        <div class="flex items-end">
                            <button type="submit" 
                                    class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded mr-2 transition duration-150 ease-in-out">
                                Apply Filters
                            </button>
                            <a href="<?php echo e(route('products.releases.index', $product)); ?>" 
                               class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-7000 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                                Clear
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Releases List -->
        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
            <?php if($releases->count() > 0): ?>
                <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                    <?php $__currentLoopData = $releases; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $release): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li class="px-4 py-6 sm:px-6 hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-700 transition duration-150 ease-in-out">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center flex-1">
                                    <div class="flex-shrink-0">
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium <?php echo e($release->release_type_badge_class); ?>">
                                            <?php echo e($release->release_type_label); ?>

                                        </span>
                                    </div>
                                    <div class="ml-6 flex-1">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                                                    <a href="<?php echo e(route('products.releases.show', [$product, $release])); ?>" 
                                                       class="hover:text-blue-600 dark:text-blue-400">
                                                        <?php echo e($release->formatted_version); ?> - <?php echo e($release->title); ?>

                                                    </a>
                                                </h3>
                                                <?php if($release->description): ?>
                                                    <p class="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1"><?php echo e($release->short_description); ?></p>
                                                <?php endif; ?>
                                                <div class="flex items-center mt-2 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 space-x-4">
                                                    <span class="flex items-center">
                                                        <i class="fas fa-calendar mr-1"></i>
                                                        <?php echo e($release->release_date ? $release->release_date->format('M d, Y') : 'Not set'); ?>

                                                    </span>
                                                    <?php if($release->total_changes_count > 0): ?>
                                                        <span class="flex items-center">
                                                            <i class="fas fa-list mr-1"></i>
                                                            <?php echo e($release->total_changes_count); ?> changes
                                                        </span>
                                                    <?php endif; ?>
                                                    <span class="flex items-center">
                                                        <i class="fas fa-user mr-1"></i>
                                                        <?php echo e($release->creator->name); ?>

                                                    </span>
                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium <?php echo e($release->is_published ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'); ?> transition duration-150 ease-in-out">
                                                        <?php echo e($release->is_published ? 'Published' : 'Draft'); ?>

                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <?php if(auth()->user()->hasPermission('view_product_releases')): ?>
                                        <a href="<?php echo e(route('products.releases.show', [$product, $release])); ?>" 
                                           class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 text-sm font-medium">
                                            View
                                        </a>
                                    <?php endif; ?>
                                    <?php if(auth()->user()->hasPermission('manage_product_releases')): ?>
                                        <a href="<?php echo e(route('products.releases.edit', [$product, $release])); ?>" 
                                           class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 text-sm font-medium">
                                            Edit
                                        </a>
                                        <?php if(!$release->is_published): ?>
                                            <form method="POST" action="<?php echo e(route('products.releases.publish', [$product, $release])); ?>" 
                                                  class="inline">
                                                <?php echo csrf_field(); ?>
                                                <button type="submit" 
                                                        class="text-green-600 dark:text-green-400 hover:text-green-900 text-sm font-medium"
                                                        onclick="return confirm('Are you sure you want to publish this release?')">
                                                    Publish
                                                </button>
                                            </form>
                                        <?php else: ?>
                                            <form method="POST" action="<?php echo e(route('products.releases.unpublish', [$product, $release])); ?>" 
                                                  class="inline">
                                                <?php echo csrf_field(); ?>
                                                <button type="submit" 
                                                        class="text-orange-600 dark:text-orange-400 hover:text-orange-900 text-sm font-medium"
                                                        onclick="return confirm('Are you sure you want to unpublish this release?')">
                                                    Unpublish
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>

                <!-- Pagination -->
                <?php if($releases->hasPages()): ?>
                    <div class="px-4 py-3 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-700 sm:px-6 transition duration-150 ease-in-out">
                        <?php echo e($releases->links()); ?>

                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="text-center py-12">
                    <i class="fas fa-tag text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 text-6xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No releases found</h3>
                    <p class="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mb-6">Create your first product release to track version history.</p>
                    <?php if(auth()->user()->hasPermission('manage_product_releases')): ?>
                        <a href="<?php echo e(route('products.releases.create', $product)); ?>" 
                           class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                            <i class="fas fa-plus mr-2"></i>Create Release
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/products/Views/releases/index.blade.php ENDPATH**/ ?>