<?php $__env->startSection('title', 'ClickUp Tasks'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">ClickUp Tasks</h1>
        <div class="flex space-x-3">
            <?php if(auth()->user()->hasPermission('sync_clickup_data')): ?>
                <button id="sync-tasks-btn" 
                        class="bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-sync mr-2"></i>
                    Sync Tasks
                </button>
            <?php endif; ?>
            <a href="<?php echo e(route('clickup.dashboard')); ?>"
               class="bg-gray-600 dark:bg-gray-700 hover:bg-gray-700 dark:hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Search and Filter Form -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6 border border-gray-200 dark:border-gray-700">
        <form method="GET" action="<?php echo e(route('clickup.tasks.index')); ?>">
            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search</label>
                    <input type="text" name="search" id="search" value="<?php echo e(request('search')); ?>"
                           placeholder="Search tasks..."
                           class="w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label>
                    <select name="status" id="status"
                            class="w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Statuses</option>
                        <option value="open" <?php echo e(request('status') === 'open' ? 'selected' : ''); ?>>Open</option>
                        <option value="in progress" <?php echo e(request('status') === 'in progress' ? 'selected' : ''); ?>>In Progress</option>
                        <option value="review" <?php echo e(request('status') === 'review' ? 'selected' : ''); ?>>Review</option>
                        <option value="complete" <?php echo e(request('status') === 'complete' ? 'selected' : ''); ?>>Complete</option>
                        <option value="closed" <?php echo e(request('status') === 'closed' ? 'selected' : ''); ?>>Closed</option>
                    </select>
                </div>

                <div>
                    <label for="priority" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Priority</label>
                    <select name="priority" id="priority"
                            class="w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Priorities</option>
                        <option value="urgent" <?php echo e(request('priority') === 'urgent' ? 'selected' : ''); ?>>Urgent</option>
                        <option value="high" <?php echo e(request('priority') === 'high' ? 'selected' : ''); ?>>High</option>
                        <option value="normal" <?php echo e(request('priority') === 'normal' ? 'selected' : ''); ?>>Normal</option>
                        <option value="low" <?php echo e(request('priority') === 'low' ? 'selected' : ''); ?>>Low</option>
                    </select>
                </div>

                <div>
                    <label for="list_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">List</label>
                    <select name="list_id" id="list_id"
                            class="w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Lists</option>
                        <?php $__currentLoopData = $lists; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $list): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($list->id); ?>" <?php echo e(request('list_id') == $list->id ? 'selected' : ''); ?>>
                                <?php echo e($list->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <div class="flex items-end">
                    <button type="submit" 
                            class="w-full bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-search mr-2"></i>
                        Filter
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Tasks Table -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Task
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            List / Manager
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Status
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Priority
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Assignees
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Due Date
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    <?php $__empty_1 = true; $__currentLoopData = $tasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                                            <?php echo e($task->name); ?>

                                        </div>
                                        <?php if($task->description): ?>
                                            <div class="text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs">
                                                <?php echo e(Str::limit($task->description, 60)); ?>

                                            </div>
                                        <?php endif; ?>
                                        <?php if($task->url): ?>
                                            <a href="<?php echo e($task->url); ?>" target="_blank" 
                                               class="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300">
                                                <i class="fas fa-external-link-alt mr-1"></i>
                                                View in ClickUp
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900 dark:text-white"><?php echo e($task->list->name); ?></div>
                                <?php if($task->list->productManager): ?>
                                    <div class="text-sm text-gray-500 dark:text-gray-400"><?php echo e($task->list->productManager->name); ?></div>
                                <?php else: ?>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">Unassigned</div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                    <?php switch($task->status):
                                        case ('complete'): ?>
                                        <?php case ('closed'): ?>
                                            bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100
                                            <?php break; ?>
                                        <?php case ('in progress'): ?>
                                            bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100
                                            <?php break; ?>
                                        <?php case ('review'): ?>
                                            bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100
                                            <?php break; ?>
                                        <?php default: ?>
                                            bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300
                                    <?php endswitch; ?>">
                                    <?php echo e(ucfirst($task->status)); ?>

                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php if($task->priority): ?>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                        <?php switch($task->priority):
                                            case ('urgent'): ?>
                                                bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100
                                                <?php break; ?>
                                            <?php case ('high'): ?>
                                                bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-100
                                                <?php break; ?>
                                            <?php case ('normal'): ?>
                                                bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100
                                                <?php break; ?>
                                            <?php case ('low'): ?>
                                                bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300
                                                <?php break; ?>
                                        <?php endswitch; ?>">
                                        <?php echo e(ucfirst($task->priority)); ?>

                                    </span>
                                <?php else: ?>
                                    <span class="text-sm text-gray-500 dark:text-gray-400">-</span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php if($task->assignee_names && count($task->assignee_names) > 0): ?>
                                    <div class="text-sm text-gray-900 dark:text-white">
                                        <?php echo e(implode(', ', array_slice($task->assignee_names, 0, 2))); ?>

                                        <?php if(count($task->assignee_names) > 2): ?>
                                            <span class="text-gray-500 dark:text-gray-400">
                                                +<?php echo e(count($task->assignee_names) - 2); ?> more
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                <?php else: ?>
                                    <span class="text-sm text-gray-500 dark:text-gray-400">Unassigned</span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                <?php if($task->due_date): ?>
                                    <div class="<?php echo e($task->is_overdue ? 'text-red-600 dark:text-red-400' : ''); ?>">
                                        <?php echo e($task->due_date->format('M d, Y')); ?>

                                        <?php if($task->is_overdue): ?>
                                            <i class="fas fa-exclamation-triangle ml-1"></i>
                                        <?php endif; ?>
                                    </div>
                                <?php else: ?>
                                    <span class="text-gray-500 dark:text-gray-400">No due date</span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex justify-end space-x-2">
                                    <?php if(auth()->user()->hasPermission('view_clickup_tasks')): ?>
                                        <a href="<?php echo e(route('clickup.tasks.show', $task)); ?>" 
                                           class="text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    <?php endif; ?>
                                    <?php if(auth()->user()->hasPermission('sync_clickup_data')): ?>
                                        <button onclick="syncTask(<?php echo e($task->id); ?>)" 
                                                class="text-green-600 dark:text-green-400 hover:text-green-500 dark:hover:text-green-300">
                                            <i class="fas fa-sync"></i>
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="7" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                                No tasks found. 
                                <?php if(auth()->user()->hasPermission('sync_clickup_data')): ?>
                                    <button id="sync-empty-btn" class="text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 underline">
                                        Sync from ClickUp
                                    </button>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <?php if($tasks->hasPages()): ?>
            <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                <?php echo e($tasks->links()); ?>

            </div>
        <?php endif; ?>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Sync tasks functionality
    function syncTasks(button) {
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Syncing...';
        
        fetch('<?php echo e(route("clickup.sync.tasks")); ?>', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Sync failed: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Sync failed. Please try again.');
        })
        .finally(() => {
            button.disabled = false;
            button.innerHTML = '<i class="fas fa-sync mr-2"></i>Sync Tasks';
        });
    }

    // Sync individual task
    window.syncTask = function(taskId) {
        fetch(`/clickup/tasks/${taskId}/sync`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Task marked for sync successfully');
                location.reload();
            } else {
                alert('Sync failed: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Sync failed. Please try again.');
        });
    };

    const syncBtn = document.getElementById('sync-tasks-btn');
    const syncEmptyBtn = document.getElementById('sync-empty-btn');
    
    if (syncBtn) {
        syncBtn.addEventListener('click', () => syncTasks(syncBtn));
    }
    
    if (syncEmptyBtn) {
        syncEmptyBtn.addEventListener('click', () => syncTasks(syncEmptyBtn));
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/clickup/Views/tasks/index.blade.php ENDPATH**/ ?>