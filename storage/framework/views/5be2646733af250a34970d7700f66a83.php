<?php $__env->startSection('title', 'Business Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Business Management</h1>
        <?php if(auth()->user()->hasPermission('manage_businesses')): ?>
            <div class="flex space-x-3">
                <a href="<?php echo e(route('business.import.index')); ?>"
                   class="bg-green-500 dark:bg-green-600 hover:bg-green-700 dark:hover:bg-green-500 dark:bg-green-600 dark:hover:bg-green-500 dark:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-upload mr-2"></i>
                    Import Data
                </a>
                <a href="<?php echo e(route('business.create')); ?>"
                   class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-600 dark:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-plus mr-2"></i>
                    Add New Business
                </a>
            </div>
        <?php endif; ?>
    </div>

    <!-- Search and Filter Form -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6 border border-gray-200 dark:border-gray-700 transition duration-150 ease-in-out">
        <form method="GET" action="<?php echo e(route('business.index')); ?>">
            <!-- Unified Search Box -->
            <div class="mb-6">
                <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search Businesses</label>
                <div class="relative">
                    <input type="text" name="search" id="search" value="<?php echo e(request('search')); ?>"
                           placeholder="Search by business name, Arabic name, email, Taqnyat ID, username, brand name, description, or location..."
                           class="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400 dark:focus:border-blue-400 transition duration-150 ease-in-out dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500"></i>
                    </div>
                </div>
            </div>

            <!-- Filter Checkboxes -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                <!-- Business Status Filter - Multi-Select Dropdown -->
                <div>
                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Business Status</h4>
                    <div class="relative">
                        <button type="button" id="status-dropdown-btn"
                                class="w-full px-3 py-2 text-left border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400 dark:focus:border-blue-400 transition duration-150 ease-in-out dark:bg-gray-700 dark:text-white">
                            <span id="status-dropdown-text" class="text-sm text-gray-700 dark:text-gray-300">
                                <?php if(request('status') && count(request('status')) > 0): ?>
                                    <?php echo e(count(request('status'))); ?> status(es) selected
                                <?php else: ?>
                                    Select status(es)
                                <?php endif; ?>
                            </span>
                            <i class="fas fa-chevron-down float-right mt-0.5 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500"></i>
                        </button>

                        <div id="status-dropdown-menu" class="hidden absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg transition duration-150 ease-in-out dark:bg-gray-700 dark:text-white">
                            <div class="py-1 max-h-60 overflow-y-auto">
                                <?php $statuses = ['lead' => 'Lead', 'deal' => 'Deal', 'customer' => 'Customer', 'partner' => 'Partner', 'churned' => 'Churned', 'lost' => 'Lost']; ?>
                                <?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <label class="flex items-center px-3 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 cursor-pointer transition duration-150 ease-in-out">
                                        <input type="checkbox" name="status[]" value="<?php echo e($value); ?>"
                                               <?php echo e(in_array($value, request('status', [])) ? 'checked' : ''); ?>

                                               class="rounded border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-400 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white"
                                               onchange="updateStatusDropdownText()">
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300"><?php echo e($label); ?></span>
                                        <?php if($value === 'lead'): ?>
                                            <span class="ml-auto text-xs text-blue-600 dark:text-blue-400">🔍</span>
                                        <?php elseif($value === 'deal'): ?>
                                            <span class="ml-auto text-xs text-orange-600 dark:text-orange-400">🤝</span>
                                        <?php elseif($value === 'customer'): ?>
                                            <span class="ml-auto text-xs text-green-600 dark:text-green-400">✅</span>
                                        <?php elseif($value === 'partner'): ?>
                                            <span class="ml-auto text-xs text-purple-600 dark:text-purple-400">🤝</span>
                                        <?php elseif($value === 'churned'): ?>
                                            <span class="ml-auto text-xs text-red-600 dark:text-red-400">❌</span>
                                        <?php elseif($value === 'lost'): ?>
                                            <span class="ml-auto text-xs text-gray-600 dark:text-gray-400 dark:text-gray-500">💔</span>
                                        <?php endif; ?>
                                    </label>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Products Filter - Multi-Select Dropdown -->
                <div>
                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Products</h4>
                    <div class="relative">
                        <button type="button" id="products-dropdown-btn"
                                class="w-full px-3 py-2 text-left border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400">
                            <span id="products-dropdown-text" class="text-sm text-gray-700 dark:text-gray-300">
                                <?php if(request('products') && count(request('products')) > 0): ?>
                                    <?php echo e(count(request('products'))); ?> product(s) selected
                                <?php else: ?>
                                    Select product(s)
                                <?php endif; ?>
                            </span>
                            <i class="fas fa-chevron-down float-right mt-0.5 text-gray-400 dark:text-gray-500"></i>
                        </button>

                        <div id="products-dropdown-menu" class="hidden absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg">
                            <div class="py-1 max-h-60 overflow-y-auto">
                                <?php $__currentLoopData = \Plugins\Business\Models\Product::orderBy('name')->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <label class="flex items-center px-3 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-700 cursor-pointer transition duration-150 ease-in-out">
                                        <input type="checkbox" name="products[]" value="<?php echo e($product->id); ?>"
                                               <?php echo e(in_array($product->id, request('products', [])) ? 'checked' : ''); ?>

                                               class="rounded border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-400 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                               onchange="updateProductsDropdownText()">
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300"><?php echo e($product->name); ?></span>
                                    </label>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tags Filter - Multi-Select Dropdown -->
                <div>
                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Tags</h4>
                    <div class="relative">
                        <button type="button" id="tags-dropdown-btn"
                                class="w-full px-3 py-2 text-left border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400">
                            <span id="tags-dropdown-text" class="text-sm text-gray-700 dark:text-gray-300">
                                <?php if(request('tags') && count(request('tags')) > 0): ?>
                                    <?php echo e(count(request('tags'))); ?> tag(s) selected
                                <?php else: ?>
                                    Select tag(s)
                                <?php endif; ?>
                            </span>
                            <i class="fas fa-chevron-down float-right mt-0.5 text-gray-400 dark:text-gray-500"></i>
                        </button>

                        <div id="tags-dropdown-menu" class="hidden absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg">
                            <div class="py-1 max-h-60 overflow-y-auto">
                                <?php $__currentLoopData = \Plugins\Business\Models\Tag::orderBy('name')->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <label class="flex items-center px-3 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-700 cursor-pointer transition duration-150 ease-in-out">
                                        <input type="checkbox" name="tags[]" value="<?php echo e($tag->id); ?>"
                                               <?php echo e(in_array($tag->id, request('tags', [])) ? 'checked' : ''); ?>

                                               class="rounded border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-400 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                               onchange="updateTagsDropdownText()">
                                        <div class="ml-2 flex items-center">
                                            <div class="w-3 h-3 rounded-full mr-2" style="background-color: <?php echo e($tag->color); ?>;"></div>
                                            <span class="text-sm text-gray-700 dark:text-gray-300"><?php echo e($tag->name); ?></span>
                                        </div>
                                    </label>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-wrap gap-2">
                <button type="submit" class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-search mr-2"></i>
                    Search
                </button>
                <?php if(request()->hasAny(['search', 'status', 'products', 'tags'])): ?>
                    <a href="<?php echo e(route('business.index')); ?>" class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-7000 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-times mr-2"></i>
                        Clear Filters
                    </a>
                <?php endif; ?>
            </div>
        </form>
    </div>

    <!-- Businesses Table -->
    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
        <?php if($businesses->count() > 0): ?>
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700 transition duration-150 ease-in-out">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Business</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Tags</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Products</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    <?php $__currentLoopData = $businesses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $business): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-700 transition duration-150 ease-in-out">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <img class="h-10 w-10 rounded-full mr-3" src="<?php echo e($business->logo_url); ?>" alt="<?php echo e($business->name); ?>">
                                    <div>
                                        <div class="text-sm font-medium">
                                            <a href="<?php echo e(route('business.show', $business)); ?>" class="text-blue-600 hover:text-blue-800 dark:text-blue-200 dark:hover:text-blue-300 hover:underline">
                                                <?php echo e($business->name); ?>

                                            </a>
                                        </div>
                                        <?php if($business->arabic_name): ?>
                                            <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500" dir="rtl" lang="ar"><?php echo e($business->arabic_name); ?></div>
                                        <?php endif; ?>
                                        <?php if($business->brand_name && $business->brand_name !== $business->name): ?>
                                            <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500"><?php echo e($business->brand_name); ?></div>
                                        <?php endif; ?>
                                        <?php if($business->taqnyat_id): ?>
                                            <div class="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1" title="Taqnyat ID">
                                                <i class="fas fa-sms mr-1"></i>
                                                <?php echo e($business->taqnyat_id); ?>

                                            </div>
                                        <?php endif; ?>
                                        <?php if($business->taqnyat_username): ?>
                                            <div class="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1" title="Taqnyat Username">
                                                <i class="fas fa-user mr-1"></i>
                                                <?php echo e('@' . $business->taqnyat_username); ?>

                                            </div>
                                        <?php endif; ?>
                                        <?php if($business->website_url): ?>
                                            <div class="text-xs text-blue-600 dark:text-blue-400 mt-1">
                                                <a href="<?php echo e($business->website_url); ?>" target="_blank" class="hover:underline">
                                                    <i class="fas fa-external-link-alt mr-1"></i>Website
                                                </a>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex flex-wrap gap-1">
                                    <?php $businessTags = $business->tags()->limit(3)->get(); ?>
                                    <?php $__currentLoopData = $businessTags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                                              style="background-color: <?php echo e($tag->color); ?>20; color: <?php echo e($tag->color); ?>;">
                                            <?php echo e($tag->name); ?>

                                        </span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($business->tags()->count() > 3): ?>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 transition duration-150 ease-in-out">
                                            +<?php echo e($business->tags()->count() - 3); ?>

                                        </span>
                                    <?php endif; ?>
                                    <?php if($business->tags()->count() == 0): ?>
                                        <span class="text-xs text-gray-400 dark:text-gray-500">No tags</span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-1">
                                    <?php $businessProducts = $business->products->take(4); ?>
                                    <?php if($businessProducts->count() > 0): ?>
                                        <?php $__currentLoopData = $businessProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="flex items-center" title="<?php echo e($product->name); ?>">
                                                <i class="<?php echo e($product->icon ?? 'fas fa-box'); ?> text-gray-600 dark:text-gray-400 dark:text-gray-500 text-sm"></i>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php if($business->products->count() > 4): ?>
                                            <span class="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 ml-1">+<?php echo e($business->products->count() - 4); ?></span>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="text-xs text-gray-400 dark:text-gray-500">No products</span>
                                    <?php endif; ?>
                                </div>
                            </td>

                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo e($business->status_color); ?>">
                                    <?php echo e($business->status_label); ?>

                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <a href="<?php echo e(route('business.show', $business)); ?>"
                                       class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300" title="View Business">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <?php if(auth()->user()->hasPermission('manage_businesses')): ?>
                                        <a href="<?php echo e(route('business.edit', $business)); ?>"
                                           class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300" title="Edit Business">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="<?php echo e(route('business.tags.index', $business)); ?>"
                                           class="text-purple-600 dark:text-purple-400 hover:text-purple-900" title="Manage Tags">
                                            <i class="fas fa-tags"></i>
                                        </a>
                                        <a href="<?php echo e(route('business.products.index', $business)); ?>"
                                           class="text-green-600 dark:text-green-400 hover:text-green-900" title="Manage Products">
                                            <i class="fas fa-box"></i>
                                        </a>
                                    <?php endif; ?>
                                    <?php if(auth()->user()->hasPermission('view_business_reports')): ?>
                                        <a href="<?php echo e(route('business.reports', $business)); ?>"
                                           class="text-orange-600 dark:text-orange-400 hover:text-orange-900" title="Reports">
                                            <i class="fas fa-chart-line"></i>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>

            <!-- Pagination -->
            <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                <?php echo e($businesses->links()); ?>

            </div>
        <?php else: ?>
            <div class="text-center py-12">
                <div class="text-gray-500 dark:text-gray-400 dark:text-gray-500 text-lg">No businesses found.</div>
                <?php if(auth()->user()->hasPermission('manage_businesses')): ?>
                    <a href="<?php echo e(route('business.create')); ?>" 
                       class="mt-4 inline-block bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Create Your First Business
                    </a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Multi-select dropdown functionality
document.addEventListener('DOMContentLoaded', function() {
    // Status dropdown
    const statusDropdownBtn = document.getElementById('status-dropdown-btn');
    const statusDropdownMenu = document.getElementById('status-dropdown-menu');

    if (statusDropdownBtn && statusDropdownMenu) {
        // Toggle dropdown
        statusDropdownBtn.addEventListener('click', function(e) {
            e.preventDefault();
            statusDropdownMenu.classList.toggle('hidden');
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!statusDropdownBtn.contains(e.target) && !statusDropdownMenu.contains(e.target)) {
                statusDropdownMenu.classList.add('hidden');
            }
        });

        // Prevent dropdown from closing when clicking inside
        statusDropdownMenu.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }

    // Products dropdown
    const productsDropdownBtn = document.getElementById('products-dropdown-btn');
    const productsDropdownMenu = document.getElementById('products-dropdown-menu');

    if (productsDropdownBtn && productsDropdownMenu) {
        // Toggle dropdown
        productsDropdownBtn.addEventListener('click', function(e) {
            e.preventDefault();
            productsDropdownMenu.classList.toggle('hidden');
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!productsDropdownBtn.contains(e.target) && !productsDropdownMenu.contains(e.target)) {
                productsDropdownMenu.classList.add('hidden');
            }
        });

        // Prevent dropdown from closing when clicking inside
        productsDropdownMenu.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }

    // Tags dropdown
    const tagsDropdownBtn = document.getElementById('tags-dropdown-btn');
    const tagsDropdownMenu = document.getElementById('tags-dropdown-menu');

    if (tagsDropdownBtn && tagsDropdownMenu) {
        // Toggle dropdown
        tagsDropdownBtn.addEventListener('click', function(e) {
            e.preventDefault();
            tagsDropdownMenu.classList.toggle('hidden');
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!tagsDropdownBtn.contains(e.target) && !tagsDropdownMenu.contains(e.target)) {
                tagsDropdownMenu.classList.add('hidden');
            }
        });

        // Prevent dropdown from closing when clicking inside
        tagsDropdownMenu.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }
});

// Update dropdown text based on selected checkboxes
function updateStatusDropdownText() {
    const checkboxes = document.querySelectorAll('input[name="status[]"]:checked');
    const dropdownText = document.getElementById('status-dropdown-text');

    if (checkboxes.length === 0) {
        dropdownText.textContent = 'Select status(es)';
    } else if (checkboxes.length === 1) {
        dropdownText.textContent = checkboxes[0].nextElementSibling.textContent.trim();
    } else {
        dropdownText.textContent = `${checkboxes.length} status(es) selected`;
    }
}

function updateProductsDropdownText() {
    const checkboxes = document.querySelectorAll('input[name="products[]"]:checked');
    const dropdownText = document.getElementById('products-dropdown-text');

    if (checkboxes.length === 0) {
        dropdownText.textContent = 'Select product(s)';
    } else if (checkboxes.length === 1) {
        dropdownText.textContent = checkboxes[0].nextElementSibling.textContent.trim();
    } else {
        dropdownText.textContent = `${checkboxes.length} product(s) selected`;
    }
}

function updateTagsDropdownText() {
    const checkboxes = document.querySelectorAll('input[name="tags[]"]:checked');
    const dropdownText = document.getElementById('tags-dropdown-text');

    if (checkboxes.length === 0) {
        dropdownText.textContent = 'Select tag(s)';
    } else if (checkboxes.length === 1) {
        dropdownText.textContent = checkboxes[0].nextElementSibling.textContent.trim();
    } else {
        dropdownText.textContent = `${checkboxes.length} tag(s) selected`;
    }
}

// Initialize dropdown text on page load
document.addEventListener('DOMContentLoaded', function() {
    updateStatusDropdownText();
    updateProductsDropdownText();
    updateTagsDropdownText();
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/business/Views/index.blade.php ENDPATH**/ ?>