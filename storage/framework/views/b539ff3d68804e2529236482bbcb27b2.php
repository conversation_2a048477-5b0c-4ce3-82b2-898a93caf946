<?php if(!$currentToken): ?>
    <div class="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-md p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas fa-exclamation-triangle text-yellow-400"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                    API Token Required
                </h3>
                <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                    <p>Please configure your ClickUp API token in the Workspace tab to manage teams.</p>
                </div>
            </div>
        </div>
    </div>
<?php else: ?>
    <!-- Teams Management -->
    <div class="mb-8">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Teams Management</h3>
        <p class="text-sm text-gray-500 dark:text-gray-400 mb-6">Configure and manage your ClickUp teams, including team assignments and permissions.</p>
        
        <?php if(!empty($teams)): ?>
            <!-- Teams List -->
            <div class="space-y-4">
                <?php $__currentLoopData = $teams; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $team): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                                        <i class="fas fa-users text-blue-600 dark:text-blue-400"></i>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <h4 class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($team['name']); ?></h4>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">Team ID: <?php echo e($team['id']); ?></p>
                                    <?php if(isset($team['members'])): ?>
                                        <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo e(count($team['members'])); ?> members</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="flex space-x-2">
                                <button type="button" 
                                        class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-3 rounded text-sm transition duration-150 ease-in-out"
                                        onclick="viewTeamDetails('<?php echo e($team['id']); ?>')">
                                    <i class="fas fa-eye mr-1"></i>
                                    View Details
                                </button>
                                <button type="button" 
                                        class="bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-bold py-2 px-3 rounded text-sm transition duration-150 ease-in-out"
                                        onclick="configureTeam('<?php echo e($team['id']); ?>')">
                                    <i class="fas fa-cog mr-1"></i>
                                    Configure
                                </button>
                            </div>
                        </div>
                        
                        <!-- Team Members (if available) -->
                        <?php if(isset($team['members']) && !empty($team['members'])): ?>
                            <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
                                <h5 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Team Members:</h5>
                                <div class="flex flex-wrap gap-2">
                                    <?php $__currentLoopData = array_slice($team['members'], 0, 5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $member): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                                            <?php echo e($member['username'] ?? $member['email'] ?? 'Unknown'); ?>

                                        </span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php if(count($team['members']) > 5): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
                                            +<?php echo e(count($team['members']) - 5); ?> more
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php else: ?>
            <div class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-8 text-center">
                <div class="flex flex-col items-center">
                    <div class="w-16 h-16 bg-gray-100 dark:bg-gray-600 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-users text-gray-400 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Teams Found</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
                        No teams were found in your ClickUp workspace. This might be normal if you don't have access to team information.
                    </p>
                    <button type="button" 
                            class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out"
                            onclick="refreshTeams()">
                        <i class="fas fa-sync mr-2"></i>
                        Refresh Teams
                    </button>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Team Configuration Options -->
    <div class="mb-8">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Team Configuration</h3>
        <p class="text-sm text-gray-500 dark:text-gray-400 mb-6">Configure how teams are managed and synchronized.</p>
        
        <form method="POST" action="<?php echo e(route('clickup.settings.update-general')); ?>" class="space-y-6">
            <?php echo csrf_field(); ?>
            <input type="hidden" name="section" value="teams">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="sync_team_members" value="1" 
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Sync team member information</span>
                    </label>
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Automatically sync team member details and roles</p>
                </div>
                
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="auto_assign_teams" value="1" 
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Auto-assign teams to projects</span>
                    </label>
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Automatically assign teams based on project criteria</p>
                </div>
                
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="notify_team_changes" value="1" 
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Notify on team changes</span>
                    </label>
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Send notifications when team assignments change</p>
                </div>
                
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="track_team_performance" value="1" 
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Track team performance</span>
                    </label>
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Enable team performance tracking and reporting</p>
                </div>
            </div>
            
            <div class="flex justify-end">
                <button type="submit"
                        class="bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-save mr-2"></i>
                    Save Team Settings
                </button>
            </div>
        </form>
    </div>
<?php endif; ?>

<!-- Team Details Modal -->
<div id="team-details-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Team Details</h3>
                <button type="button" id="close-team-modal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="team-details-content" class="space-y-4">
                <!-- Team details will be loaded here -->
            </div>
        </div>
    </div>
</div>

<script>
function viewTeamDetails(teamId) {
    // Show modal
    document.getElementById('team-details-modal').classList.remove('hidden');
    
    // Load team details via AJAX
    fetch(`/clickup/api/teams/${teamId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayTeamDetails(data.team);
            } else {
                document.getElementById('team-details-content').innerHTML = 
                    '<p class="text-red-600 dark:text-red-400">Failed to load team details.</p>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('team-details-content').innerHTML = 
                '<p class="text-red-600 dark:text-red-400">Error loading team details.</p>';
        });
}

function displayTeamDetails(team) {
    const content = `
        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h4 class="font-medium text-gray-900 dark:text-white mb-2">${team.name}</h4>
            <p class="text-sm text-gray-600 dark:text-gray-400">Team ID: ${team.id}</p>
            ${team.members ? `<p class="text-sm text-gray-600 dark:text-gray-400">Members: ${team.members.length}</p>` : ''}
        </div>
    `;
    document.getElementById('team-details-content').innerHTML = content;
}

function configureTeam(teamId) {
    // Implement team configuration logic
    alert('Team configuration feature coming soon!');
}

function refreshTeams() {
    location.reload();
}

// Close modal functionality
document.getElementById('close-team-modal')?.addEventListener('click', function() {
    document.getElementById('team-details-modal').classList.add('hidden');
});

// Close modal on outside click
document.getElementById('team-details-modal')?.addEventListener('click', function(e) {
    if (e.target === this) {
        this.classList.add('hidden');
    }
});
</script>
<?php /**PATH /Users/<USER>/Herd/business/plugins/clickup/Views/settings/tabs/teams.blade.php ENDPATH**/ ?>