<?php $__env->startSection('title', 'ClickUp Dashboard'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">ClickUp Dashboard</h1>
        <div class="flex space-x-3">
            <?php if(auth()->user()->hasPermission('sync_clickup_data')): ?>
                <button id="sync-all-btn" 
                        class="bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-sync mr-2"></i>
                    Sync All Data
                </button>
            <?php endif; ?>
            <?php if(auth()->user()->hasPermission('manage_clickup_settings')): ?>
                <a href="<?php echo e(route('clickup.settings.index')); ?>"
                   class="bg-gray-600 dark:bg-gray-700 hover:bg-gray-700 dark:hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-cog mr-2"></i>
                    Settings
                </a>
            <?php endif; ?>
        </div>
    </div>

    <!-- API Status Alert -->
    <?php if(!$apiStatus['configured']): ?>
        <div class="bg-yellow-100 dark:bg-yellow-900 border-l-4 border-yellow-500 text-yellow-700 dark:text-yellow-300 p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-yellow-500"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm">
                        ClickUp API is not configured. 
                        <?php if(auth()->user()->hasPermission('manage_clickup_settings')): ?>
                            <a href="<?php echo e(route('clickup.settings.index')); ?>" class="font-medium underline hover:text-yellow-600 dark:hover:text-yellow-200">
                                Configure it now
                            </a>
                        <?php else: ?>
                            Please contact an administrator to configure the API.
                        <?php endif; ?>
                    </p>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Overview Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Tasks -->
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-tasks text-blue-500 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Total Tasks</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e(number_format($stats['total_tasks'])); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Tasks -->
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-play-circle text-green-500 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Active Tasks</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e(number_format($stats['active_tasks'])); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Completion Rate -->
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-chart-line text-purple-500 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Completion Rate</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($stats['completion_rate']); ?>%</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Overdue Tasks -->
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-circle text-red-500 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Overdue Tasks</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e(number_format($stats['overdue_tasks'])); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Recent Activity -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Recent Activity</h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <?php $__empty_1 = true; $__currentLoopData = $recentTasks['recently_completed']->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="flex items-center space-x-3">
                            <div class="flex-shrink-0">
                                <i class="fas fa-check-circle text-green-500"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                                    <?php echo e($task->name); ?>

                                </p>
                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                    <?php if($task->list): ?>
                                        <?php echo e($task->list->name); ?> •
                                        <?php if($task->list->productManager): ?>
                                            <?php echo e($task->list->productManager->name); ?>

                                        <?php else: ?>
                                            Unassigned
                                        <?php endif; ?>
                                    <?php else: ?>
                                        No List • Unassigned
                                    <?php endif; ?>
                                </p>
                            </div>
                            <div class="flex-shrink-0 text-sm text-gray-500 dark:text-gray-400">
                                <?php echo e($task->date_closed ? \Carbon\Carbon::parse($task->date_closed)->diffForHumans() : ''); ?>

                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <p class="text-gray-500 dark:text-gray-400 text-center py-4">No recent activity</p>
                    <?php endif; ?>
                </div>
                <?php if(auth()->user()->hasPermission('view_clickup_tasks')): ?>
                    <div class="mt-6">
                        <a href="<?php echo e(route('clickup.tasks.index')); ?>" 
                           class="text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 text-sm font-medium">
                            View all tasks →
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Performance Metrics -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Performance Metrics</h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Tasks This Week</span>
                        <span class="text-sm font-bold text-gray-900 dark:text-white"><?php echo e($performanceMetrics['tasks_this_week']); ?></span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Tasks Last Week</span>
                        <span class="text-sm font-bold text-gray-900 dark:text-white"><?php echo e($performanceMetrics['tasks_last_week']); ?></span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Weekly Change</span>
                        <span class="text-sm font-bold <?php echo e($performanceMetrics['weekly_change'] >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'); ?>">
                            <?php echo e($performanceMetrics['weekly_change'] > 0 ? '+' : ''); ?><?php echo e($performanceMetrics['weekly_change']); ?>%
                        </span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Avg. Completion Time</span>
                        <span class="text-sm font-bold text-gray-900 dark:text-white"><?php echo e($performanceMetrics['avg_completion_time']); ?>h</span>
                    </div>
                </div>
                <?php if(auth()->user()->hasPermission('view_clickup_reports')): ?>
                    <div class="mt-6">
                        <a href="<?php echo e(route('clickup.reports.index')); ?>" 
                           class="text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 text-sm font-medium">
                            View detailed reports →
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Sync Status -->
    <div class="mt-8 bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Sync Status</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-900 dark:text-white"><?php echo e(number_format($syncStatus['total_lists'])); ?></div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">Total Lists</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-900 dark:text-white"><?php echo e(number_format($syncStatus['total_tasks'])); ?></div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">Total Tasks</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-900 dark:text-white">
                        <?php echo e($syncStatus['last_sync'] ? \Carbon\Carbon::parse($syncStatus['last_sync'])->diffForHumans() : 'Never'); ?>

                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">Last Sync</div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const syncAllBtn = document.getElementById('sync-all-btn');
    
    if (syncAllBtn) {
        syncAllBtn.addEventListener('click', function() {
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Syncing...';
            
            fetch('<?php echo e(route("clickup.sync.all")); ?>', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message with stats if available
                    let message = data.message;
                    if (data.stats && data.stats.total_errors > 0) {
                        message += ` (${data.stats.error_rate}% error rate)`;
                    }
                    alert('✅ ' + message);
                    location.reload();
                } else {
                    // Check if it's a partial success (some items synced)
                    if (data.stats && data.stats.total_synced > 0) {
                        alert('⚠️ Partial sync: ' + data.message + '\n\nMost items synced successfully. Check logs for details on errors.');
                        location.reload(); // Still reload to show synced data
                    } else {
                        alert('❌ Sync failed: ' + data.message);
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('❌ Sync failed. Please try again.');
            })
            .finally(() => {
                this.disabled = false;
                this.innerHTML = '<i class="fas fa-sync mr-2"></i>Sync All Data';
            });
        });
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/clickup/Views/dashboard/index.blade.php ENDPATH**/ ?>