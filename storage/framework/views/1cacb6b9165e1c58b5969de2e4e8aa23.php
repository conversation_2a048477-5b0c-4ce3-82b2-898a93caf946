<?php $__env->startSection('title', 'Tag Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="max-w-6xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Tag Management</h1>
                <p class="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">Manage tags for organizing businesses</p>
            </div>
            <div class="flex space-x-3">
                <?php if(auth()->user()->hasPermission('manage_businesses')): ?>
                    <a href="<?php echo e(route('tags.create')); ?>" 
                       class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Create Tag
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <!-- Search and Filter Form -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6 transition duration-150 ease-in-out">
            <form method="GET" action="<?php echo e(route('tags.index')); ?>" class="flex flex-wrap gap-4">
                <div class="flex-1 min-w-64">
                    <input type="text" name="search" value="<?php echo e(request('search')); ?>" 
                           placeholder="Search tags..." 
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 dark:bg-gray-700 dark:text-white">
                </div>
                <div>
                    <select name="status" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 dark:bg-gray-700 dark:text-white">
                        <option value="">All Status</option>
                        <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>Active</option>
                        <option value="inactive" <?php echo e(request('status') === 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                    </select>
                </div>
                <button type="submit" class="bg-gray-50 dark:bg-gray-7000 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Search
                </button>
                <?php if(request()->hasAny(['search', 'status'])): ?>
                    <a href="<?php echo e(route('tags.index')); ?>" class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-7000 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Clear
                    </a>
                <?php endif; ?>
            </form>
        </div>

        <!-- Tags List -->
        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
            <?php if($tags->count() > 0): ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
                    <?php $__currentLoopData = $tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow">
                            <div class="flex items-start justify-between mb-3">
                                <div class="flex items-center">
                                    <div class="w-4 h-4 rounded-full mr-3" style="background-color: <?php echo e($tag->color); ?>;"></div>
                                    <div>
                                        <h3 class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e($tag->name); ?></h3>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500"><?php echo e($tag->businesses_count); ?> businesses</p>
                                    </div>
                                </div>
                                <div class="flex space-x-1">
                                    <a href="<?php echo e(route('tags.show', $tag)); ?>" 
                                       class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:text-blue-200 dark:hover:text-blue-300 text-xs">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <?php if(auth()->user()->hasPermission('manage_businesses')): ?>
                                        <a href="<?php echo e(route('tags.edit', $tag)); ?>" 
                                           class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 text-xs">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <?php if($tag->businesses_count == 0): ?>
                                            <form method="POST" action="<?php echo e(route('tags.destroy', $tag)); ?>" 
                                                  onsubmit="return confirm('Are you sure you want to delete this tag?')" class="inline">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="text-red-600 dark:text-red-400 hover:text-red-800 dark:text-red-200 text-xs">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <?php if($tag->description): ?>
                                <p class="text-xs text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mb-3"><?php echo e($tag->description); ?></p>
                            <?php endif; ?>
                            
                            <div class="flex items-center justify-between">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo e($tag->is_active ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'); ?> transition duration-150 ease-in-out">
                                    <?php echo e($tag->is_active ? 'Active' : 'Inactive'); ?>

                                </span>
                                <span class="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                                    Created <?php echo e($tag->created_at->diffForHumans()); ?>

                                </span>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <!-- Pagination -->
                <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                    <?php echo e($tags->links()); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-12">
                    <div class="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 text-lg">No tags found.</div>
                    <?php if(auth()->user()->hasPermission('manage_businesses')): ?>
                        <a href="<?php echo e(route('tags.create')); ?>" 
                           class="mt-4 inline-block bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                            Create First Tag
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Tag Statistics -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-tags text-blue-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Total Tags</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($tags->total()); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Active Tags</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($tags->where('is_active', true)->count()); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-building text-purple-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Used Tags</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($tags->where('businesses_count', '>', 0)->count()); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-palette text-orange-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Colors Used</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($tags->pluck('color')->unique()->count()); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Popular Tags -->
        <?php if($tags->count() > 0): ?>
            <div class="mt-8 bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                <div class="px-4 py-5 sm:px-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Most Popular Tags</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Tags with the most business assignments</p>
                </div>
                <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:px-6">
                    <div class="flex flex-wrap gap-2">
                        <?php $__currentLoopData = $tags->sortByDesc('businesses_count')->take(10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium" 
                                  style="background-color: <?php echo e($tag->color); ?>20; color: <?php echo e($tag->color); ?>;">
                                <?php echo e($tag->name); ?> (<?php echo e($tag->businesses_count); ?>)
                            </span>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/business/Views/tags/index.blade.php ENDPATH**/ ?>