<?php $__env->startSection('title', 'Backup Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Backup Management</h1>
                <p class="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-2">Create, manage, and restore system backups</p>
                <div class="mt-2 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                    <i class="fas fa-folder mr-1"></i>
                    <strong>Storage Location:</strong> <?php echo e(storage_path('app/backups')); ?>

                </div>
            </div>
            <button onclick="showCreateBackupModal()" class="bg-green-500 dark:bg-green-600 hover:bg-green-700 dark:hover:bg-green-500 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-plus mr-2"></i>Create Backup
            </button>
        </div>
    </div>

    <!-- Backup Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-archive text-blue-500 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Total Backups</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($stats['total_backups']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-hdd text-purple-500 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Storage Used</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($stats['total_size_formatted']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-clock text-green-500 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Latest Backup</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white">
                                <?php echo e($stats['newest_backup'] ? \Carbon\Carbon::parse($stats['newest_backup'])->diffForHumans() : 'None'); ?>

                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-history text-orange-500 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Oldest Backup</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white">
                                <?php echo e($stats['oldest_backup'] ? \Carbon\Carbon::parse($stats['oldest_backup'])->diffForHumans() : 'None'); ?>

                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Messages -->
    <div id="statusMessage" class="hidden mb-6"></div>

    <!-- Backup Configuration -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 transition duration-150 ease-in-out">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Backup Configuration</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400"><?php echo e(config('settings.max_backup_files', 10)); ?></div>
                    <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">Max Backup Files</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600 dark:text-green-400"><?php echo e(config('settings.backup_retention_days', 30)); ?></div>
                    <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">Retention Days</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">ZIP</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">Compression Format</div>
                </div>
            </div>

            <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">What's Included in Backups:</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                        <span class="text-sm text-gray-700 dark:text-gray-300">Database (all tables)</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                        <span class="text-sm text-gray-700 dark:text-gray-300">Application files</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                        <span class="text-sm text-gray-700 dark:text-gray-300">Configuration files</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                        <span class="text-sm text-gray-700 dark:text-gray-300">User uploads</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Backup List -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg transition duration-150 ease-in-out">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Available Backups</h3>
        </div>
        <div class="overflow-x-auto">
            <?php if(count($backups) > 0): ?>
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700 transition duration-150 ease-in-out">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Backup File</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Created</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Size</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700 transition duration-150 ease-in-out">
                        <?php $__currentLoopData = $backups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $backup): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-700 transition duration-150 ease-in-out">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <i class="fas fa-file-archive text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mr-3"></i>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e($backup['filename']); ?></div>
                                        <?php if(isset($backup['info']['description']) && $backup['info']['description']): ?>
                                            <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500"><?php echo e($backup['info']['description']); ?></div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900 dark:text-white"><?php echo e(\Carbon\Carbon::parse($backup['created_at'])->format('M j, Y')); ?></div>
                                <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500"><?php echo e(\Carbon\Carbon::parse($backup['created_at'])->format('g:i A')); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                <?php echo e(number_format($backup['size'] / 1024 / 1024, 2)); ?> MB
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php if(isset($backup['info']['includes_files']) && $backup['info']['includes_files']): ?>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 transition duration-150 ease-in-out">
                                        Full Backup
                                    </span>
                                <?php else: ?>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 transition duration-150 ease-in-out">
                                        Database Only
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="downloadBackup('<?php echo e($backup['filename']); ?>')" class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300">
                                    <i class="fas fa-download mr-1"></i>Download
                                </button>
                                <button onclick="showBackupInfo('<?php echo e($backup['filename']); ?>')" class="text-green-600 dark:text-green-400 hover:text-green-900">
                                    <i class="fas fa-info-circle mr-1"></i>Info
                                </button>
                                <button onclick="restoreBackup('<?php echo e($backup['filename']); ?>')" class="text-orange-600 dark:text-orange-400 hover:text-orange-900">
                                    <i class="fas fa-undo mr-1"></i>Restore
                                </button>
                                <button onclick="deleteBackup('<?php echo e($backup['filename']); ?>')" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300">
                                    <i class="fas fa-trash mr-1"></i>Delete
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            <?php else: ?>
                <div class="text-center py-12">
                    <i class="fas fa-archive text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 text-6xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Backups Found</h3>
                    <p class="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mb-4">Create your first backup to get started</p>
                    <button onclick="showCreateBackupModal()" class="bg-green-500 dark:bg-green-600 hover:bg-green-700 dark:hover:bg-green-500 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-plus mr-2"></i>Create Backup
                    </button>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Create Backup Modal -->
<div id="createBackupModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50 transition duration-150 ease-in-out">
    <div class="bg-white dark:bg-gray-800 rounded-lg max-w-md mx-auto transition duration-150 ease-in-out">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Create New Backup</h3>
        </div>
        <form id="createBackupForm" class="p-6">
            <div class="mb-4">
                <label class="flex items-center">
                    <input type="checkbox" id="includeFiles" checked class="rounded border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-400 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 dark:bg-gray-700 dark:text-white">
                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Include application files</span>
                </label>
                <p class="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">Uncheck to create database-only backup (faster)</p>
            </div>
            <div class="mb-6">
                <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description (optional)</label>
                <input type="text" id="description" class="w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:bg-gray-700 dark:text-white" placeholder="Backup description...">
            </div>
            <div class="flex justify-end space-x-3">
                <button type="button" onclick="hideCreateBackupModal()" class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-7000 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Cancel
                </button>
                <button type="submit" class="bg-green-500 dark:bg-green-600 hover:bg-green-700 dark:hover:bg-green-500 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-download mr-2"></i>Create Backup
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Backup Info Modal -->
<div id="backupInfoModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50 transition duration-150 ease-in-out">
    <div class="bg-white dark:bg-gray-800 rounded-lg max-w-lg mx-auto transition duration-150 ease-in-out">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Backup Information</h3>
            <button onclick="hideBackupInfoModal()" class="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div id="backupInfoContent" class="p-6">
            <!-- Backup info will be loaded here -->
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div id="loadingModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50 transition duration-150 ease-in-out">
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-sm mx-auto transition duration-150 ease-in-out">
        <div class="flex items-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
            <span id="loadingText">Processing...</span>
        </div>
    </div>
</div>

<script>
function showCreateBackupModal() {
    document.getElementById('createBackupModal').classList.remove('hidden');
    document.getElementById('createBackupModal').classList.add('flex');
}

function hideCreateBackupModal() {
    document.getElementById('createBackupModal').classList.add('hidden');
    document.getElementById('createBackupModal').classList.remove('flex');
    document.getElementById('createBackupForm').reset();
}

function showBackupInfoModal() {
    document.getElementById('backupInfoModal').classList.remove('hidden');
    document.getElementById('backupInfoModal').classList.add('flex');
}

function hideBackupInfoModal() {
    document.getElementById('backupInfoModal').classList.add('hidden');
    document.getElementById('backupInfoModal').classList.remove('flex');
}

document.getElementById('createBackupForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const includeFiles = document.getElementById('includeFiles').checked;
    const description = document.getElementById('description').value;
    
    hideCreateBackupModal();
    showLoading('Creating backup...');
    
    fetch('<?php echo e(route('settings.backups.create')); ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            include_files: includeFiles,
            description: description
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        showStatus(data);
        
        if (data.success) {
            setTimeout(() => location.reload(), 2000);
        }
    })
    .catch(error => {
        hideLoading();
        showStatus({
            success: false,
            message: 'Backup creation failed: ' + error.message
        });
    });
});

function downloadBackup(filename) {
    window.location.href = `<?php echo e(url('settings/backups/download')); ?>/${filename}`;
}

function showBackupInfo(filename) {
    showLoading('Loading backup information...');
    
    fetch(`<?php echo e(url('settings/backups/status')); ?>/${filename}`)
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                displayBackupInfo(data.backup_info);
                showBackupInfoModal();
            } else {
                showStatus(data);
            }
        })
        .catch(error => {
            hideLoading();
            showStatus({
                success: false,
                message: 'Failed to load backup info: ' + error.message
            });
        });
}

function displayBackupInfo(info) {
    let html = '<div class="space-y-3">';
    
    Object.entries(info).forEach(([key, value]) => {
        const label = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        html += `
            <div class="flex justify-between py-2 border-b border-gray-100">
                <span class="font-medium text-gray-700 dark:text-gray-300">${label}:</span>
                <span class="text-gray-900 dark:text-white">${value}</span>
            </div>
        `;
    });
    
    html += '</div>';
    document.getElementById('backupInfoContent').innerHTML = html;
}

function restoreBackup(filename) {
    if (confirm(`Restore from backup "${filename}"? This will overwrite all current data. This action cannot be undone.`)) {
        showLoading('Restoring backup...');
        
        fetch(`<?php echo e(url('settings/backups/restore')); ?>/${filename}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                confirm: true
            })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            showStatus(data);
            
            if (data.success) {
                setTimeout(() => {
                    alert('Backup restored successfully. The application will reload.');
                    location.reload();
                }, 2000);
            }
        })
        .catch(error => {
            hideLoading();
            showStatus({
                success: false,
                message: 'Backup restore failed: ' + error.message
            });
        });
    }
}

function deleteBackup(filename) {
    if (confirm(`Delete backup "${filename}"? This action cannot be undone.`)) {
        showLoading('Deleting backup...');
        
        fetch(`<?php echo e(url('settings/backups')); ?>/${filename}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            showStatus(data);
            
            if (data.success) {
                setTimeout(() => location.reload(), 1500);
            }
        })
        .catch(error => {
            hideLoading();
            showStatus({
                success: false,
                message: 'Backup deletion failed: ' + error.message
            });
        });
    }
}

function showStatus(data) {
    const statusDiv = document.getElementById('statusMessage');
    statusDiv.classList.remove('hidden');
    
    if (data.success) {
        statusDiv.className = 'mb-6 p-4 rounded-md border bg-green-50 border-green-200';
        statusDiv.innerHTML = `
            <div class="flex">
                <i class="fas fa-check-circle text-green-400 mr-3 mt-1"></i>
                <div>
                    <h4 class="text-green-800 dark:text-green-200 font-medium">Success</h4>
                    <p class="text-green-700 mt-1">${data.message}</p>
                </div>
            </div>
        `;
    } else {
        statusDiv.className = 'mb-6 p-4 rounded-md border bg-red-50 border-red-200';
        statusDiv.innerHTML = `
            <div class="flex">
                <i class="fas fa-times-circle text-red-400 mr-3 mt-1"></i>
                <div>
                    <h4 class="text-red-800 dark:text-red-200 font-medium">Error</h4>
                    <p class="text-red-700 mt-1">${data.message}</p>
                </div>
            </div>
        `;
    }
}

function showLoading(text) {
    document.getElementById('loadingText').textContent = text;
    document.getElementById('loadingModal').classList.remove('hidden');
    document.getElementById('loadingModal').classList.add('flex');
}

function hideLoading() {
    document.getElementById('loadingModal').classList.add('hidden');
    document.getElementById('loadingModal').classList.remove('flex');
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/settings/Views/backups.blade.php ENDPATH**/ ?>