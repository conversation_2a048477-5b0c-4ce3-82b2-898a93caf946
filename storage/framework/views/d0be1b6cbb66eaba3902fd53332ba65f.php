<?php $__env->startSection('title', $product->name); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="flex justify-between items-start mb-6">
            <div class="flex items-center">
                <?php if($product->icon): ?>
                    <i class="<?php echo e($product->icon); ?> text-4xl text-blue-500 mr-4"></i>
                <?php else: ?>
                    <i class="fas fa-box text-4xl text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mr-4"></i>
                <?php endif; ?>
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white"><?php echo e($product->name); ?></h1>
                    <p class="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1"><?php echo e($product->slug); ?></p>
                    <div class="flex items-center mt-2">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($product->is_active ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'); ?> transition duration-150 ease-in-out">
                            <?php echo e($product->is_active ? 'Active' : 'Inactive'); ?>

                        </span>
                        <?php if($product->latest_release): ?>
                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 transition duration-150 ease-in-out">
                                Latest: <?php echo e($product->latest_release->formatted_version); ?>

                            </span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="flex space-x-3">
                <a href="<?php echo e(route('products.index')); ?>" 
                   class="bg-gray-50 dark:bg-gray-7000 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Back to Products
                </a>
                <?php if(auth()->user()->hasPermission('manage_products')): ?>
                    <a href="<?php echo e(route('products.edit', $product)); ?>" 
                       class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-edit mr-2"></i>Edit Product
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-building text-blue-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Businesses</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($stats['total_businesses']); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-file text-green-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Documents</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($stats['current_documents']); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-tag text-purple-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Releases</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($stats['published_releases']); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-dollar-sign text-yellow-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Pricing Items</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($stats['pricing_items']); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg mb-6 transition duration-150 ease-in-out">
            <div class="border-b border-gray-200 dark:border-gray-700">
                <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                    <a href="#overview" class="tab-link border-blue-500 text-blue-600 dark:text-blue-400 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="overview">
                        Overview
                    </a>
                    <a href="#documents" class="tab-link border-transparent text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-700 dark:text-gray-300 hover:border-gray-300 dark:border-gray-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="documents">
                        Documents (<?php echo e($stats['current_documents']); ?>)
                    </a>
                    <a href="#releases" class="tab-link border-transparent text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-700 dark:text-gray-300 hover:border-gray-300 dark:border-gray-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="releases">
                        Releases (<?php echo e($stats['published_releases']); ?>)
                    </a>
                    <a href="#pricing" class="tab-link border-transparent text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-700 dark:text-gray-300 hover:border-gray-300 dark:border-gray-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="pricing">
                        Pricing (<?php echo e($stats['pricing_items']); ?>)
                    </a>
                    <a href="#businesses" class="tab-link border-transparent text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-700 dark:text-gray-300 hover:border-gray-300 dark:border-gray-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="businesses">
                        Businesses (<?php echo e($stats['total_businesses']); ?>)
                    </a>
                </nav>
            </div>
        </div>

        <!-- Tab Content -->
        <div id="tab-content">
            <!-- Overview Tab -->
            <div id="overview-tab" class="tab-content">
                <div class="grid grid-cols-1 gap-6">
                    <!-- Product Information -->
                    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                        <div class="px-4 py-5 sm:px-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Product Information</h3>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Basic details and description</p>
                        </div>
                        <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-0">
                            <dl class="sm:divide-y sm:divide-gray-200 dark:divide-gray-700">
                                <?php if($product->description): ?>
                                    <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Description</dt>
                                        <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2"><?php echo e($product->description); ?></dd>
                                    </div>
                                <?php endif; ?>
                                <?php if($product->target_audience): ?>
                                    <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Target Audience</dt>
                                        <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2"><?php echo e($product->target_audience); ?></dd>
                                    </div>
                                <?php endif; ?>
                                <?php if($product->use_cases): ?>
                                    <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Use Cases</dt>
                                        <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2"><?php echo e($product->use_cases); ?></dd>
                                    </div>
                                <?php endif; ?>
                                <?php if($product->reference_links && count($product->reference_links) > 0): ?>
                                    <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Reference Links</dt>
                                        <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                                            <ul class="space-y-1">
                                                <?php $__currentLoopData = $product->reference_links; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $link): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <li>
                                                        <a href="<?php echo e($link); ?>" target="_blank" class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300">
                                                            <?php echo e($link); ?> <i class="fas fa-external-link-alt ml-1"></i>
                                                        </a>
                                                    </li>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </ul>
                                        </dd>
                                    </div>
                                <?php endif; ?>
                                <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Created By</dt>
                                    <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                                        <?php echo e($product->creator->name); ?> on <?php echo e($product->created_at->format('M d, Y')); ?>

                                    </dd>
                                </div>
                            </dl>
                        </div>
                    </div>


                </div>
            </div>

            <!-- Documents Tab -->
            <div id="documents-tab" class="tab-content hidden">
                <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                    <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                        <div>
                            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Product Documents</h3>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Current version documents</p>
                        </div>
                        <?php if(auth()->user()->hasPermission('manage_product_documents')): ?>
                            <a href="<?php echo e(route('products.documents.create', $product)); ?>" 
                               class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                                <i class="fas fa-plus mr-2"></i>Upload Document
                            </a>
                        <?php endif; ?>
                    </div>
                    <div class="border-t border-gray-200 dark:border-gray-700">
                        <?php if($product->currentDocuments->count() > 0): ?>
                            <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                                <?php $__currentLoopData = $product->currentDocuments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $document): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li class="px-4 py-4 sm:px-6">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <i class="<?php echo e($document->file_icon); ?> mr-3"></i>
                                                <div>
                                                    <p class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e($document->name); ?></p>
                                                    <p class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                                                        <?php echo e($document->category_label); ?> • <?php echo e($document->formatted_file_size); ?> • v<?php echo e($document->version); ?>

                                                    </p>
                                                </div>
                                            </div>
                                            <div class="flex space-x-2">
                                                <?php if(auth()->user()->hasPermission('view_product_documents')): ?>
                                                    <a href="<?php echo e(route('products.documents.download', [$product, $document])); ?>" 
                                                       class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 text-sm">
                                                        Download
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                            <div class="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-right sm:px-6 transition duration-150 ease-in-out">
                                <a href="<?php echo e(route('products.documents.index', $product)); ?>" 
                                   class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 text-sm font-medium">
                                    View All Documents →
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-12">
                                <i class="fas fa-file text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 text-4xl mb-4"></i>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No documents yet</h3>
                                <p class="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mb-6">Upload your first product document to get started.</p>
                                <?php if(auth()->user()->hasPermission('manage_product_documents')): ?>
                                    <a href="<?php echo e(route('products.documents.create', $product)); ?>" 
                                       class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                                        <i class="fas fa-plus mr-2"></i>Upload Document
                                    </a>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Releases Tab -->
            <div id="releases-tab" class="tab-content hidden">
                <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                    <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                        <div>
                            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Product Releases</h3>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Version history and release notes</p>
                        </div>
                        <?php if(auth()->user()->hasPermission('manage_product_releases')): ?>
                            <a href="<?php echo e(route('products.releases.create', $product)); ?>"
                               class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                                <i class="fas fa-plus mr-2"></i>Create Release
                            </a>
                        <?php endif; ?>
                    </div>
                    <div class="border-t border-gray-200 dark:border-gray-700">
                        <?php if($product->publishedReleases->count() > 0): ?>
                            <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                                <?php $__currentLoopData = $product->publishedReleases; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $release): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li class="px-4 py-4 sm:px-6">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($release->release_type_badge_class); ?>">
                                                        <?php echo e($release->release_type_label); ?>

                                                    </span>
                                                </div>
                                                <div class="ml-4">
                                                    <h4 class="text-lg font-medium text-gray-900 dark:text-white">
                                                        <?php echo e($release->formatted_version); ?> - <?php echo e($release->title); ?>

                                                    </h4>
                                                    <?php if($release->description): ?>
                                                        <p class="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1"><?php echo e($release->short_description); ?></p>
                                                    <?php endif; ?>
                                                    <div class="flex items-center mt-2 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                                                        <span>Released <?php echo e($release->release_date->format('M d, Y')); ?></span>
                                                        <?php if($release->total_changes_count > 0): ?>
                                                            <span class="ml-4"><?php echo e($release->total_changes_count); ?> changes</span>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="flex space-x-2">
                                                <?php if(auth()->user()->hasPermission('view_product_releases')): ?>
                                                    <a href="<?php echo e(route('products.releases.show', [$product, $release])); ?>"
                                                       class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 text-sm font-medium">
                                                        View Details
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                            <div class="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-right sm:px-6 transition duration-150 ease-in-out">
                                <a href="<?php echo e(route('products.releases.index', $product)); ?>"
                                   class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 text-sm font-medium">
                                    View All Releases →
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-12">
                                <i class="fas fa-tag text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 text-4xl mb-4"></i>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No releases yet</h3>
                                <p class="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mb-6">Create your first product release to track version history.</p>
                                <?php if(auth()->user()->hasPermission('manage_product_releases')): ?>
                                    <a href="<?php echo e(route('products.releases.create', $product)); ?>"
                                       class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                                        <i class="fas fa-plus mr-2"></i>Create Release
                                    </a>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Pricing Tab -->
            <div id="pricing-tab" class="tab-content hidden">
                <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                    <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                        <div>
                            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Pricing Items</h3>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Available pricing items and plans</p>
                        </div>
                        <?php if(auth()->user()->hasPermission('manage_products')): ?>
                            <a href="<?php echo e(route('products.pricing.create', $product)); ?>"
                               class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                                <i class="fas fa-plus mr-2"></i>Add Pricing Item
                            </a>
                        <?php endif; ?>
                    </div>
                    <div class="border-t border-gray-200 dark:border-gray-700">
                        <?php if($product->activePricingItems->count() > 0): ?>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                    <thead class="bg-gray-50 dark:bg-gray-700 transition duration-150 ease-in-out">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                                                Name
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                                                Pricing Model
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                                                Price
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                                                Unit Type
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                                                Currency
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                                                Status
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                                                Actions
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700 transition duration-150 ease-in-out">
                                        <?php $__currentLoopData = $product->activePricingItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pricing): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-700 <?php echo e($pricing->is_popular ? 'bg-blue-50' : ''); ?> transition duration-150 ease-in-out">
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="flex items-center">
                                                        <div>
                                                            <div class="text-sm font-medium text-gray-900 dark:text-white flex items-center">
                                                                <?php echo e($pricing->name); ?>

                                                                <?php if($pricing->is_popular): ?>
                                                                    <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 transition duration-150 ease-in-out">
                                                                        Popular
                                                                    </span>
                                                                <?php endif; ?>
                                                            </div>
                                                            <?php if($pricing->description): ?>
                                                                <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 max-w-xs truncate">
                                                                    <?php echo e($pricing->description); ?>

                                                                </div>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 transition duration-150 ease-in-out">
                                                        <?php echo e($pricing->pricing_model_label); ?>

                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm text-gray-900 dark:text-white">
                                                        <?php if($pricing->price !== null): ?>
                                                            <?php echo e($pricing->formatted_price); ?>

                                                        <?php else: ?>
                                                            <span class="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Contact for pricing</span>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm text-gray-900 dark:text-white">
                                                        <?php if($pricing->unit_type): ?>
                                                            <?php echo e($pricing->unit_type_label); ?>

                                                        <?php else: ?>
                                                            <span class="text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">-</span>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm text-gray-900 dark:text-white"><?php echo e($pricing->currency); ?></div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($pricing->is_active ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'); ?> transition duration-150 ease-in-out">
                                                        <?php echo e($pricing->is_active ? 'Active' : 'Inactive'); ?>

                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                    <?php if(auth()->user()->hasPermission('manage_products')): ?>
                                                        <a href="<?php echo e(route('products.pricing.edit', [$product, $pricing])); ?>"
                                                           class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300">
                                                            Edit
                                                        </a>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-right sm:px-6 transition duration-150 ease-in-out">
                                <a href="<?php echo e(route('products.pricing.index', $product)); ?>"
                                   class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 text-sm font-medium">
                                    Manage All Pricing →
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-12">
                                <i class="fas fa-dollar-sign text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 text-4xl mb-4"></i>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No pricing items yet</h3>
                                <p class="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mb-6">Create your first pricing item to define product costs.</p>
                                <?php if(auth()->user()->hasPermission('manage_products')): ?>
                                    <a href="<?php echo e(route('products.pricing.create', $product)); ?>"
                                       class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                                        <i class="fas fa-plus mr-2"></i>Add Pricing Item
                                    </a>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Businesses Tab -->
            <div id="businesses-tab" class="tab-content hidden">
                <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                    <div class="px-4 py-5 sm:px-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Assigned Businesses</h3>
                        <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Businesses using this product</p>
                    </div>
                    <div class="border-t border-gray-200 dark:border-gray-700">
                        <?php if($product->businesses->count() > 0): ?>
                            <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                                <?php $__currentLoopData = $product->businesses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $business): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li class="px-4 py-4 sm:px-6">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($business->pivot->status === 'active' ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'); ?> transition duration-150 ease-in-out">
                                                        <?php echo e(ucfirst($business->pivot->status)); ?>

                                                    </span>
                                                </div>
                                                <div class="ml-4">
                                                    <h4 class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($business->name); ?></h4>
                                                    <div class="flex items-center mt-1 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                                                        <?php if($business->pivot->start_date): ?>
                                                            <span>Started <?php echo e(\Carbon\Carbon::parse($business->pivot->start_date)->format('M d, Y')); ?></span>
                                                        <?php endif; ?>
                                                        <?php if($business->pivot->product_version): ?>
                                                            <span class="ml-4">Version <?php echo e($business->pivot->product_version); ?></span>
                                                        <?php endif; ?>
                                                    </div>
                                                    <?php if($business->pivot->notes): ?>
                                                        <p class="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1"><?php echo e($business->pivot->notes); ?></p>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <div class="flex space-x-2">
                                                <?php if(auth()->user()->hasPermission('view_businesses')): ?>
                                                    <a href="<?php echo e(route('business.show', $business)); ?>"
                                                       class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 text-sm font-medium">
                                                        View Business
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        <?php else: ?>
                            <div class="text-center py-12">
                                <i class="fas fa-building text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 text-4xl mb-4"></i>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No businesses assigned</h3>
                                <p class="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mb-6">This product hasn't been assigned to any businesses yet.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Tab switching functionality
document.addEventListener('DOMContentLoaded', function() {
    const tabLinks = document.querySelectorAll('.tab-link');
    const tabContents = document.querySelectorAll('.tab-content');

    tabLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active classes from all tabs
            tabLinks.forEach(l => {
                l.classList.remove('border-blue-500', 'text-blue-600 dark:text-blue-400');
                l.classList.add('border-transparent', 'text-gray-500 dark:text-gray-400 dark:text-gray-500');
            });
            
            // Hide all tab contents
            tabContents.forEach(content => {
                content.classList.add('hidden');
            });
            
            // Add active class to clicked tab
            this.classList.remove('border-transparent', 'text-gray-500 dark:text-gray-400 dark:text-gray-500');
            this.classList.add('border-blue-500', 'text-blue-600 dark:text-blue-400');
            
            // Show corresponding tab content
            const tabId = this.getAttribute('data-tab') + '-tab';
            const tabContent = document.getElementById(tabId);
            if (tabContent) {
                tabContent.classList.remove('hidden');
            }
        });
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/products/Views/show.blade.php ENDPATH**/ ?>