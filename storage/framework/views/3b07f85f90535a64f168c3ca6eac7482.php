<?php $__env->startSection('title', 'List: ' . $list->name); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white"><?php echo e($list->name); ?></h1>
        <div class="flex space-x-3">
            <?php if(auth()->user()->hasPermission('manage_clickup_lists')): ?>
                <a href="<?php echo e(route('clickup.lists.edit', $list)); ?>"
                   class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-edit mr-2"></i>
                    Edit List
                </a>
            <?php endif; ?>
            <?php if($list->url): ?>
                <a href="<?php echo e($list->url); ?>" target="_blank"
                   class="bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-external-link-alt mr-2"></i>
                    View in ClickUp
                </a>
            <?php endif; ?>
            <a href="<?php echo e(route('clickup.lists.index')); ?>"
               class="bg-gray-600 dark:bg-gray-700 hover:bg-gray-700 dark:hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Lists
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2">
            <!-- List Details -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700 mb-6">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">List Details</h3>
                </div>
                <div class="p-6">
                    <?php if($list->description): ?>
                        <div class="mb-6">
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Description</h4>
                            <div class="text-sm text-gray-900 dark:text-white"><?php echo e($list->description); ?></div>
                        </div>
                    <?php endif; ?>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Status</h4>
                            <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full 
                                <?php echo e($list->status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'); ?>">
                                <?php echo e(ucfirst($list->status)); ?>

                            </span>
                        </div>

                        <?php if($list->priority): ?>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Priority</h4>
                                <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full 
                                    <?php switch($list->priority):
                                        case ('urgent'): ?>
                                            bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100
                                            <?php break; ?>
                                        <?php case ('high'): ?>
                                            bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-100
                                            <?php break; ?>
                                        <?php case ('normal'): ?>
                                            bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100
                                            <?php break; ?>
                                        <?php case ('low'): ?>
                                            bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300
                                            <?php break; ?>
                                    <?php endswitch; ?>">
                                    <?php echo e(ucfirst($list->priority)); ?>

                                </span>
                            </div>
                        <?php endif; ?>

                        <?php if($list->due_date): ?>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Due Date</h4>
                                <div class="text-sm text-gray-900 dark:text-white">
                                    <?php echo e($list->due_date->format('M d, Y')); ?>

                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if($list->start_date): ?>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Start Date</h4>
                                <div class="text-sm text-gray-900 dark:text-white">
                                    <?php echo e($list->start_date->format('M d, Y')); ?>

                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Recent Tasks -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Recent Tasks</h3>
                        <?php if(auth()->user()->hasPermission('view_clickup_tasks')): ?>
                            <a href="<?php echo e(route('clickup.tasks.index', ['list_id' => $list->id])); ?>" 
                               class="text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 text-sm">
                                View All Tasks →
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="p-6">
                    <?php if($list->tasks->count() > 0): ?>
                        <div class="space-y-4">
                            <?php $__currentLoopData = $list->tasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-<?php echo e($task->is_completed ? 'check-circle text-green-500' : 'circle text-gray-400'); ?>"></i>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900 dark:text-white <?php echo e($task->is_completed ? 'line-through' : ''); ?>">
                                                <?php echo e($task->name); ?>

                                            </div>
                                            <div class="text-xs text-gray-500 dark:text-gray-400">
                                                <?php echo e(ucfirst($task->status)); ?>

                                                <?php if($task->priority): ?>
                                                    • <?php echo e(ucfirst($task->priority)); ?> Priority
                                                <?php endif; ?>
                                                <?php if($task->due_date): ?>
                                                    • Due <?php echo e($task->due_date->format('M d')); ?>

                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex space-x-2">
                                        <?php if(auth()->user()->hasPermission('view_clickup_tasks')): ?>
                                            <a href="<?php echo e(route('clickup.tasks.show', $task)); ?>" 
                                               class="text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        <?php endif; ?>
                                        <?php if($task->url): ?>
                                            <a href="<?php echo e($task->url); ?>" target="_blank" 
                                               class="text-green-600 dark:text-green-400 hover:text-green-500 dark:hover:text-green-300">
                                                <i class="fas fa-external-link-alt"></i>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-8">
                            <i class="fas fa-tasks text-gray-400 text-4xl mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Tasks Found</h3>
                            <p class="text-gray-500 dark:text-gray-400">This list doesn't have any tasks yet.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">
            <!-- Product Manager -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700 mb-6">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Product Manager</h3>
                </div>
                <div class="p-6">
                    <?php if($list->productManager): ?>
                        <div class="flex items-center space-x-3">
                            <?php if($list->productManager->avatar_url): ?>
                                <img src="<?php echo e($list->productManager->avatar_url); ?>" alt="<?php echo e($list->productManager->name); ?>" 
                                     class="w-10 h-10 rounded-full">
                            <?php else: ?>
                                <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                                    <span class="text-white font-bold">
                                        <?php echo e(substr($list->productManager->name, 0, 1)); ?>

                                    </span>
                                </div>
                            <?php endif; ?>
                            <div>
                                <div class="text-sm font-medium text-gray-900 dark:text-white">
                                    <?php echo e($list->productManager->name); ?>

                                </div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    <?php echo e($list->productManager->email); ?>

                                </div>
                                <?php if($list->productManager->role): ?>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">
                                        <?php echo e($list->productManager->role); ?>

                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php if(auth()->user()->hasPermission('view_clickup_product_managers')): ?>
                            <div class="mt-4">
                                <a href="<?php echo e(route('clickup.product-managers.show', $list->productManager)); ?>" 
                                   class="text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 text-sm">
                                    View Profile →
                                </a>
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-user-slash text-gray-400 text-2xl mb-2"></i>
                            <p class="text-sm text-gray-500 dark:text-gray-400">No product manager assigned</p>
                            <?php if(auth()->user()->hasPermission('manage_clickup_lists')): ?>
                                <button class="mt-2 text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 text-sm">
                                    Assign Manager
                                </button>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Performance Metrics -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700 mb-6">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Performance Metrics</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-500 dark:text-gray-400">Total Tasks</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e($metrics['total_tasks']); ?></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-500 dark:text-gray-400">Active Tasks</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e($metrics['active_tasks']); ?></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-500 dark:text-gray-400">Completed Tasks</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e($metrics['completed_tasks']); ?></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-500 dark:text-gray-400">Overdue Tasks</span>
                            <span class="text-sm font-medium text-red-600 dark:text-red-400"><?php echo e($metrics['overdue_tasks']); ?></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-500 dark:text-gray-400">Completion Rate</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e($metrics['completion_rate']); ?>%</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-500 dark:text-gray-400">Avg Task Age</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e($metrics['average_task_age']); ?> days</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sync Information -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Sync Information</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Last Synced</h4>
                            <div class="text-sm text-gray-900 dark:text-white">
                                <?php echo e($list->last_synced_at ? $list->last_synced_at->diffForHumans() : 'Never'); ?>

                            </div>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Sync Status</h4>
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                <?php echo e($list->sync_status === 'success' ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 
                                   ($list->sync_status === 'failed' ? 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100' : 
                                    'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100')); ?>">
                                <?php echo e(ucfirst($list->sync_status)); ?>

                            </span>
                        </div>
                        <?php if($list->clickup_id): ?>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">ClickUp ID</h4>
                                <div class="text-sm text-gray-900 dark:text-white font-mono"><?php echo e($list->clickup_id); ?></div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/clickup/Views/lists/show.blade.php ENDPATH**/ ?>