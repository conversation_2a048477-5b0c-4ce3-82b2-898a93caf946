<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" class="<?php echo e(auth()->user() ? auth()->user()->getThemeClass() : ''); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo e(config('app.name', 'Business')); ?></title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo e(asset('favicon.ico')); ?>">

    <!-- Tailwind CSS CDN with JIT -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <!-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"> -->
    <link rel="stylesheet" href="<?php echo e(asset('css/all.css')); ?>">
    
    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>

    <!-- Custom Styles -->
    <style>
        [x-cloak] { display: none !important; }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }

        ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* Smooth transitions */
        * {
            transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
            transition-duration: 150ms;
        }

        /* Sidebar styles */
        .sidebar-transition {
            transition: transform 0.3s ease-in-out;
        }
    </style>

    <?php echo $__env->yieldPushContent('styles'); ?>

    <!-- Vite Assets -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>
<body class="bg-gray-50 dark:bg-gray-900 font-sans antialiased">
    <div class="flex flex-row h-screen bg-gray-50 dark:bg-gray-900">
        <!-- Sidebar -->
        <div class="hidden md:flex md:w-64 md:flex-shrink-0">
            <div class="flex flex-col h-full w-full pt-5 bg-white dark:bg-gray-800 overflow-y-auto border-r border-gray-200 dark:border-gray-700">
                <!-- Logo -->
                <div class="flex items-center flex-shrink-0 px-4">
                    <a href="<?php echo e(url('/')); ?>" class="flex items-center">
                        <i class="fas fa-cube text-primary-600 dark:text-primary-400 text-2xl mr-3"></i>
                        <span class="text-xl font-bold text-gray-900 dark:text-white"><?php echo e(config('app.name')); ?></span>
                    </a>
                </div>

                <!-- Navigation -->
                <div class="mt-8 flex-1 flex flex-col overflow-hidden">
                    <nav class="flex-1 px-2 space-y-1 overflow-y-auto">
                        <!-- Dynamic Navigation from NavigationMenu -->
                        <div class="space-y-1">
                            <?php if(isset($navigationTree) && count($navigationTree) > 0): ?>
                                <?php $__currentLoopData = $navigationTree; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php echo $__env->make('partials.navigation-item', ['item' => $item], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                <!-- Fallback navigation if no dynamic navigation is available -->
                                <a href="<?php echo e(url('/')); ?>" class="group flex items-center px-2 py-2 text-sm font-medium rounded-md <?php echo e(request()->is('/') ? 'bg-primary-100 dark:bg-primary-900 text-primary-900 dark:text-primary-100' : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white'); ?> transition duration-150 ease-in-out">
                                    <i class="fas fa-home mr-3 text-gray-400 dark:text-gray-300 group-hover:text-gray-500 dark:group-hover:text-gray-200"></i>
                                    Dashboard
                                </a>
                                <?php if(auth()->user()->hasPermission('manage_plugins')): ?>
                                    <a href="<?php echo e(url('/plugins')); ?>" class="group flex items-center px-2 py-2 text-sm font-medium rounded-md <?php echo e(request()->is('plugins*') ? 'bg-primary-100 dark:bg-primary-900 text-primary-900 dark:text-primary-100' : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white'); ?> transition duration-150 ease-in-out">
                                        <i class="fas fa-puzzle-piece mr-3 text-gray-400 dark:text-gray-300 group-hover:text-gray-500 dark:group-hover:text-gray-200"></i>
                                        Plugin Manager
                                    </a>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>

                    </nav>
                </div>



                <!-- User Info -->
                <div class="flex-shrink-0 border-t border-gray-200 dark:border-gray-700 p-4">
                    <!-- Controls Row -->
                    <div class="mb-3 flex items-center justify-between space-x-2">
                        <!-- Language Switcher -->
                        <?php
                            $showLanguageSwitcher = false;
                            $availableLanguages = collect();
                            $currentLanguage = null;

                            try {
                                $pluginManager = app(\App\Services\PluginManager::class);
                                if ($pluginManager->isPluginEnabled('localization')) {
                                    if (class_exists('\Plugins\Localization\Models\Language')) {
                                        $currentLanguageCode = session('locale', config('app.locale', 'en'));
                                        $currentLanguage = \Plugins\Localization\Models\Language::findByCode($currentLanguageCode);
                                        $availableLanguages = \Plugins\Localization\Models\Language::active()->ordered()->get();
                                        $showLanguageSwitcher = $availableLanguages->count() > 1;
                                    }
                                }
                            } catch (\Exception $e) {
                                // Localization not available yet
                            }
                        ?>

                        <?php if($showLanguageSwitcher): ?>
                            <div class="relative" id="language-switcher">
                                <button type="button"
                                        onclick="toggleLanguageDropdown()"
                                        class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-150 ease-in-out"
                                        title="Switch Language">
                                    <span class="text-lg mr-2"><?php echo e($currentLanguage ? $currentLanguage->getFlagDisplay() : '🌐'); ?></span>
                                    <span class="hidden sm:block"><?php echo e($currentLanguage ? $currentLanguage->code : 'EN'); ?></span>
                                    <i class="fas fa-chevron-down ml-1 text-xs"></i>
                                </button>

                                <div id="language-dropdown"
                                     class="hidden absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50">
                                    <div class="py-1" role="menu">
                                        <?php $__currentLoopData = $availableLanguages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <button type="button"
                                                    onclick="switchLanguage('<?php echo e($language->code); ?>')"
                                                    class="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition duration-150 <?php echo e($currentLanguage && $currentLanguage->code === $language->code ? 'bg-gray-50 dark:bg-gray-700' : ''); ?>"
                                                    role="menuitem">
                                                <span class="text-lg mr-3"><?php echo e($language->getFlagDisplay()); ?></span>
                                                <div class="flex-1 text-left">
                                                    <div class="font-medium"><?php echo e($language->name); ?></div>
                                                    <div class="text-xs text-gray-500 dark:text-gray-400"><?php echo e($language->native_name); ?></div>
                                                </div>
                                                <?php if($currentLanguage && $currentLanguage->code === $language->code): ?>
                                                    <i class="fas fa-check text-blue-600 dark:text-blue-400 ml-2"></i>
                                                <?php endif; ?>
                                            </button>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Dark Mode Toggle -->
                        <button type="button"
                                onclick="toggleDarkMode()"
                                class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-150 ease-in-out"
                                title="Toggle Dark Mode">
                            <i id="dark-mode-icon" class="fas fa-moon dark:fa-sun mr-2"></i>
                            <span id="dark-mode-text" class="hidden sm:block">Dark Mode</span>
                        </button>
                    </div>

                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div>
                                <i class="fas fa-user-circle text-2xl text-gray-400 dark:text-gray-300"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-gray-700 dark:text-gray-200"><?php echo e(Auth::user()->name); ?></p>
                                <p class="text-xs font-medium text-gray-500 dark:text-gray-400"><?php echo e(Auth::user()->role ? Auth::user()->role->display_name : 'User'); ?></p>
                            </div>
                        </div>
                        <form method="POST" action="<?php echo e(route('logout')); ?>">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="text-gray-400 dark:text-gray-300 hover:text-gray-600 dark:hover:text-gray-100 transition duration-150 ease-in-out" title="Logout">
                                <i class="fas fa-sign-out-alt"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile sidebar -->
        <div class="md:hidden">
            <div class="fixed inset-0 flex z-40 sidebar-transition" id="mobile-sidebar" style="display: none;">
                <div class="fixed inset-0 bg-gray-600 bg-opacity-75" aria-hidden="true"></div>
                <div class="relative flex-1 flex flex-col max-w-xs w-full bg-white">
                    <div class="absolute top-0 right-0 -mr-12 pt-2">
                        <button type="button" class="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white" id="close-sidebar">
                            <span class="sr-only">Close sidebar</span>
                            <i class="fas fa-times text-white"></i>
                        </button>
                    </div>
                    <div class="flex-1 h-0 pt-5 pb-4 overflow-y-auto">
                        <div class="flex-shrink-0 flex items-center px-4">
                            <a href="<?php echo e(url('/')); ?>" class="flex items-center">
                                <i class="fas fa-cube text-primary-600 text-2xl mr-3"></i>
                                <span class="text-xl font-bold text-gray-900"><?php echo e(config('app.name')); ?></span>
                            </a>
                        </div>
                        <nav class="mt-5 px-2 space-y-1">
                            <!-- Dynamic Navigation from NavigationMenu -->
                            <div class="space-y-1">
                                <?php if(isset($navigationTree) && count($navigationTree) > 0): ?>
                                    <?php $__currentLoopData = $navigationTree; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php echo $__env->make('partials.navigation-item-mobile', ['item' => $item], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php else: ?>
                                    <!-- Fallback navigation if no dynamic navigation is available -->
                                    <a href="<?php echo e(url('/')); ?>" class="group flex items-center px-2 py-2 text-base font-medium rounded-md <?php echo e(request()->is('/') ? 'bg-primary-100 text-primary-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'); ?>">
                                        <i class="fas fa-home mr-4 text-gray-400"></i>
                                        Dashboard
                                    </a>
                                    <?php if(auth()->user()->hasPermission('manage_plugins')): ?>
                                        <a href="<?php echo e(url('/plugins')); ?>" class="group flex items-center px-2 py-2 text-base font-medium rounded-md <?php echo e(request()->is('plugins*') ? 'bg-primary-100 text-primary-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'); ?>">
                                            <i class="fas fa-puzzle-piece mr-4 text-gray-400"></i>
                                            Plugin Manager
                                        </a>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>

                        </nav>
                    </div>
                    <div class="flex-shrink-0 border-t border-gray-200 p-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div>
                                    <i class="fas fa-user-circle text-2xl text-gray-400"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-700"><?php echo e(Auth::user()->name); ?></p>
                                    <p class="text-xs font-medium text-gray-500"><?php echo e(Auth::user()->role ? Auth::user()->role->display_name : 'User'); ?></p>
                                </div>
                            </div>
                            <form method="POST" action="<?php echo e(route('logout')); ?>">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="text-gray-400 hover:text-gray-600 transition duration-150 ease-in-out" title="Logout">
                                    <i class="fas fa-sign-out-alt"></i>
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main content -->
        <div class="flex flex-col flex-1 min-w-0 overflow-hidden">
            <!-- Top bar for mobile -->
            <div class="md:hidden">
                <div class="relative z-10 flex-shrink-0 flex h-16 bg-white shadow">
                    <button type="button" class="px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 md:hidden" id="open-sidebar">
                        <span class="sr-only">Open sidebar</span>
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="flex-1 px-4 flex justify-between">
                        <div class="flex-1 flex items-center">
                            <h1 class="text-lg font-semibold text-gray-900"><?php echo e(config('app.name')); ?></h1>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main content area -->
            <main class="flex-1 relative overflow-y-auto focus:outline-none">
                <div class="py-6">
                    <div class="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
                        <!-- Flash Messages -->
                        <?php if(session('success')): ?>
                            <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                                <span class="block sm:inline"><?php echo e(session('success')); ?></span>
                            </div>
                        <?php endif; ?>

                        <?php if(session('error')): ?>
                            <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                                <span class="block sm:inline"><?php echo e(session('error')); ?></span>
                            </div>
                        <?php endif; ?>

                        <?php if(session('warning')): ?>
                            <div class="mb-4 bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative" role="alert">
                                <span class="block sm:inline"><?php echo e(session('warning')); ?></span>
                            </div>
                        <?php endif; ?>

                        <!-- Page Content -->
                        <?php echo e($slot ?? ''); ?>

                        <?php echo $__env->yieldContent('content'); ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Scripts -->
    <?php
        $pluginManager = app(\App\Services\PluginManager::class);
        $announcementsEnabled = $pluginManager->isPluginEnabled('announcements');
    ?>
    <script>
        // Plugin status flags
        const announcementsEnabled = <?php echo e($announcementsEnabled ? 'true' : 'false'); ?>;
        // Mobile sidebar toggle
        document.addEventListener('DOMContentLoaded', function() {
            const openSidebarButton = document.getElementById('open-sidebar');
            const closeSidebarButton = document.getElementById('close-sidebar');
            const mobileSidebar = document.getElementById('mobile-sidebar');

            if (openSidebarButton && mobileSidebar) {
                openSidebarButton.addEventListener('click', function() {
                    mobileSidebar.style.display = 'flex';
                });
            }

            if (closeSidebarButton && mobileSidebar) {
                closeSidebarButton.addEventListener('click', function() {
                    mobileSidebar.style.display = 'none';
                });
            }

            // Close sidebar when clicking outside
            if (mobileSidebar) {
                mobileSidebar.addEventListener('click', function(e) {
                    if (e.target === mobileSidebar || e.target.getAttribute('aria-hidden') === 'true') {
                        mobileSidebar.style.display = 'none';
                    }
                });
            }
        });

        // Generic submenu toggle function for dynamic navigation
        function toggleSubmenu(menuName) {
            const submenu = document.getElementById(menuName + '-submenu');
            const icon = document.getElementById(menuName + '-menu-icon');

            if (submenu) {
                if (submenu.classList.contains('hidden')) {
                    submenu.classList.remove('hidden');
                    if (icon) icon.classList.add('transform', 'rotate-90');
                } else {
                    submenu.classList.add('hidden');
                    if (icon) icon.classList.remove('transform', 'rotate-90');
                }
            }
        }

        // Mobile submenu toggle function for dynamic navigation
        function toggleSubmenuMobile(menuName) {
            const submenu = document.getElementById(menuName + '-submenu-mobile');
            const icon = document.getElementById(menuName + '-menu-icon-mobile');

            if (submenu) {
                if (submenu.classList.contains('hidden')) {
                    submenu.classList.remove('hidden');
                    if (icon) icon.classList.add('transform', 'rotate-90');
                } else {
                    submenu.classList.add('hidden');
                    if (icon) icon.classList.remove('transform', 'rotate-90');
                }
            }
        }

        // Business menu toggle
        function toggleBusinessMenu() {
            // Handle desktop submenu
            const submenu = document.getElementById('business-submenu');
            const iconDesktop = document.getElementById('business-menu-icon-desktop');

            // Handle mobile submenu
            const submenuMobile = document.getElementById('business-submenu-mobile');
            const iconMobile = document.getElementById('business-menu-icon-mobile');

            // Toggle desktop submenu
            if (submenu) {
                if (submenu.classList.contains('hidden')) {
                    submenu.classList.remove('hidden');
                    if (iconDesktop) iconDesktop.classList.add('transform', 'rotate-90');
                } else {
                    submenu.classList.add('hidden');
                    if (iconDesktop) iconDesktop.classList.remove('transform', 'rotate-90');
                }
            }

            // Toggle mobile submenu
            if (submenuMobile) {
                if (submenuMobile.classList.contains('hidden')) {
                    submenuMobile.classList.remove('hidden');
                    if (iconMobile) iconMobile.classList.add('transform', 'rotate-90');
                } else {
                    submenuMobile.classList.add('hidden');
                    if (iconMobile) iconMobile.classList.remove('transform', 'rotate-90');
                }
            }
        }

        // Users menu toggle
        function toggleUsersMenu() {
            // Handle desktop submenu
            const submenu = document.getElementById('users-submenu');
            const iconDesktop = document.getElementById('users-menu-icon-desktop');

            // Handle mobile submenu
            const submenuMobile = document.getElementById('users-submenu-mobile');
            const iconMobile = document.getElementById('users-menu-icon-mobile');

            // Toggle desktop submenu
            if (submenu) {
                if (submenu.classList.contains('hidden')) {
                    submenu.classList.remove('hidden');
                    if (iconDesktop) iconDesktop.classList.add('transform', 'rotate-90');
                } else {
                    submenu.classList.add('hidden');
                    if (iconDesktop) iconDesktop.classList.remove('transform', 'rotate-90');
                }
            }

            // Toggle mobile submenu
            if (submenuMobile) {
                if (submenuMobile.classList.contains('hidden')) {
                    submenuMobile.classList.remove('hidden');
                    if (iconMobile) iconMobile.classList.add('transform', 'rotate-90');
                } else {
                    submenuMobile.classList.add('hidden');
                    if (iconMobile) iconMobile.classList.remove('transform', 'rotate-90');
                }
            }
        }

        // Announcements menu toggle
        function toggleAnnouncementsMenu() {
            // Only function if announcements plugin is enabled
            if (!announcementsEnabled) {
                return;
            }

            // Handle desktop submenu
            const submenu = document.getElementById('announcements-submenu');
            const iconDesktop = document.getElementById('announcements-menu-icon-desktop');

            // Handle mobile submenu
            const submenuMobile = document.getElementById('announcements-submenu-mobile');
            const iconMobile = document.getElementById('announcements-menu-icon-mobile');

            // Toggle desktop submenu
            if (submenu) {
                if (submenu.classList.contains('hidden')) {
                    submenu.classList.remove('hidden');
                    if (iconDesktop) iconDesktop.classList.add('transform', 'rotate-90');
                } else {
                    submenu.classList.add('hidden');
                    if (iconDesktop) iconDesktop.classList.remove('transform', 'rotate-90');
                }
            }

            // Toggle mobile submenu
            if (submenuMobile) {
                if (submenuMobile.classList.contains('hidden')) {
                    submenuMobile.classList.remove('hidden');
                    if (iconMobile) iconMobile.classList.add('transform', 'rotate-90');
                } else {
                    submenuMobile.classList.add('hidden');
                    if (iconMobile) iconMobile.classList.remove('transform', 'rotate-90');
                }
            }
        }

        // Products menu toggle
        function toggleProductsMenu() {
            // Handle desktop submenu
            const submenu = document.getElementById('products-submenu');
            const iconDesktop = document.getElementById('products-menu-icon-desktop');

            // Handle mobile submenu
            const submenuMobile = document.getElementById('products-submenu-mobile');
            const iconMobile = document.getElementById('products-menu-icon-mobile');

            if (submenu) {
                if (submenu.classList.contains('hidden')) {
                    submenu.classList.remove('hidden');
                    if (iconDesktop) iconDesktop.classList.add('transform', 'rotate-90');
                } else {
                    submenu.classList.add('hidden');
                    if (iconDesktop) iconDesktop.classList.remove('transform', 'rotate-90');
                }
            }

            if (submenuMobile) {
                if (submenuMobile.classList.contains('hidden')) {
                    submenuMobile.classList.remove('hidden');
                    if (iconMobile) iconMobile.classList.add('transform', 'rotate-90');
                } else {
                    submenuMobile.classList.add('hidden');
                    if (iconMobile) iconMobile.classList.remove('transform', 'rotate-90');
                }
            }
        }

        // Localization menu toggle
        function toggleLocalizationMenu() {
            // Handle desktop submenu
            const submenu = document.getElementById('localization-menu-desktop');
            const iconDesktop = document.getElementById('localization-menu-icon-desktop');

            // Handle mobile submenu
            const submenuMobile = document.getElementById('localization-submenu-mobile');
            const iconMobile = document.getElementById('localization-menu-icon-mobile');

            // Toggle desktop submenu
            if (submenu) {
                if (submenu.classList.contains('hidden')) {
                    submenu.classList.remove('hidden');
                    if (iconDesktop) iconDesktop.classList.add('transform', 'rotate-90');
                } else {
                    submenu.classList.add('hidden');
                    if (iconDesktop) iconDesktop.classList.remove('transform', 'rotate-90');
                }
            }

            // Toggle mobile submenu
            if (submenuMobile) {
                if (submenuMobile.classList.contains('hidden')) {
                    submenuMobile.classList.remove('hidden');
                    if (iconMobile) iconMobile.classList.add('transform', 'rotate-90');
                } else {
                    submenuMobile.classList.add('hidden');
                    if (iconMobile) iconMobile.classList.remove('transform', 'rotate-90');
                }
            }
        }

        // Dark Mode Toggle Functionality
        function toggleDarkMode() {
            // Get current state
            const htmlElement = document.documentElement;
            const currentIsDark = htmlElement.classList.contains('dark');
            const newIsDark = !currentIsDark;

            // Immediately update UI for better user experience
            if (newIsDark) {
                htmlElement.classList.add('dark');
            } else {
                htmlElement.classList.remove('dark');
            }
            updateDarkModeButton(newIsDark);

            // Store in localStorage as backup
            localStorage.setItem('darkMode', newIsDark ? 'dark' : 'light');

            // Try to save to server
            fetch('<?php echo e(route('preferences.toggle-dark-mode')); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('Dark mode preference saved:', data.message);
                } else {
                    console.warn('Failed to save dark mode preference:', data.message);
                    // UI is already updated, so we don't need to revert
                }
            })
            .catch(error => {
                console.warn('Error saving dark mode preference:', error);
                // UI is already updated, preference is stored in localStorage
            });
        }

        function updateDarkModeButton(isDarkMode) {
            const icon = document.getElementById('dark-mode-icon');
            const text = document.getElementById('dark-mode-text');

            if (icon) {
                if (isDarkMode) {
                    icon.className = 'fas fa-sun mr-2';
                } else {
                    icon.className = 'fas fa-moon mr-2';
                }
            }

            if (text) {
                text.textContent = isDarkMode ? 'Light Mode' : 'Dark Mode';
            }
        }

        // Initialize dark mode button on page load
        document.addEventListener('DOMContentLoaded', function() {
            const htmlElement = document.documentElement;

            // Check if dark mode should be enabled from various sources
            let isDarkMode = htmlElement.classList.contains('dark');

            // If no server-side preference, check localStorage
            if (!isDarkMode) {
                const storedTheme = localStorage.getItem('darkMode');
                if (storedTheme === 'dark') {
                    htmlElement.classList.add('dark');
                    isDarkMode = true;
                }
            }

            updateDarkModeButton(isDarkMode);
        });

        // Language switcher functionality
        function toggleLanguageDropdown() {
            const dropdown = document.getElementById('language-dropdown');
            if (dropdown) {
                dropdown.classList.toggle('hidden');
            }
        }

        function switchLanguage(languageCode) {
            // Show loading state
            const button = document.querySelector('#language-switcher button');
            if (button) {
                const originalContent = button.innerHTML;
                button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Switching...';
                button.disabled = true;

                fetch('/localization/lang/switch', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        language: languageCode
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Reload the page to apply the new language
                        window.location.reload();
                    } else {
                        console.error('Failed to switch language:', data.message);
                        alert('Failed to switch language. Please try again.');

                        // Restore button state
                        button.innerHTML = originalContent;
                        button.disabled = false;
                    }
                })
                .catch(error => {
                    console.error('Error switching language:', error);
                    alert('An error occurred while switching language. Please try again.');

                    // Restore button state
                    button.innerHTML = originalContent;
                    button.disabled = false;
                });

                // Hide dropdown
                const dropdown = document.getElementById('language-dropdown');
                if (dropdown) {
                    dropdown.classList.add('hidden');
                }
            }
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const switcher = document.getElementById('language-switcher');
            const dropdown = document.getElementById('language-dropdown');

            if (switcher && dropdown && !switcher.contains(event.target)) {
                dropdown.classList.add('hidden');
            }
        });

        // Close dropdown on escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const dropdown = document.getElementById('language-dropdown');
                if (dropdown) {
                    dropdown.classList.add('hidden');
                }
            }
        });
    </script>

    <!-- Include Announcements Integration -->
    <?php if($announcementsEnabled && auth()->user() && auth()->user()->hasPermission('view_announcements')): ?>
        <?php echo $__env->make('plugins.announcements::components.announcement-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        <script>
        // Enhanced announcement checking with navigation integration
        let unreadAnnouncements = [];
        let lastCheckTime = 0;
        const CHECK_INTERVAL = 300000; // 5 minutes

        // Check for unread announcements on page load
        document.addEventListener('DOMContentLoaded', function() {
            checkUnreadAnnouncements();

            // Check every 5 minutes for new announcements
            setInterval(checkUnreadAnnouncements, CHECK_INTERVAL);
        });

        function checkUnreadAnnouncements() {
            const now = Date.now();

            // Prevent too frequent API calls
            if (now - lastCheckTime < 30000) { // 30 seconds minimum between checks
                return;
            }

            lastCheckTime = now;

            fetch('/announcements/api/unread', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                unreadAnnouncements = data.announcements || [];
                updateAllUnreadCounts();

                // Show modal if there are unread announcements requiring acknowledgment
                const requiresAcknowledgment = unreadAnnouncements.some(a => a.requires_acknowledgment);
                if (unreadAnnouncements.length > 0 && requiresAcknowledgment && !isAnnouncementModalOpen()) {
                    showAnnouncementModal();
                }
            })
            .catch(error => {
                console.error('Error checking unread announcements:', error);
            });
        }

        function updateAllUnreadCounts() {
            const count = unreadAnnouncements.length;

            // Update navigation counts
            updateNavigationCounts(count);

            // Update modal count
            const modalCountElement = document.getElementById('announcement-count');
            if (modalCountElement) {
                modalCountElement.textContent = count;
            }
        }

        function updateNavigationCounts(count) {
            const navCounts = [
                'unread-announcement-count-nav',
                'unread-announcement-count-nav-mobile'
            ];

            navCounts.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = count;
                    element.style.display = count > 0 ? 'inline-flex' : 'none';
                }
            });
        }

        function isAnnouncementModalOpen() {
            const modal = document.getElementById('announcement-modal');
            return modal && !modal.classList.contains('hidden');
        }

        // Listen for page visibility changes to check for new announcements
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                // Page became visible, check for new announcements
                setTimeout(checkUnreadAnnouncements, 1000);
            }
        });

        // Expose functions globally for use in other components
        window.checkUnreadAnnouncements = checkUnreadAnnouncements;
        window.updateAllUnreadCounts = updateAllUnreadCounts;
        </script>
    <?php endif; ?>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH /Users/<USER>/Herd/business/resources/views/layouts/app.blade.php ENDPATH**/ ?>