<?php $__env->startSection('head'); ?>
<style>
.sortable-ghost {
    opacity: 0.4;
    background: #f3f4f6;
    border: 2px dashed #d1d5db;
}

.sortable-chosen {
    background: #eff6ff;
    border: 2px solid #3b82f6;
}

.sortable-drag {
    background: #ffffff;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transform: rotate(5deg);
}

.nested-sortable {
    min-height: 20px;
    transition: all 0.2s ease;
}

.nested-sortable:hover {
    background-color: #f9fafb;
    border-radius: 0.375rem;
}

.nested-sortable.hidden {
    display: none;
}

.menu-item:hover .nested-sortable.hidden {
    display: block;
}

.drop-zone-active {
    background-color: #dbeafe;
    border-color: #3b82f6;
}
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Navigation Management</h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Customize the sidebar navigation structure</p>
        </div>
        <div class="flex space-x-3">
            <a href="<?php echo e(route('dashboard')); ?>" class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 text-white font-medium rounded-md transition duration-150 ease-in-out">
                <i class="fas fa-arrow-left mr-2"></i>
                Back
            </a>
            <button onclick="showCreateModal()" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 dark:hover:bg-blue-600 dark:bg-blue-700 dark:hover:bg-blue-600 text-white font-medium rounded-md transition duration-150 ease-in-out">
                <i class="fas fa-plus mr-2"></i>
                Add Menu Item
            </button>
            <button onclick="resetNavigation()" class="inline-flex items-center px-4 py-2 bg-yellow-600 hover:bg-yellow-700 dark:bg-yellow-700 dark:hover:bg-yellow-600 text-white font-medium rounded-md transition duration-150 ease-in-out">
                <i class="fas fa-undo mr-2"></i>
                Reset to Default
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-2 md:grid-cols-6 gap-4 mb-6">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
            <div class="p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-list text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div class="ml-3 w-0 flex-1">
                        <dl>
                            <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 truncate">Total Items</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($stats['total']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
            <div class="p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-green-600 dark:text-green-400"></i>
                    </div>
                    <div class="ml-3 w-0 flex-1">
                        <dl>
                            <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 truncate">Active</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($stats['active']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
            <div class="p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-shield-alt text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div class="ml-3 w-0 flex-1">
                        <dl>
                            <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 truncate">System</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($stats['system']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
            <div class="p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-puzzle-piece text-purple-600 dark:text-purple-400"></i>
                    </div>
                    <div class="ml-3 w-0 flex-1">
                        <dl>
                            <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 truncate">Plugin</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($stats['plugin_based']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
            <div class="p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-minus text-gray-600 dark:text-gray-400"></i>
                    </div>
                    <div class="ml-3 w-0 flex-1">
                        <dl>
                            <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 truncate">Separators</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($stats['separators']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
            <div class="p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-external-link-alt text-green-600 dark:text-green-400"></i>
                    </div>
                    <div class="ml-3 w-0 flex-1">
                        <dl>
                            <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 truncate">External</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($stats['external_links']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- Color Legend -->
    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg mb-6 transition duration-150 ease-in-out">
        <div class="px-4 py-3 sm:px-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-sm leading-6 font-medium text-gray-900 dark:text-white">Menu Item Types</h3>
        </div>
        <div class="px-4 py-3 sm:px-6">
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
                <div class="flex items-center space-x-2">
                    <div class="w-4 h-4 bg-blue-100 dark:bg-blue-900 border border-blue-200 dark:border-blue-800 rounded transition duration-150 ease-in-out"></div>
                    <span class="text-xs text-gray-600 dark:text-gray-400">System</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-4 h-4 bg-purple-100 dark:bg-purple-900 border border-purple-200 dark:border-purple-800 rounded transition duration-150 ease-in-out"></div>
                    <span class="text-xs text-gray-600 dark:text-gray-400">Plugin</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-4 h-4 bg-indigo-100 dark:bg-indigo-900 border border-indigo-200 dark:border-indigo-800 rounded transition duration-150 ease-in-out"></div>
                    <span class="text-xs text-gray-600 dark:text-gray-400">Internal Link</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-4 h-4 bg-green-100 dark:bg-green-900 border border-green-200 dark:border-green-800 rounded transition duration-150 ease-in-out"></div>
                    <span class="text-xs text-gray-600 dark:text-gray-400">External Link</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-4 h-4 bg-orange-100 dark:bg-orange-900 border border-orange-200 dark:border-orange-800 rounded transition duration-150 ease-in-out"></div>
                    <span class="text-xs text-gray-600 dark:text-gray-400">Parent Menu</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-4 h-4 bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded transition duration-150 ease-in-out"></div>
                    <span class="text-xs text-gray-600 dark:text-gray-400">Separator</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation Tree -->
    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
        <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Current Navigation Structure</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">Drag and drop to reorder menu items. Each item is color-coded by type. Click "Add Sub-Item" to add children.</p>
        </div>
        <div class="border-t border-gray-200 dark:border-gray-700">
            <div class="px-4 py-5 sm:px-6">
                <div id="navigation-tree" class="space-y-3">
                    <!-- Navigation tree will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create/Edit Modal -->
<div id="menuModal" class="fixed inset-0 bg-gray-600 dark:bg-gray-900 bg-opacity-50 dark:bg-opacity-75 overflow-y-auto h-full w-full hidden z-50 transition duration-150 ease-in-out">
    <div class="relative top-10 mx-auto p-6 border border-gray-200 dark:border-gray-700 max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800 transition duration-150 ease-in-out">
        <div class="mb-4">
            <h3 id="modalTitle" class="text-xl leading-6 font-medium text-gray-900 dark:text-white">Add Menu Item</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Create or edit a navigation menu item</p>
        </div>

        <form id="menuForm" class="space-y-6">
            <input type="hidden" id="menuId" name="id">

            <!-- Basic Information Section -->
            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg transition duration-150 ease-in-out">
                <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">Basic Information</h4>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="menuLabel" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Display Name *</label>
                        <input type="text" id="menuLabel" name="label" required
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white"
                               placeholder="e.g., Dashboard, Settings">
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Text shown in the navigation menu</p>
                    </div>

                    <div>
                        <label for="menuName" class="block text-sm font-medium text-gray-700 dark:text-gray-300">System Name *</label>
                        <input type="text" id="menuName" name="name" required
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white"
                               placeholder="e.g., dashboard, settings">
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Unique identifier (auto-generated from display name)</p>
                    </div>
                </div>
            </div>

            <!-- Menu Type Section -->
            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg transition duration-150 ease-in-out">
                <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">Menu Type & Behavior</h4>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="menuType" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Item Type *</label>
                        <select id="menuType" name="type" required onchange="toggleModalFormFields()"
                                class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                            <option value="link">Menu Link</option>
                            <option value="separator">Separator Line</option>
                        </select>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Choose between a clickable link or visual separator</p>
                    </div>

                    <div id="modalBehaviorField">
                        <label for="menuBehavior" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Link Behavior *</label>
                        <select id="menuBehavior" name="behavior" onchange="toggleModalBehaviorFields()"
                                class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                            <option value="internal">Internal Link</option>
                            <option value="external">External Link</option>
                            <option value="parent">Parent (with sub-items)</option>
                        </select>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">How this menu item should behave when clicked</p>
                    </div>
                </div>
            </div>

            <!-- Appearance Section -->
            <div id="modalAppearanceSection" class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg transition duration-150 ease-in-out">
                <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">Appearance & Navigation</h4>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div id="modalIconField">
                        <label for="menuIcon" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Icon</label>
                        <div class="mt-1 flex">
                            <input type="text" id="menuIcon" name="icon"
                                   class="block w-full border-gray-300 dark:border-gray-600 rounded-l-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white"
                                   placeholder="fas fa-home">
                            <button type="button" onclick="showIconSelector('menuIcon')"
                                    class="inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 dark:border-gray-600 rounded-r-md bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-600 transition duration-150 ease-in-out">
                                <i class="fas fa-icons"></i>
                            </button>
                        </div>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Choose an icon for this menu item</p>
                    </div>

                    <div>
                        <label for="menuParent" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Parent Menu</label>
                        <select id="menuParent" name="parent_id"
                                class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                            <option value="">Top Level</option>
                        </select>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Place this item under another menu item</p>
                    </div>
                </div>

                <div id="modalUrlField" class="mt-4">
                    <label for="menuUrl" class="block text-sm font-medium text-gray-700 dark:text-gray-300">URL</label>
                    <input type="text" id="menuUrl" name="url"
                           class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white"
                           placeholder="/custom-page">
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">The URL this menu item should navigate to</p>
                </div>

                <div id="modalRouteField" class="mt-4">
                    <label for="menuRoute" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Laravel Route (Optional)</label>

                    <!-- Route Selection Method -->
                    <div class="mt-2 mb-3">
                        <div class="flex items-center space-x-4">
                            <label class="inline-flex items-center">
                                <input type="radio" name="routeMethod" value="select" class="form-radio text-primary-600" checked>
                                <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Select from plugin routes</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="radio" name="routeMethod" value="manual" class="form-radio text-primary-600">
                                <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Enter manually</span>
                            </label>
                        </div>
                    </div>

                    <!-- Plugin and Route Selection -->
                    <div id="routeSelectContainer">
                        <!-- Plugin Selection -->
                        <div class="mb-3">
                            <label for="routePluginSelect" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Select Plugin</label>
                            <select id="routePluginSelect" name="route_plugin"
                                    class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                                <option value="">-- Select a plugin --</option>
                                <option value="core">Core System</option>
                            </select>
                        </div>

                        <!-- Route Selection -->
                        <div>
                            <label for="menuRouteSelect" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Select Route</label>
                            <select id="menuRouteSelect" name="route_select"
                                    class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white"
                                    disabled>
                                <option value="">-- Select a plugin first --</option>
                            </select>
                        </div>
                    </div>

                    <!-- Manual Route Input -->
                    <div id="routeManualContainer" style="display: none;">
                        <input type="text" id="menuRoute" name="route"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white"
                               placeholder="dashboard">
                    </div>

                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Use Laravel route name instead of URL (takes priority over URL)</p>
                </div>
            </div>

            <!-- Advanced Settings Section -->
            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg transition duration-150 ease-in-out">
                <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">Advanced Settings</h4>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="menuPlugin" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Plugin</label>
                        <select id="menuPlugin" name="plugin"
                                class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                            <option value="">No Plugin</option>
                            <?php $__currentLoopData = $availablePlugins; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pluginName => $plugin): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($pluginName); ?>"><?php echo e($plugin->config['display_name'] ?? ucfirst($pluginName)); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Associate with a plugin (optional)</p>
                    </div>

                    <div>
                        <label for="menuTarget" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Link Target</label>
                        <select id="menuTarget" name="target"
                                class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                            <option value="_self">Same Window</option>
                            <option value="_blank">New Window</option>
                            <option value="_parent">Parent Frame</option>
                            <option value="_top">Top Frame</option>
                        </select>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Where to open the link</p>
                    </div>
                </div>

                <div class="mt-4">
                    <label for="menuPermissions" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Required Permissions</label>
                    <div class="mt-1 relative">
                        <button type="button" id="permissionDropdownBtn" onclick="togglePermissionDropdown()"
                                class="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-2 text-left cursor-pointer focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                            <span id="permissionDropdownText" class="block truncate text-gray-500 dark:text-gray-400">Select permissions...</span>
                            <span class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                                <i class="fas fa-chevron-down text-gray-400 dark:text-gray-500"></i>
                            </span>
                        </button>

                        <div id="permissionDropdown" class="hidden absolute z-10 mt-1 w-full bg-white dark:bg-gray-800 shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm">
                            <div class="px-3 py-2 border-b border-gray-200 dark:border-gray-700">
                                <input type="text" id="permissionSearch" placeholder="Search permissions..."
                                       class="w-full border-gray-300 dark:border-gray-600 rounded-md text-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                                       onkeyup="filterPermissions()">
                            </div>
                            <?php $__currentLoopData = $availablePermissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <label class="permission-option flex items-center px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer transition duration-150 ease-in-out" data-permission="<?php echo e($permission->name); ?>">
                                    <input type="checkbox" value="<?php echo e($permission->name); ?>"
                                           class="permission-checkbox rounded border-gray-300 dark:border-gray-600 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50"
                                           onchange="updatePermissionSelection()">
                                    <div class="ml-3">
                                        <div class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e($permission->display_name); ?></div>
                                        <?php if($permission->description): ?>
                                            <div class="text-xs text-gray-500 dark:text-gray-400"><?php echo e($permission->description); ?></div>
                                        <?php endif; ?>
                                        <?php if($permission->plugin): ?>
                                            <div class="text-xs text-blue-600 dark:text-blue-400">Plugin: <?php echo e($permission->plugin); ?></div>
                                        <?php endif; ?>
                                    </div>
                                </label>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                    <input type="hidden" id="menuPermissions" name="permissions">
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Select permissions needed to see this menu item</p>
                </div>

                <div class="mt-4 flex items-center space-x-6">
                    <label class="flex items-center">
                        <input type="checkbox" id="menuActive" name="is_active" checked
                               class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:text-white">
                        <span class="ml-2 text-sm text-gray-900 dark:text-white">Active</span>
                        <span class="ml-1 text-xs text-gray-500 dark:text-gray-400">(functional)</span>
                    </label>

                    <label class="flex items-center">
                        <input type="checkbox" id="menuVisible" name="visible" checked
                               class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:text-white">
                        <span class="ml-2 text-sm text-gray-900 dark:text-white">Visible</span>
                        <span class="ml-1 text-xs text-gray-500 dark:text-gray-400">(shown in menu)</span>
                    </label>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-600">
                <button type="button" onclick="hideMenuModal()"
                        class="px-6 py-2 bg-gray-500 dark:bg-gray-600 text-white font-medium rounded-md hover:bg-gray-600 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-300 transition duration-150 ease-in-out">
                    Cancel
                </button>
                <button type="submit"
                        class="px-6 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-300 transition duration-150 ease-in-out">
                    <i class="fas fa-save mr-2"></i>
                    Save Menu Item
                </button>
            </div>
            </form>
        </div>
    </div>
</div>

<!-- Icon Selector Modal -->
<div id="iconSelectorModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50 transition duration-150 ease-in-out">
    <div class="relative top-20 mx-auto p-5 border w-4/5 max-w-4xl shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Select an Icon</h3>
                <button onclick="hideIconSelector()" class="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Search -->
            <div class="mb-4">
                <input type="text" id="iconSearch" placeholder="Search icons..."
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:bg-gray-700 dark:text-white"
                       onkeyup="filterIcons()">
            </div>

            <!-- Icon Categories -->
            <div class="mb-4">
                <div class="flex flex-wrap gap-2">
                    <button onclick="filterByCategory('all')" class="icon-category-btn active px-3 py-1 text-sm bg-blue-600 dark:bg-blue-700 text-white rounded transition duration-150 ease-in-out">All</button>
                    <button onclick="filterByCategory('solid')" class="icon-category-btn px-3 py-1 text-sm bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-500 transition duration-150 ease-in-out">Solid</button>
                    <button onclick="filterByCategory('regular')" class="icon-category-btn px-3 py-1 text-sm bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-500 transition duration-150 ease-in-out">Regular</button>
                    <button onclick="filterByCategory('light')" class="icon-category-btn px-3 py-1 text-sm bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-500 transition duration-150 ease-in-out">Light</button>
                    <button onclick="filterByCategory('thin')" class="icon-category-btn px-3 py-1 text-sm bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-500 transition duration-150 ease-in-out">Thin</button>
                    <button onclick="filterByCategory('duotone')" class="icon-category-btn px-3 py-1 text-sm bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-500 transition duration-150 ease-in-out">Duotone</button>
                    <button onclick="filterByCategory('brands')" class="icon-category-btn px-3 py-1 text-sm bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-500 transition duration-150 ease-in-out">Brands</button>
                    <button onclick="filterByCategory('social')" class="icon-category-btn px-3 py-1 text-sm bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-500 transition duration-150 ease-in-out">Social</button>
                    <button onclick="filterByCategory('business')" class="icon-category-btn px-3 py-1 text-sm bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-500 transition duration-150 ease-in-out">Business</button>
                    <button onclick="filterByCategory('ecommerce')" class="icon-category-btn px-3 py-1 text-sm bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-500 transition duration-150 ease-in-out">E-commerce</button>
                    <button onclick="filterByCategory('technology')" class="icon-category-btn px-3 py-1 text-sm bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-500 transition duration-150 ease-in-out">Technology</button>
                    <button onclick="filterByCategory('education')" class="icon-category-btn px-3 py-1 text-sm bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-500 transition duration-150 ease-in-out">Education</button>
                    <button onclick="filterByCategory('security')" class="icon-category-btn px-3 py-1 text-sm bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-500 transition duration-150 ease-in-out">Security</button>
                    <button onclick="filterByCategory('analytics')" class="icon-category-btn px-3 py-1 text-sm bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-500 transition duration-150 ease-in-out">Analytics</button>
                    <button onclick="filterByCategory('content')" class="icon-category-btn px-3 py-1 text-sm bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-500 transition duration-150 ease-in-out">Content</button>
                    <button onclick="filterByCategory('products')" class="icon-category-btn px-3 py-1 text-sm bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-500 transition duration-150 ease-in-out">Products</button>
                </div>
            </div>

            <!-- Icons Grid -->
            <div id="iconsGrid" class="grid grid-cols-8 md:grid-cols-12 lg:grid-cols-16 gap-2 max-h-96 overflow-y-auto">
                <!-- Icons will be populated by JavaScript -->
            </div>

            <div class="mt-4 flex justify-end">
                <button onclick="hideIconSelector()" class="px-4 py-2 bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 font-medium rounded-md transition duration-150 ease-in-out">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
<script>
// Pass enabled plugins data to JavaScript
window.enabledPlugins = <?php echo json_encode(array_keys($availablePlugins ?? [])); ?>;
</script>
<script>
let navigationData = [];
let isEditing = false;
let editingId = null;
let currentTargetInput = null;
let isAutoGeneratingName = true;

document.addEventListener('DOMContentLoaded', function() {
    loadNavigationTree();

    // Auto-generate system name from display name
    const labelInput = document.getElementById('menuLabel');
    const nameInput = document.getElementById('menuName');

    if (labelInput && nameInput) {
        labelInput.addEventListener('input', function() {
            if (isAutoGeneratingName) {
                const systemName = this.value.toLowerCase()
                    .replace(/[^a-z0-9\s]/g, '')
                    .replace(/\s+/g, '_')
                    .replace(/_{2,}/g, '_')
                    .replace(/^_|_$/g, '');
                nameInput.value = systemName;
            }
        });

        nameInput.addEventListener('input', function() {
            // If user manually edits the name, stop auto-generation
            isAutoGeneratingName = false;
        });
    }

    // Handle route selection method toggle
    const routeMethodRadios = document.querySelectorAll('input[name="routeMethod"]');
    const routeSelectContainer = document.getElementById('routeSelectContainer');
    const routeManualContainer = document.getElementById('routeManualContainer');

    routeMethodRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'select') {
                routeSelectContainer.style.display = 'block';
                routeManualContainer.style.display = 'none';
            } else {
                routeSelectContainer.style.display = 'none';
                routeManualContainer.style.display = 'block';
            }
        });
    });

    // Handle plugin selection for routes
    const routePluginSelect = document.getElementById('routePluginSelect');
    const routeSelect = document.getElementById('menuRouteSelect');
    const routeInput = document.getElementById('menuRoute');

    if (routePluginSelect && routeSelect) {
        routePluginSelect.addEventListener('change', function() {
            loadRoutesForPlugin(this.value);
        });
    }

    // Handle route selection from dropdown
    if (routeSelect && routeInput) {
        routeSelect.addEventListener('change', function() {
            if (this.value) {
                routeInput.value = this.value;
            }
        });
    }

    // Load available plugins and routes
    loadAvailablePlugins();
});

function loadNavigationTree() {
    fetch('/navigation/tree')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                navigationData = data.tree;
                renderNavigationTree();
                initializeSortable();
            } else {
                showNotification('Failed to load navigation tree', 'error');
            }
        })
        .catch(error => {
            console.error('Error loading navigation tree:', error);
            showNotification('Error loading navigation tree', 'error');
        });
}

function loadAvailablePlugins() {
    // Get enabled plugins from the global variable
    const enabledPlugins = window.enabledPlugins || [];
    const routePluginSelect = document.getElementById('routePluginSelect');

    if (!routePluginSelect) return;

    // Clear existing options except the first one
    while (routePluginSelect.children.length > 1) {
        routePluginSelect.removeChild(routePluginSelect.lastChild);
    }

    // Add core system option
    const coreOption = document.createElement('option');
    coreOption.value = 'core';
    coreOption.textContent = 'Core System';
    routePluginSelect.appendChild(coreOption);

    // Add enabled plugins
    enabledPlugins.forEach(pluginName => {
        const option = document.createElement('option');
        option.value = pluginName;
        option.textContent = getPluginDisplayName(pluginName);
        routePluginSelect.appendChild(option);
    });
}

function getPluginDisplayName(pluginName) {
    const displayNames = {
        'users': 'User Management',
        'business': 'Business Management',
        'navigation': 'Navigation',
        'announcements': 'Announcements',
        'settings': 'Settings',
        'products': 'Product Management',
        'todo': 'Todo Management'
    };
    return displayNames[pluginName] || pluginName.charAt(0).toUpperCase() + pluginName.slice(1);
}

function loadRoutesForPlugin(pluginName) {
    const routeSelect = document.getElementById('menuRouteSelect');
    if (!routeSelect) return;

    // Clear existing routes
    routeSelect.innerHTML = '<option value="">-- Select a route --</option>';

    if (!pluginName) {
        routeSelect.disabled = true;
        return;
    }

    routeSelect.disabled = false;

    // Define routes for each plugin
    const pluginRoutes = {
        'core': [
            { value: 'dashboard', label: 'Dashboard - Main dashboard page' },
            { value: 'plugins.index', label: 'Plugin Manager - Plugin management interface' }
        ],
        'users': [
            { value: 'users.index', label: 'All Users - User listing page' },
            { value: 'users.create', label: 'Create User - Add new user form' },
            { value: 'users.show', label: 'View User - User details page' },
            { value: 'users.edit', label: 'Edit User - User edit form' },
            { value: 'roles.index', label: 'All Roles - Role management page' },
            { value: 'roles.create', label: 'Create Role - Add new role form' },
            { value: 'roles.show', label: 'View Role - Role details page' },
            { value: 'roles.edit', label: 'Edit Role - Role edit form' },
            { value: 'permissions.index', label: 'All Permissions - Permission management page' }
        ],
        'business': [
            { value: 'business.index', label: 'All Businesses - Business listing page' },
            { value: 'business.create', label: 'Create Business - Add new business form' },
            { value: 'business.show', label: 'View Business - Business details page' },
            { value: 'business.edit', label: 'Edit Business - Business edit form' },
            { value: 'business.contacts.index', label: 'All Contacts - Contact management page' },
            { value: 'business.contacts.create', label: 'Create Contact - Add new contact form' },
            { value: 'business.contacts.show', label: 'View Contact - Contact details page' },
            { value: 'business.contacts.edit', label: 'Edit Contact - Contact edit form' }
        ],
        'navigation': [
            { value: 'navigation.index', label: 'Navigation Manager - Navigation management interface' }
        ],
        'announcements': [
            { value: 'announcements.index', label: 'All Announcements - Announcement listing page' },
            { value: 'announcements.create', label: 'Create Announcement - Add new announcement form' },
            { value: 'announcements.show', label: 'View Announcement - Announcement details page' },
            { value: 'announcements.edit', label: 'Edit Announcement - Announcement edit form' }
        ],
        'settings': [
            { value: 'settings.index', label: 'System Settings - Main settings page' },
            { value: 'settings.general', label: 'General Settings - General configuration' },
            { value: 'settings.security', label: 'Security Settings - Security configuration' },
            { value: 'settings.backup', label: 'Backup Settings - Backup configuration' }
        ],
        'products': [
            { value: 'products.index', label: 'All Products - Product listing page' },
            { value: 'products.create', label: 'Create Product - Add new product form' },
            { value: 'products.show', label: 'View Product - Product details page' },
            { value: 'products.edit', label: 'Edit Product - Product edit form' },
            { value: 'all-products.index', label: 'All Products (Global) - Global product listing' },
            { value: 'products.api', label: 'Products API - Product API endpoint' }
        ],
        'todo': [
            { value: 'todo.index', label: 'Todo List - Main todo management page' },
            { value: 'todo.show', label: 'View Todo - Single todo item view' }
        ]
    };

    const routes = pluginRoutes[pluginName] || [];

    routes.forEach(route => {
        const option = document.createElement('option');
        option.value = route.value;
        option.textContent = route.label;
        routeSelect.appendChild(option);
    });
}

function findPluginForRoute(routeValue) {
    if (!routeValue) return null;

    // Define the same route mappings as in loadRoutesForPlugin
    const pluginRoutes = {
        'core': ['dashboard', 'plugins.index'],
        'users': ['users.index', 'users.create', 'users.show', 'users.edit', 'roles.index', 'roles.create', 'roles.show', 'roles.edit', 'permissions.index'],
        'business': ['business.index', 'business.create', 'business.show', 'business.edit', 'business.contacts.index', 'business.contacts.create', 'business.contacts.show', 'business.contacts.edit'],
        'navigation': ['navigation.index'],
        'announcements': ['announcements.index', 'announcements.create', 'announcements.show', 'announcements.edit'],
        'settings': ['settings.index', 'settings.general', 'settings.security', 'settings.backup'],
        'products': ['products.index', 'products.create', 'products.show', 'products.edit', 'all-products.index', 'products.api'],
        'todo': ['todo.index', 'todo.show']
    };

    // Find which plugin contains this route
    for (const [pluginName, routes] of Object.entries(pluginRoutes)) {
        if (routes.includes(routeValue)) {
            return pluginName;
        }
    }

    return null; // Route not found in predefined routes
}

function getSelectedRoute(formData) {
    // Check which route method is selected
    const routeMethodRadios = document.querySelectorAll('input[name="routeMethod"]');
    let selectedMethod = 'select'; // default

    routeMethodRadios.forEach(radio => {
        if (radio.checked) {
            selectedMethod = radio.value;
        }
    });

    if (selectedMethod === 'select') {
        // Get route from plugin/route dropdown selection
        const routeSelect = document.getElementById('menuRouteSelect');
        return routeSelect ? routeSelect.value : '';
    } else {
        // Get route from manual input
        return formData.get('route') || '';
    }
}

function renderNavigationTree() {
    const container = document.getElementById('navigation-tree');
    container.innerHTML = '';

    if (navigationData.length === 0) {
        const emptyMessage = document.createElement('div');
        emptyMessage.className = 'p-4 text-gray-500 dark:text-gray-400 text-center';
        emptyMessage.textContent = 'No navigation items found. Use "Add Menu Item" to create new items or "Reset Navigation" to restore defaults.';
        container.appendChild(emptyMessage);
        return;
    }

    navigationData.forEach(item => {
        const element = createMenuElement(item);
        container.appendChild(element);
    });
}

function createMenuElement(item) {
    // Determine item type and colors
    const itemType = getItemType(item);
    const colors = getTypeColors(itemType);

    const div = document.createElement('div');
    div.className = `menu-item ${colors.bg} border ${colors.border} rounded-lg p-4 mb-3 transition-all duration-200 hover:shadow-md`;
    div.dataset.id = item.id;

    // Create the main content
    const mainContent = document.createElement('div');
    mainContent.className = 'flex items-start justify-between';

    // Left side content
    const leftContent = document.createElement('div');
    leftContent.className = 'flex items-start space-x-4 flex-1';

    // Drag handle and icon
    const iconSection = document.createElement('div');
    iconSection.className = 'flex items-center space-x-2';
    iconSection.innerHTML = `
        <i class="fas fa-grip-vertical text-gray-400 dark:text-gray-500 cursor-move"></i>
        <i class="${item.icon || getDefaultIcon(itemType)} ${colors.icon} text-lg"></i>
    `;

    // Main info section
    const infoSection = document.createElement('div');
    infoSection.className = 'flex-1 min-w-0';

    // Title and badges row
    const titleRow = document.createElement('div');
    titleRow.className = 'flex items-center space-x-2 mb-2';
    titleRow.innerHTML = `
        <h4 class="font-semibold text-gray-900 dark:text-white truncate">${item.label || item.name}</h4>
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${colors.badge}">
            ${getTypeLabel(itemType)}
        </span>
        ${!item.is_active ? '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 transition duration-150 ease-in-out">Inactive</span>' : ''}
        ${!item.visible ? '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 transition duration-150 ease-in-out">Hidden</span>' : ''}
    `;

    // Details row
    const detailsRow = document.createElement('div');
    detailsRow.className = 'space-y-1';

    const systemName = document.createElement('div');
    systemName.className = 'text-sm text-gray-500 dark:text-gray-400 font-mono';
    systemName.textContent = `ID: ${item.name}`;

    const destination = getDestinationInfo(item);
    const destinationDiv = document.createElement('div');
    destinationDiv.className = 'text-sm text-gray-600 dark:text-gray-300';
    destinationDiv.innerHTML = `<i class="fas fa-link mr-1"></i>${destination}`;

    // Additional info row
    const additionalInfo = document.createElement('div');
    additionalInfo.className = 'flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400 mt-2';

    const infoItems = [];
    if (item.plugin) {
        infoItems.push(`<span><i class="fas fa-puzzle-piece mr-1"></i>Plugin: ${item.plugin}</span>`);
    }
    if (item.permissions && item.permissions.length > 0) {
        infoItems.push(`<span><i class="fas fa-shield-alt mr-1"></i>Permissions: ${item.permissions.length}</span>`);
    }
    if (item.children && item.children.length > 0) {
        infoItems.push(`<span><i class="fas fa-sitemap mr-1"></i>Children: ${item.children.length}</span>`);
    }
    if (item.sort_order !== undefined) {
        infoItems.push(`<span><i class="fas fa-sort mr-1"></i>Order: ${item.sort_order}</span>`);
    }

    additionalInfo.innerHTML = infoItems.join('');

    detailsRow.appendChild(systemName);
    detailsRow.appendChild(destinationDiv);
    detailsRow.appendChild(additionalInfo);

    infoSection.appendChild(titleRow);
    infoSection.appendChild(detailsRow);

    leftContent.appendChild(iconSection);
    leftContent.appendChild(infoSection);

    // Right side actions
    const rightContent = document.createElement('div');
    rightContent.className = 'flex flex-col items-end space-y-2';

    // Action buttons row
    const actionsRow = document.createElement('div');
    actionsRow.className = 'flex items-center space-x-2';

    // Add Sub-Item button
    const addSubButton = document.createElement('button');
    addSubButton.className = 'inline-flex items-center px-2 py-1 text-xs font-medium rounded-md text-green-700 dark:text-green-300 bg-green-100 dark:bg-green-900/30 border border-green-300 dark:border-green-700 hover:bg-green-200 dark:hover:bg-green-900/50 transition-colors';
    addSubButton.title = 'Add Sub-Item';
    addSubButton.innerHTML = '<i class="fas fa-plus mr-1"></i>Sub-Item';
    addSubButton.onclick = () => showCreateModal(item.id);
    actionsRow.appendChild(addSubButton);

    // Edit button
    const editButton = document.createElement('button');
    editButton.className = 'inline-flex items-center px-2 py-1 text-xs font-medium rounded-md text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/30 border border-blue-300 dark:border-blue-700 hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors';
    editButton.title = 'Edit Menu Item';
    editButton.innerHTML = '<i class="fas fa-edit mr-1"></i>Edit';
    editButton.onclick = () => editMenuItem(item.id);
    actionsRow.appendChild(editButton);

    // Delete button (only for non-system items)
    if (!item.is_system) {
        const deleteButton = document.createElement('button');
        deleteButton.className = 'inline-flex items-center px-2 py-1 text-xs font-medium rounded-md text-red-700 dark:text-red-300 bg-red-100 dark:bg-red-900/30 border border-red-300 dark:border-red-700 hover:bg-red-200 dark:hover:bg-red-900/50 transition-colors';
        deleteButton.title = 'Delete Menu Item';
        deleteButton.innerHTML = '<i class="fas fa-trash mr-1"></i>Delete';
        deleteButton.onclick = () => deleteMenuItem(item.id);
        actionsRow.appendChild(deleteButton);
    } else {
        const systemLabel = document.createElement('span');
        systemLabel.className = 'inline-flex items-center px-2 py-1 text-xs font-medium rounded-md text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600';
        systemLabel.innerHTML = '<i class="fas fa-lock mr-1"></i>Protected';
        systemLabel.title = 'System items cannot be deleted';
        actionsRow.appendChild(systemLabel);
    }

    rightContent.appendChild(actionsRow);

    mainContent.appendChild(leftContent);
    mainContent.appendChild(rightContent);
    div.appendChild(mainContent);

    // Add children if they exist
    if (item.filtered_children && item.filtered_children.length > 0) {
        const childrenContainer = document.createElement('div');
        childrenContainer.className = 'ml-6 mt-3 space-y-2 nested-sortable';
        childrenContainer.dataset.parentId = item.id;

        item.filtered_children.forEach(child => {
            const childElement = createMenuElement(child);
            childrenContainer.appendChild(childElement);
        });

        div.appendChild(childrenContainer);
    } else {
        // Add an empty drop zone for items without children
        const dropZone = document.createElement('div');
        dropZone.className = 'ml-6 mt-2 h-8 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded nested-sortable hidden';
        dropZone.dataset.parentId = item.id;
        dropZone.innerHTML = '<div class="flex items-center justify-center h-full text-xs text-gray-400 dark:text-gray-500">Drop items here to make them sub-items</div>';
        div.appendChild(dropZone);
    }

    return div;
}

function initializeSortable() {
    const container = document.getElementById('navigation-tree');

    new Sortable(container, {
        animation: 150,
        handle: '.fa-grip-vertical',
        group: 'navigation',
        fallbackOnBody: true,
        swapThreshold: 0.65,
        ghostClass: 'sortable-ghost',
        chosenClass: 'sortable-chosen',
        dragClass: 'sortable-drag',
        onEnd: function(evt) {
            handleDragEnd(evt);
        },
        onMove: function(evt) {
            return handleDragMove(evt);
        },
        onAdd: function(evt) {
            // Show drop zone when dragging over
            const dropZone = evt.to;
            if (dropZone.classList.contains('nested-sortable')) {
                dropZone.classList.add('drop-zone-active');
            }
        },
        onRemove: function(evt) {
            // Hide drop zone when dragging away
            const dropZone = evt.from;
            if (dropZone.classList.contains('nested-sortable')) {
                dropZone.classList.remove('drop-zone-active');
            }
        },
        onAdd: function(evt) {
            // Show drop zone when dragging over
            const dropZone = evt.to;
            if (dropZone.classList.contains('nested-sortable')) {
                dropZone.classList.add('drop-zone-active');
            }
        },
        onRemove: function(evt) {
            // Hide drop zone when dragging away
            const dropZone = evt.from;
            if (dropZone.classList.contains('nested-sortable')) {
                dropZone.classList.remove('drop-zone-active');
            }
        }
    });

    // Initialize sortable for nested containers
    initializeNestedSortable();
}

function initializeNestedSortable() {
    const nestedContainers = document.querySelectorAll('.nested-sortable');

    nestedContainers.forEach(container => {
        new Sortable(container, {
            animation: 150,
            handle: '.fa-grip-vertical',
            group: 'navigation',
            fallbackOnBody: true,
            swapThreshold: 0.65,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            dragClass: 'sortable-drag',
            onEnd: function(evt) {
                handleDragEnd(evt);
            },
            onMove: function(evt) {
                return handleDragMove(evt);
            }
        });
    });
}

function handleDragMove(evt) {
    const draggedElement = evt.dragged;
    const relatedElement = evt.related;

    // Prevent dropping a parent into its own child
    if (isDescendant(relatedElement, draggedElement)) {
        return false;
    }

    return true;
}

function handleDragEnd(evt) {
    const draggedElement = evt.item;
    const targetContainer = evt.to;
    const sourceContainer = evt.from;

    const draggedId = parseInt(draggedElement.dataset.id);
    const targetParentElement = targetContainer.closest('.menu-item');
    const targetParentId = targetParentElement ? parseInt(targetParentElement.dataset.id) : null;

    // Check if we're dropping onto a different parent
    if (targetContainer !== sourceContainer || targetParentId !== null) {
        updateMenuParent(draggedId, targetParentId);
    } else {
        updateMenuOrder();
    }
}

function isDescendant(parent, child) {
    let node = child.parentNode;
    while (node != null) {
        if (node === parent) {
            return true;
        }
        node = node.parentNode;
    }
    return false;
}



function showCreateModal(parentId = null) {
    isEditing = false;
    editingId = null;
    isAutoGeneratingName = true; // Enable auto-generation for new items

    // Set title based on whether this is a sub-item or main item
    if (parentId) {
        document.getElementById('modalTitle').textContent = 'Add Sub-Menu Item';
    } else {
        document.getElementById('modalTitle').textContent = 'Add Menu Item';
    }

    document.getElementById('menuForm').reset();
    document.getElementById('menuId').value = '';

    // Reset route selection to default (select method)
    document.querySelector('input[name="routeMethod"][value="select"]').checked = true;
    document.getElementById('routeSelectContainer').style.display = 'block';
    document.getElementById('routeManualContainer').style.display = 'none';

    // Reset plugin and route dropdowns
    const routePluginSelect = document.getElementById('routePluginSelect');
    const routeSelect = document.getElementById('menuRouteSelect');

    if (routePluginSelect) {
        routePluginSelect.value = '';
    }
    if (routeSelect) {
        routeSelect.innerHTML = '<option value="">-- Select a plugin first --</option>';
        routeSelect.disabled = true;
    }
    document.getElementById('menuRoute').value = '';

    // Populate parent options and set the parent if provided
    populateParentOptions();
    if (parentId) {
        document.getElementById('menuParent').value = parentId;
    }

    toggleModalFormFields(); // Reset field visibility
    document.getElementById('menuModal').classList.remove('hidden');
}

function toggleModalFormFields() {
    const typeSelect = document.getElementById('menuType');
    const behaviorField = document.getElementById('modalBehaviorField');
    const iconField = document.getElementById('modalIconField');
    const urlField = document.getElementById('modalUrlField');
    const routeField = document.getElementById('modalRouteField');
    const labelField = document.getElementById('menuLabel');

    if (typeSelect.value === 'separator') {
        behaviorField.style.display = 'none';
        iconField.style.display = 'none';
        urlField.style.display = 'none';
        routeField.style.display = 'none';
        labelField.placeholder = 'Section label (optional for separators)';
        labelField.required = false;
    } else {
        behaviorField.style.display = 'block';
        iconField.style.display = 'block';
        routeField.style.display = 'block';
        labelField.placeholder = 'Display text for the menu item';
        labelField.required = true;
        toggleModalBehaviorFields();
    }
}

function toggleModalBehaviorFields() {
    const behaviorSelect = document.getElementById('menuBehavior');
    const urlField = document.getElementById('modalUrlField');
    const urlInput = document.getElementById('menuUrl');

    if (behaviorSelect.value === 'parent') {
        urlField.style.display = 'none';
        urlInput.required = false;
    } else {
        urlField.style.display = 'block';
        urlInput.required = false; // URL is optional if route is specified

        if (behaviorSelect.value === 'external') {
            urlInput.placeholder = 'https://example.com';
        } else {
            urlInput.placeholder = '/custom-page';
        }
    }
}

function editMenuItem(id) {
    isEditing = true;
    editingId = id;

    // Find the menu item in navigationData
    const item = findMenuItemById(id, navigationData);
    if (!item) return;

    document.getElementById('modalTitle').textContent = 'Edit Menu Item';
    document.getElementById('menuId').value = item.id;
    document.getElementById('menuName').value = item.name;
    document.getElementById('menuType').value = item.type || 'link';

    // Determine behavior based on existing data
    let behavior = 'internal';
    if (item.target === '_blank') {
        behavior = 'external';
    } else if (item.url === '#' || !item.url) {
        behavior = 'parent';
    }
    document.getElementById('menuBehavior').value = behavior;

    document.getElementById('menuLabel').value = item.label;
    document.getElementById('menuIcon').value = item.icon || '';
    document.getElementById('menuUrl').value = item.url || '';

    // Handle route selection
    const routeValue = item.route || '';
    document.getElementById('menuRoute').value = routeValue;

    // Try to find which plugin this route belongs to and set up the dropdowns
    const pluginForRoute = findPluginForRoute(routeValue);

    if (pluginForRoute && routeValue) {
        // Route exists in our predefined routes, use select method
        document.querySelector('input[name="routeMethod"][value="select"]').checked = true;
        document.getElementById('routeSelectContainer').style.display = 'block';
        document.getElementById('routeManualContainer').style.display = 'none';

        // Set the plugin dropdown
        const routePluginSelect = document.getElementById('routePluginSelect');
        routePluginSelect.value = pluginForRoute;

        // Load routes for the plugin and then set the route
        loadRoutesForPlugin(pluginForRoute);
        setTimeout(() => {
            const routeSelect = document.getElementById('menuRouteSelect');
            routeSelect.value = routeValue;
        }, 100);
    } else {
        // Route doesn't exist in our predefined routes or is empty, use manual method
        document.querySelector('input[name="routeMethod"][value="manual"]').checked = true;
        document.getElementById('routeSelectContainer').style.display = 'none';
        document.getElementById('routeManualContainer').style.display = 'block';
    }

    document.getElementById('menuPlugin').value = item.plugin || '';
    document.getElementById('menuTarget').value = item.target || '_self';
    document.getElementById('menuActive').checked = item.is_active;
    document.getElementById('menuVisible').checked = item.visible ?? true;

    // Set permissions using the permission selector
    setPermissionSelection(item.permissions || []);

    // Disable auto-generation for existing items
    isAutoGeneratingName = false;

    populateParentOptions(item.id);
    document.getElementById('menuParent').value = item.parent_id || '';

    // Toggle form fields based on type
    toggleModalFormFields();

    document.getElementById('menuModal').classList.remove('hidden');
}

function hideMenuModal() {
    document.getElementById('menuModal').classList.add('hidden');
}

function populateParentOptions(excludeId = null) {
    const select = document.getElementById('menuParent');
    select.innerHTML = '<option value="">Top Level</option>';

    function addOptions(items, level = 0) {
        items.forEach(item => {
            if (item.id !== excludeId) {
                const option = document.createElement('option');
                option.value = item.id;
                option.textContent = '  '.repeat(level) + item.label;
                select.appendChild(option);

                if (item.filtered_children && item.filtered_children.length > 0) {
                    addOptions(item.filtered_children, level + 1);
                }
            }
        });
    }

    addOptions(navigationData);
}

function findMenuItemById(id, items) {
    for (const item of items) {
        if (item.id === id) {
            return item;
        }
        if (item.filtered_children) {
            const found = findMenuItemById(id, item.filtered_children);
            if (found) return found;
        }
    }
    return null;
}

// Form submission
document.getElementById('menuForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const behavior = formData.get('behavior') || 'internal';
    let url = formData.get('url');
    let target = formData.get('target') || '_self';

    // Adjust URL and target based on behavior
    if (behavior === 'parent' && !url) {
        url = '#';
    } else if (behavior === 'external') {
        target = '_blank';
    }

    const data = {
        name: formData.get('name'),
        type: formData.get('type') || 'link',
        label: formData.get('label'),
        icon: formData.get('icon'),
        url: url,
        route: getSelectedRoute(formData),
        plugin: formData.get('plugin'),
        parent_id: formData.get('parent_id') || null,
        target: target,
        is_active: formData.has('is_active'),
        visible: formData.has('visible'),
        permissions: formData.get('permissions') ? formData.get('permissions').split(',').map(p => p.trim()).filter(p => p) : []
    };

    // Validate required fields
    if (!data.name) {
        showNotification('Name is required', 'error');
        return;
    }
    if (!data.label && data.type !== 'separator') {
        showNotification('Label is required for links', 'error');
        return;
    }

    const apiUrl = isEditing ? `/navigation/${editingId}` : '/navigation';
    const method = isEditing ? 'PUT' : 'POST';
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    console.log('Making request to:', apiUrl);
    console.log('Method:', method);
    console.log('CSRF Token:', csrfToken);
    console.log('Data:', JSON.stringify(data, null, 2));

    fetch(apiUrl, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': csrfToken,
            'Accept': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        return response.text().then(text => {
            console.log('Raw response:', text);
            try {
                return JSON.parse(text);
            } catch (e) {
                console.error('JSON parse error:', e);
                console.error('Response text:', text);
                throw new Error('Invalid JSON response: ' + text.substring(0, 100));
            }
        });
    })
    .then(data => {
        if (data.success) {
            hideMenuModal();
            loadNavigationTree();
            showNotification(data.message, 'success');
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error saving menu item:', error);
        showNotification('Error saving menu item', 'error');
    });
});



function deleteMenuItem(id) {
    // Find the menu item in navigationData to check if it has children
    const item = findMenuItemById(id, navigationData);
    if (!item) {
        showNotification('Menu item not found', 'error');
        return;
    }

    // Create appropriate confirmation message
    let confirmMessage = `Are you sure you want to delete "${item.label}"?`;
    if (item.filtered_children && item.filtered_children.length > 0) {
        confirmMessage += `\n\nThis will also delete ${item.filtered_children.length} sub-menu item(s).`;
    }
    confirmMessage += '\n\nThis action cannot be undone.';

    if (!confirm(confirmMessage)) {
        return;
    }

    // Show loading state
    showNotification('Deleting menu item...', 'info');

    fetch(`/navigation/${id}`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            loadNavigationTree();
            showNotification(data.message, 'success');
        } else {
            showNotification(data.message || 'Failed to delete menu item', 'error');
        }
    })
    .catch(error => {
        console.error('Error deleting menu item:', error);
        showNotification('Error deleting menu item: ' + error.message, 'error');
    });
}

function updateMenuOrder() {
    const items = [];

    // Process root level items
    const rootContainer = document.getElementById('navigation-tree');
    const rootItems = rootContainer.children;

    Array.from(rootItems).forEach((item, index) => {
        if (item.classList.contains('menu-item')) {
            const itemData = {
                id: parseInt(item.dataset.id),
                sort_order: index,
                parent_id: null
            };
            items.push(itemData);

            // Process children
            const childContainer = item.querySelector('.nested-sortable');
            if (childContainer) {
                const childItems = childContainer.children;
                Array.from(childItems).forEach((childItem, childIndex) => {
                    if (childItem.classList.contains('menu-item')) {
                        items.push({
                            id: parseInt(childItem.dataset.id),
                            sort_order: childIndex,
                            parent_id: parseInt(item.dataset.id)
                        });
                    }
                });
            }
        }
    });

    fetch('/navigation/update-order', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ items })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Menu order updated', 'success');
            loadNavigationTree(); // Refresh the tree
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error updating menu order:', error);
        showNotification('Error updating menu order', 'error');
    });
}

function updateMenuParent(itemId, newParentId) {
    fetch(`/navigation/${itemId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            parent_id: newParentId,
            _method: 'PUT'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Menu structure updated', 'success');
            loadNavigationTree(); // Refresh the tree
        } else {
            showNotification(data.message, 'error');
            loadNavigationTree(); // Refresh on error to revert changes
        }
    })
    .catch(error => {
        console.error('Error updating menu parent:', error);
        showNotification('Error updating menu structure', 'error');
        loadNavigationTree(); // Refresh on error to revert changes
    });
}

function resetNavigation() {
    if (!confirm('Are you sure you want to reset the navigation to default? This will remove all custom menu items.')) {
        return;
    }

    fetch('/navigation/reset', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadNavigationTree();
            showNotification(data.message, 'success');
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error resetting navigation:', error);
        showNotification('Error resetting navigation', 'error');
    });
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg ${
        type === 'success' ? 'bg-green-500 dark:bg-green-600 text-white' :
        type === 'error' ? 'bg-red-500 dark:bg-red-600 text-white' :
        'bg-blue-600 dark:bg-blue-700 text-white'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Remove after 5 seconds
    setTimeout(() => {
        notification.remove();
    }, 5000);
}

// Permission Selector Functions
function togglePermissionDropdown() {
    const dropdown = document.getElementById('permissionDropdown');
    dropdown.classList.toggle('hidden');
}

function filterPermissions() {
    const searchTerm = document.getElementById('permissionSearch').value.toLowerCase();
    const options = document.querySelectorAll('.permission-option');

    options.forEach(option => {
        const text = option.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            option.style.display = 'flex';
        } else {
            option.style.display = 'none';
        }
    });
}

function updatePermissionSelection() {
    const checkboxes = document.querySelectorAll('.permission-checkbox:checked');
    const selectedPermissions = Array.from(checkboxes).map(cb => cb.value);
    const hiddenInput = document.getElementById('menuPermissions');
    const displayText = document.getElementById('permissionDropdownText');

    hiddenInput.value = selectedPermissions.join(',');

    if (selectedPermissions.length === 0) {
        displayText.textContent = 'Select permissions...';
        displayText.className = 'block truncate text-gray-500 dark:text-gray-400';
    } else if (selectedPermissions.length === 1) {
        const checkbox = document.querySelector(`.permission-checkbox[value="${selectedPermissions[0]}"]`);
        const label = checkbox.closest('.permission-option');
        const displayName = label.querySelector('.text-sm.font-medium').textContent;
        displayText.textContent = displayName;
        displayText.className = 'block truncate text-gray-900 dark:text-white';
    } else {
        displayText.textContent = `${selectedPermissions.length} permissions selected`;
        displayText.className = 'block truncate text-gray-900 dark:text-white';
    }
}

function setPermissionSelection(permissions) {
    // Clear all checkboxes first
    document.querySelectorAll('.permission-checkbox').forEach(cb => cb.checked = false);

    // Check the selected permissions
    if (permissions && permissions.length > 0) {
        permissions.forEach(permission => {
            const checkbox = document.querySelector(`.permission-checkbox[value="${permission}"]`);
            if (checkbox) {
                checkbox.checked = true;
            }
        });
    }

    updatePermissionSelection();
}

// Close dropdown when clicking outside
document.addEventListener('click', function(event) {
    const dropdown = document.getElementById('permissionDropdown');
    const button = document.getElementById('permissionDropdownBtn');

    if (dropdown && button && !dropdown.contains(event.target) && !button.contains(event.target)) {
        dropdown.classList.add('hidden');
    }
});

// Icon Selector Functions
let currentIconInputId = null;

const iconList = [
    // Common icons - Font Awesome 6 Pro Solid
    'fa-solid fa-house', 'fa-solid fa-user', 'fa-solid fa-users', 'fa-solid fa-gear', 'fa-solid fa-sliders', 'fa-solid fa-gauge', 'fa-solid fa-gauge-high',
    'fa-solid fa-chart-bar', 'fa-solid fa-chart-line', 'fa-solid fa-chart-pie', 'fa-solid fa-file', 'fa-solid fa-folder', 'fa-solid fa-envelope',
    'fa-solid fa-phone', 'fa-solid fa-calendar', 'fa-solid fa-clock', 'fa-solid fa-bell', 'fa-solid fa-star', 'fa-solid fa-heart', 'fa-solid fa-bookmark',
    'fa-solid fa-tag', 'fa-solid fa-tags', 'fa-solid fa-magnifying-glass', 'fa-solid fa-filter', 'fa-solid fa-sort', 'fa-solid fa-pen', 'fa-solid fa-trash',
    'fa-solid fa-plus', 'fa-solid fa-minus', 'fa-solid fa-xmark', 'fa-solid fa-check', 'fa-solid fa-arrow-left', 'fa-solid fa-arrow-right',
    'fa-solid fa-arrow-up', 'fa-solid fa-arrow-down', 'fa-solid fa-download', 'fa-solid fa-upload', 'fa-solid fa-floppy-disk', 'fa-solid fa-print',
    'fa-solid fa-share', 'fa-solid fa-link', 'fa-solid fa-arrow-up-right-from-square', 'fa-solid fa-lock', 'fa-solid fa-unlock', 'fa-solid fa-key',
    'fa-solid fa-shield', 'fa-solid fa-eye', 'fa-solid fa-eye-slash', 'fa-solid fa-circle-info', 'fa-solid fa-circle-question', 'fa-solid fa-circle-exclamation',
    'fa-solid fa-triangle-exclamation', 'fa-solid fa-ban', 'fa-solid fa-circle-check', 'fa-solid fa-circle-xmark', 'fa-solid fa-circle-info',
    'fa-solid fa-circle-question', 'fa-solid fa-circle-exclamation', 'fa-solid fa-lightbulb', 'fa-solid fa-wand-magic-sparkles', 'fa-solid fa-wrench',
    'fa-solid fa-screwdriver-wrench', 'fa-solid fa-hammer', 'fa-solid fa-screwdriver', 'fa-solid fa-paintbrush', 'fa-solid fa-palette', 'fa-solid fa-image',
    'fa-solid fa-images', 'fa-solid fa-camera', 'fa-solid fa-video', 'fa-solid fa-music', 'fa-solid fa-headphones', 'fa-solid fa-microphone',
    'fa-solid fa-volume-high', 'fa-solid fa-volume-low', 'fa-solid fa-volume-xmark', 'fa-solid fa-play', 'fa-solid fa-pause', 'fa-solid fa-stop',
    'fa-solid fa-forward', 'fa-solid fa-backward', 'fa-solid fa-forward-step', 'fa-solid fa-backward-step', 'fa-solid fa-eject',
    'fa-solid fa-shuffle', 'fa-solid fa-repeat', 'fa-solid fa-wifi', 'fa-solid fa-signal', 'fa-solid fa-battery-full', 'fa-solid fa-battery-half',
    'fa-solid fa-battery-empty', 'fa-solid fa-plug', 'fa-solid fa-power-off', 'fa-solid fa-desktop', 'fa-solid fa-laptop', 'fa-solid fa-tablet',
    'fa-solid fa-mobile', 'fa-solid fa-keyboard', 'fa-solid fa-computer-mouse', 'fa-solid fa-gamepad', 'fa-solid fa-tv', 'fa-solid fa-radio',

    // Additional Common icons - Font Awesome 6 Pro
    'fa-solid fa-globe', 'fa-solid fa-earth-americas', 'fa-solid fa-earth-asia', 'fa-solid fa-earth-europe', 'fa-solid fa-language',
    'fa-solid fa-flag', 'fa-solid fa-bookmark', 'fa-solid fa-thumbs-up', 'fa-solid fa-thumbs-down', 'fa-regular fa-thumbs-up', 'fa-regular fa-thumbs-down',
    'fa-solid fa-face-smile', 'fa-solid fa-face-frown', 'fa-solid fa-face-meh', 'fa-solid fa-face-laugh', 'fa-solid fa-face-angry', 'fa-solid fa-face-surprise',
    'fa-solid fa-copy', 'fa-solid fa-scissors', 'fa-solid fa-paste', 'fa-solid fa-rotate-left', 'fa-solid fa-rotate-right', 'fa-solid fa-arrows-rotate', 'fa-solid fa-arrow-rotate-right',
    'fa-solid fa-clock-rotate-left', 'fa-solid fa-box-archive', 'fa-solid fa-box', 'fa-solid fa-boxes-stacked', 'fa-solid fa-cube', 'fa-solid fa-cubes',
    'fa-solid fa-layer-group', 'fa-solid fa-bars-staggered', 'fa-solid fa-list', 'fa-solid fa-list-ol', 'fa-solid fa-indent', 'fa-solid fa-outdent',
    'fa-solid fa-align-left', 'fa-solid fa-align-center', 'fa-solid fa-align-right', 'fa-solid fa-align-justify', 'fa-solid fa-text-height',
    'fa-solid fa-text-width', 'fa-solid fa-bold', 'fa-solid fa-italic', 'fa-solid fa-underline', 'fa-solid fa-strikethrough',
    'fa-solid fa-font', 'fa-solid fa-paragraph', 'fa-solid fa-heading', 'fa-solid fa-quote-left', 'fa-solid fa-quote-right',
    'fa-solid fa-superscript', 'fa-solid fa-subscript', 'fa-solid fa-highlighter', 'fa-solid fa-marker', 'fa-solid fa-pen',
    'fa-solid fa-pencil', 'fa-solid fa-eraser', 'fa-solid fa-spell-check', 'fa-solid fa-language', 'fa-solid fa-keyboard',

    // Business icons - Font Awesome 6 Pro
    'fa-solid fa-briefcase', 'fa-solid fa-building', 'fa-solid fa-industry', 'fa-solid fa-store', 'fa-solid fa-cart-shopping',
    'fa-solid fa-bag-shopping', 'fa-solid fa-credit-card', 'fa-solid fa-money-bill', 'fa-solid fa-coins', 'fa-solid fa-dollar-sign',
    'fa-solid fa-euro-sign', 'fa-solid fa-sterling-sign', 'fa-solid fa-yen-sign', 'fa-solid fa-calculator', 'fa-solid fa-receipt',
    'fa-solid fa-handshake', 'fa-solid fa-signature', 'fa-solid fa-chart-area', 'fa-solid fa-chart-pie', 'fa-solid fa-chart-simple',
    'fa-solid fa-scale-balanced', 'fa-solid fa-gavel', 'fa-solid fa-clipboard', 'fa-solid fa-clipboard-list', 'fa-solid fa-list-check',

    // Additional Business & Finance icons - Font Awesome 6 Pro
    'fa-solid fa-building-columns', 'fa-solid fa-university', 'fa-solid fa-landmark', 'fa-solid fa-piggy-bank', 'fa-solid fa-vault',
    'fa-solid fa-safe', 'fa-solid fa-cash-register', 'fa-solid fa-money-check', 'fa-solid fa-money-check-dollar', 'fa-solid fa-wallet',
    'fa-solid fa-hand-holding-dollar', 'fa-solid fa-hand-holding-dollar', 'fa-solid fa-filter-circle-dollar', 'fa-solid fa-magnifying-glass-dollar',
    'fa-solid fa-percent', 'fa-solid fa-percent', 'fa-solid fa-chart-line-up', 'fa-solid fa-arrow-trend-up', 'fa-solid fa-arrow-trend-down',
    'fa-solid fa-business-time', 'fa-solid fa-city', 'fa-solid fa-warehouse', 'fa-solid fa-industry', 'fa-solid fa-building-user',
    'fa-solid fa-shop', 'fa-solid fa-shop', 'fa-solid fa-store', 'fa-solid fa-shop-lock', 'fa-solid fa-shop-slash',
    'fa-solid fa-file-contract', 'fa-solid fa-file-contract', 'fa-solid fa-file-invoice', 'fa-solid fa-file-invoice-dollar',
    'fa-solid fa-file-invoice', 'fa-solid fa-file-lines', 'fa-solid fa-file-pen', 'fa-solid fa-quote-left', 'fa-solid fa-file-signature',
    'fa-solid fa-handshake-simple', 'fa-solid fa-handshake-angle', 'fa-solid fa-people-group', 'fa-solid fa-building-shield', 'fa-solid fa-building-lock',
    'fa-solid fa-chart-line', 'fa-solid fa-briefcase', 'fa-solid fa-chart-column', 'fa-solid fa-chart-gantt', 'fa-solid fa-coins',
    'fa-solid fa-money-bills', 'fa-solid fa-bitcoin-sign', 'fa-solid fa-ethereum', 'fa-solid fa-pickaxe', 'fa-solid fa-arrow-right-arrow-left',
    'fa-solid fa-chart-line', 'fa-solid fa-chart-line-down', 'fa-solid fa-money-bill-trend-up', 'fa-solid fa-sack-dollar', 'fa-solid fa-budget',
    'fa-solid fa-crystal-ball', 'fa-solid fa-chart-simple', 'fa-solid fa-bullseye', 'fa-solid fa-flag-checkered', 'fa-solid fa-road',
    'fa-solid fa-gauge-high', 'fa-solid fa-chart-bar', 'fa-solid fa-tachometer-alt', 'fa-solid fa-arrow-up-right-dots', 'fa-solid fa-rocket',

    // Social Media & Communication icons - Font Awesome 6 Pro
    'fa-brands fa-whatsapp', 'fa-brands fa-twitter', 'fa-brands fa-facebook', 'fa-brands fa-facebook-f', 'fa-brands fa-instagram',
    'fa-brands fa-linkedin', 'fa-brands fa-linkedin-in', 'fa-brands fa-youtube', 'fa-brands fa-telegram', 'fa-brands fa-snapchat',
    'fa-brands fa-tiktok', 'fa-brands fa-discord', 'fa-brands fa-slack', 'fa-brands fa-skype', 'fa-brands fa-viber', 'fa-brands fa-pinterest',
    'fa-brands fa-reddit', 'fa-brands fa-tumblr', 'fa-brands fa-twitch', 'fa-brands fa-github', 'fa-brands fa-gitlab', 'fa-brands fa-bitbucket',
    'fa-brands fa-google', 'fa-brands fa-google-plus', 'fa-brands fa-apple', 'fa-brands fa-microsoft', 'fa-brands fa-amazon',
    'fa-solid fa-envelope', 'fa-solid fa-phone', 'fa-solid fa-mobile-screen', 'fa-solid fa-fax', 'fa-solid fa-comments', 'fa-solid fa-comment',
    'fa-solid fa-message', 'fa-solid fa-at', 'fa-solid fa-hashtag', 'fa-solid fa-share', 'fa-solid fa-square-share-nodes',

    // Additional Social & Communication icons - Font Awesome 6 Pro
    'fa-brands fa-weixin', 'fa-brands fa-weibo', 'fa-brands fa-qq', 'fa-brands fa-line', 'fa-solid fa-comment-dots', 'fa-solid fa-comments',
    'fa-brands fa-signal', 'fa-solid fa-shield-halved', 'fa-solid fa-user-secret', 'fa-solid fa-atom', 'fa-solid fa-matrix',
    'fa-brands fa-mastodon', 'fa-solid fa-share-nodes', 'fa-solid fa-brain', 'fa-solid fa-users-gear', 'fa-solid fa-people-arrows',
    'fa-solid fa-house-user', 'fa-solid fa-satellite-dish', 'fa-brands fa-zoom', 'fa-brands fa-microsoft', 'fa-brands fa-google',
    'fa-solid fa-video', 'fa-solid fa-handshake', 'fa-solid fa-users-rectangle', 'fa-solid fa-chalkboard-user',
    'fa-solid fa-video', 'fa-solid fa-camera-retro', 'fa-solid fa-tower-broadcast', 'fa-solid fa-satellite',
    'fa-solid fa-podcast', 'fa-solid fa-rss', 'fa-solid fa-square-rss', 'fa-solid fa-rss', 'fa-solid fa-blog',
    'fa-solid fa-newspaper', 'fa-solid fa-book-open', 'fa-solid fa-book-journal-whills', 'fa-solid fa-file-lines', 'fa-solid fa-bullhorn',
    'fa-solid fa-bullhorn', 'fa-solid fa-bullhorn', 'fa-solid fa-volume-high', 'fa-solid fa-volume-high', 'fa-solid fa-volume-high',
    'fa-solid fa-envelopes-bulk', 'fa-solid fa-envelope-open', 'fa-solid fa-square-envelope', 'fa-solid fa-inbox',
    'fa-solid fa-share-from-square', 'fa-solid fa-paper-plane', 'fa-solid fa-paper-plane', 'fa-solid fa-reply', 'fa-solid fa-reply-all',
    'fa-solid fa-share', 'fa-solid fa-box-archive', 'fa-solid fa-ban', 'fa-solid fa-trash-can', 'fa-solid fa-trash',

    // E-commerce & Payment icons - Font Awesome 6 Pro
    'fa-brands fa-paypal', 'fa-brands fa-stripe', 'fa-brands fa-cc-visa', 'fa-brands fa-cc-mastercard', 'fa-brands fa-cc-amex',
    'fa-brands fa-cc-discover', 'fa-brands fa-bitcoin', 'fa-brands fa-ethereum', 'fa-solid fa-credit-card', 'fa-solid fa-money-check',
    'fa-solid fa-wallet', 'fa-solid fa-cash-register', 'fa-solid fa-receipt', 'fa-solid fa-file-invoice', 'fa-solid fa-percent',

    // Additional E-commerce & Payment icons - Font Awesome 6 Pro
    'fa-brands fa-cc-jcb', 'fa-brands fa-cc-diners-club', 'fa-brands fa-cc-apple-pay', 'fa-brands fa-google-pay',
    'fa-brands fa-alipay', 'fa-brands fa-amazon-pay', 'fa-brands fa-square', 'fa-solid fa-money-bill-transfer', 'fa-solid fa-mobile-screen-button',
    'fa-solid fa-money-bill-wave', 'fa-solid fa-building-columns', 'fa-solid fa-arrows-rotate', 'fa-solid fa-handshake', 'fa-solid fa-calendar-check',
    'fa-solid fa-basket-shopping', 'fa-solid fa-cart-shopping', 'fa-solid fa-cart-plus', 'fa-solid fa-cart-arrow-down',
    'fa-solid fa-store', 'fa-solid fa-shop', 'fa-solid fa-building', 'fa-solid fa-city', 'fa-solid fa-building-user',
    'fa-solid fa-cash-register', 'fa-solid fa-computer', 'fa-solid fa-barcode', 'fa-solid fa-qrcode', 'fa-solid fa-magnifying-glass',
    'fa-solid fa-tag', 'fa-solid fa-tags', 'fa-solid fa-percent', 'fa-solid fa-ticket', 'fa-solid fa-receipt',
    'fa-solid fa-gift', 'fa-solid fa-id-card', 'fa-solid fa-address-card', 'fa-solid fa-calendar-days',
    'fa-solid fa-arrows-rotate', 'fa-solid fa-calendar-check', 'fa-solid fa-file-invoice-dollar', 'fa-solid fa-file-invoice',
    'fa-solid fa-money-bill-transfer', 'fa-solid fa-ban', 'fa-solid fa-scale-balanced', 'fa-solid fa-user-shield', 'fa-solid fa-shield',
    'fa-solid fa-handshake-simple', 'fa-solid fa-certificate', 'fa-solid fa-award', 'fa-solid fa-shield-halved', 'fa-solid fa-lock',

    // Technology & Development icons - Font Awesome 6 Pro
    'fa-brands fa-html5', 'fa-brands fa-css3', 'fa-brands fa-js', 'fa-brands fa-react', 'fa-brands fa-vuejs', 'fa-brands fa-angular',
    'fa-brands fa-node-js', 'fa-brands fa-php', 'fa-brands fa-python', 'fa-brands fa-java', 'fa-brands fa-swift', 'fa-brands fa-android',
    'fa-brands fa-docker', 'fa-brands fa-aws', 'fa-brands fa-digital-ocean', 'fa-solid fa-server', 'fa-solid fa-database',
    'fa-solid fa-cloud', 'fa-solid fa-code', 'fa-solid fa-terminal', 'fa-solid fa-bug', 'fa-solid fa-gears',

    // Additional Technology & Development icons - Font Awesome 6 Pro
    'fa-brands fa-laravel', 'fa-brands fa-symfony', 'fa-solid fa-fire', 'fa-brands fa-wordpress', 'fa-brands fa-drupal',
    'fa-brands fa-joomla', 'fa-brands fa-magento', 'fa-brands fa-shopify', 'fa-solid fa-cart-shopping', 'fa-solid fa-store',
    'fa-brands fa-bootstrap', 'fa-brands fa-sass', 'fa-brands fa-less', 'fa-brands fa-gulp', 'fa-brands fa-grunt',
    'fa-solid fa-cube', 'fa-brands fa-npm', 'fa-brands fa-yarn', 'fa-solid fa-music', 'fa-solid fa-box',
    'fa-brands fa-git', 'fa-brands fa-git-alt', 'fa-brands fa-github', 'fa-brands fa-gitlab', 'fa-brands fa-bitbucket',
    'fa-solid fa-code-branch', 'fa-solid fa-code-merge', 'fa-solid fa-tower-observation', 'fa-solid fa-code-fork',
    'fa-solid fa-hammer', 'fa-solid fa-triangle-exclamation', 'fa-solid fa-circle-dot', 'fa-solid fa-building', 'fa-solid fa-seedling',
    'fa-solid fa-dharmachakra', 'fa-solid fa-hat-cowboy', 'fa-solid fa-horse', 'fa-solid fa-ship', 'fa-solid fa-earth-americas',
    'fa-solid fa-robot', 'fa-solid fa-user-tie', 'fa-solid fa-hat-chef', 'fa-solid fa-suitcase', 'fa-solid fa-box-open',
    'fa-solid fa-building-user', 'fa-solid fa-vault', 'fa-solid fa-route', 'fa-solid fa-border-all', 'fa-solid fa-map',
    'fa-solid fa-microchip', 'fa-solid fa-memory', 'fa-solid fa-hard-drive', 'fa-solid fa-hard-drive', 'fa-brands fa-usb',
    'fa-solid fa-ethernet', 'fa-solid fa-network-wired', 'fa-solid fa-wifi', 'fa-solid fa-toggle-on', 'fa-solid fa-shield-halved',
    'fa-solid fa-shield', 'fa-solid fa-user-secret', 'fa-solid fa-scale-balanced', 'fa-solid fa-globe', 'fa-solid fa-memory',

    // Transportation & Location icons - Font Awesome 6 Pro
    'fa-solid fa-car', 'fa-solid fa-truck', 'fa-solid fa-bus', 'fa-solid fa-taxi', 'fa-solid fa-plane', 'fa-solid fa-ship',
    'fa-solid fa-bicycle', 'fa-solid fa-motorcycle', 'fa-solid fa-train', 'fa-solid fa-train-subway', 'fa-solid fa-map',
    'fa-solid fa-location-dot', 'fa-solid fa-location-pin', 'fa-solid fa-location-arrow', 'fa-solid fa-compass',
    'fa-solid fa-route', 'fa-solid fa-road', 'fa-solid fa-square-parking', 'fa-solid fa-gas-pump',

    // Food & Restaurant icons - Font Awesome 6 Pro
    'fa-solid fa-utensils', 'fa-solid fa-mug-saucer', 'fa-solid fa-wine-glass', 'fa-solid fa-beer-mug-empty', 'fa-solid fa-martini-glass',
    'fa-solid fa-pizza-slice', 'fa-solid fa-burger', 'fa-solid fa-ice-cream', 'fa-solid fa-cake-candles',
    'fa-solid fa-apple-whole', 'fa-solid fa-carrot', 'fa-solid fa-fish', 'fa-solid fa-cheese',

    // Health & Medical icons - Font Awesome 6 Pro
    'fa-solid fa-hospital', 'fa-solid fa-truck-medical', 'fa-solid fa-user-doctor', 'fa-solid fa-stethoscope', 'fa-solid fa-pills',
    'fa-solid fa-syringe', 'fa-solid fa-temperature-three-quarters', 'fa-solid fa-heart-pulse', 'fa-solid fa-dna', 'fa-solid fa-tooth',
    'fa-solid fa-eye', 'fa-solid fa-brain', 'fa-solid fa-hand-holding-heart', 'fa-solid fa-kit-medical',

    // Sports & Recreation icons - Font Awesome 6 Pro
    'fa-solid fa-football', 'fa-solid fa-basketball', 'fa-solid fa-baseball', 'fa-solid fa-baseball',
    'fa-solid fa-volleyball', 'fa-solid fa-golf-ball-tee', 'fa-solid fa-hockey-puck', 'fa-solid fa-bowling-ball',
    'fa-solid fa-person-running', 'fa-solid fa-person-swimming', 'fa-solid fa-person-skiing', 'fa-solid fa-person-hiking', 'fa-solid fa-person-biking',
    'fa-solid fa-dumbbell', 'fa-solid fa-trophy', 'fa-solid fa-medal', 'fa-solid fa-award',

    // Weather & Nature icons - Font Awesome 6 Pro
    'fa-solid fa-sun', 'fa-solid fa-moon', 'fa-solid fa-cloud', 'fa-solid fa-cloud-rain', 'fa-solid fa-cloud-snow',
    'fa-solid fa-bolt', 'fa-solid fa-rainbow', 'fa-solid fa-snowflake', 'fa-solid fa-wind', 'fa-solid fa-temperature-arrow-up',
    'fa-solid fa-temperature-arrow-down', 'fa-solid fa-tree', 'fa-solid fa-leaf', 'fa-solid fa-seedling', 'fa-solid fa-spa',

    // Navigation icons - Font Awesome 6 Pro
    'fa-solid fa-bars', 'fa-solid fa-list', 'fa-solid fa-table-cells', 'fa-solid fa-table-cells-large', 'fa-solid fa-list', 'fa-solid fa-grip',
    'fa-solid fa-grip-vertical', 'fa-solid fa-ellipsis', 'fa-solid fa-ellipsis-vertical', 'fa-solid fa-chevron-left', 'fa-solid fa-chevron-right',
    'fa-solid fa-chevron-up', 'fa-solid fa-chevron-down', 'fa-solid fa-angle-left', 'fa-solid fa-angle-right', 'fa-solid fa-angle-up',
    'fa-solid fa-angle-down', 'fa-solid fa-caret-left', 'fa-solid fa-caret-right', 'fa-solid fa-caret-up', 'fa-solid fa-caret-down',

    // Education & Learning icons - Font Awesome 6 Pro
    'fa-solid fa-graduation-cap', 'fa-solid fa-school', 'fa-solid fa-building-columns', 'fa-solid fa-book', 'fa-solid fa-book-open',
    'fa-solid fa-book-atlas', 'fa-solid fa-building-columns', 'fa-solid fa-magnifying-glass', 'fa-solid fa-flask', 'fa-solid fa-scroll',
    'fa-solid fa-certificate', 'fa-solid fa-certificate', 'fa-solid fa-award', 'fa-solid fa-medal', 'fa-solid fa-trophy',
    'fa-solid fa-user-graduate', 'fa-solid fa-chalkboard-user', 'fa-solid fa-user-tie', 'fa-solid fa-user-check', 'fa-solid fa-handshake',
    'fa-solid fa-chalkboard', 'fa-solid fa-presentation-screen', 'fa-solid fa-users', 'fa-solid fa-hammer', 'fa-solid fa-dumbbell',
    'fa-solid fa-book-bookmark', 'fa-solid fa-list-check', 'fa-solid fa-file-lines', 'fa-solid fa-book-open-reader', 'fa-solid fa-clipboard-list',
    'fa-solid fa-house-user', 'fa-solid fa-file-circle-check', 'fa-solid fa-clipboard-question', 'fa-solid fa-circle-question', 'fa-solid fa-star',
    'fa-solid fa-chalkboard', 'fa-solid fa-chalkboard', 'fa-solid fa-chalkboard-user', 'fa-solid fa-display', 'fa-solid fa-video',

    // Security & Privacy icons - Font Awesome 6 Pro
    'fa-solid fa-shield', 'fa-solid fa-shield-halved', 'fa-solid fa-shield-check', 'fa-solid fa-shield-virus', 'fa-solid fa-user-shield',
    'fa-solid fa-lock', 'fa-solid fa-unlock', 'fa-solid fa-lock-open', 'fa-solid fa-key', 'fa-solid fa-key',
    'fa-solid fa-fingerprint', 'fa-solid fa-face-smile', 'fa-solid fa-id-card', 'fa-solid fa-eye', 'fa-solid fa-eye',
    'fa-solid fa-asterisk', 'fa-solid fa-key', 'fa-solid fa-coins', 'fa-solid fa-mobile-screen-button', 'fa-solid fa-shield-check',
    'fa-solid fa-lock', 'fa-solid fa-unlock', 'fa-solid fa-hashtag', 'fa-solid fa-signature', 'fa-solid fa-certificate',
    'fa-solid fa-globe', 'fa-solid fa-globe', 'fa-solid fa-globe', 'fa-solid fa-shield', 'fa-solid fa-user-secret',
    'fa-solid fa-shield-halved', 'fa-solid fa-shield-virus', 'fa-solid fa-bug', 'fa-solid fa-horse-head', 'fa-solid fa-virus',
    'fa-solid fa-fish', 'fa-solid fa-ban', 'fa-solid fa-user-xmark', 'fa-solid fa-triangle-exclamation', 'fa-solid fa-door-open',

    // Analytics & Reporting icons - Font Awesome 6 Pro
    'fa-solid fa-chart-simple', 'fa-solid fa-chart-bar', 'fa-solid fa-chart-line', 'fa-solid fa-chart-area', 'fa-solid fa-chart-pie',
    'fa-solid fa-chart-column', 'fa-solid fa-square-poll-vertical', 'fa-solid fa-gauge', 'fa-solid fa-gauge-high', 'fa-solid fa-gauge',
    'fa-solid fa-file-lines', 'fa-solid fa-lightbulb', 'fa-solid fa-arrow-trend-up', 'fa-solid fa-crystal-ball', 'fa-solid fa-brain',
    'fa-solid fa-database', 'fa-solid fa-table', 'fa-solid fa-eye', 'fa-solid fa-fire', 'fa-solid fa-filter',
    'fa-solid fa-arrow-right-arrow-left', 'fa-solid fa-link', 'fa-solid fa-users', 'fa-solid fa-scissors', 'fa-solid fa-users',
    'fa-solid fa-car', 'fa-solid fa-users', 'fa-solid fa-eye', 'fa-solid fa-clock', 'fa-solid fa-chart-line-down',

    // Content Management icons - Font Awesome 6 Pro
    'fa-solid fa-file-lines', 'fa-solid fa-newspaper', 'fa-solid fa-blog', 'fa-solid fa-thumbtack', 'fa-solid fa-file',
    'fa-solid fa-file-pen', 'fa-solid fa-upload', 'fa-solid fa-calendar-days', 'fa-solid fa-list', 'fa-solid fa-diagram-project',
    'fa-solid fa-sitemap', 'fa-solid fa-check-circle', 'fa-solid fa-magnifying-glass', 'fa-solid fa-clock-rotate-left', 'fa-solid fa-code-branch',
    'fa-solid fa-gear', 'fa-solid fa-pen-to-square', 'fa-solid fa-code', 'fa-brands fa-markdown', 'fa-brands fa-html5',
    'fa-solid fa-file-code', 'fa-solid fa-palette', 'fa-solid fa-table-cells', 'fa-solid fa-paintbrush', 'fa-solid fa-swatchbook',
    'fa-solid fa-photo-film', 'fa-solid fa-images', 'fa-solid fa-folder-open', 'fa-solid fa-play', 'fa-solid fa-arrows-left-right',

    // Product & Inventory icons - Font Awesome 6 Pro
    'fa-solid fa-box', 'fa-solid fa-boxes-stacked', 'fa-solid fa-box-open', 'fa-solid fa-cube', 'fa-solid fa-tag',
    'fa-solid fa-clipboard-list', 'fa-solid fa-chart-line', 'fa-solid fa-warehouse', 'fa-solid fa-database', 'fa-solid fa-layer-group',
    'fa-solid fa-book', 'fa-solid fa-folder', 'fa-solid fa-folder-open', 'fa-solid fa-list-ol', 'fa-solid fa-boxes-packing',
    'fa-solid fa-sliders', 'fa-solid fa-list-check', 'fa-solid fa-tags', 'fa-solid fa-star', 'fa-solid fa-file-lines',
    'fa-solid fa-barcode', 'fa-solid fa-qrcode', 'fa-solid fa-hashtag', 'fa-solid fa-barcode', 'fa-solid fa-book',
    'fa-solid fa-list-ol', 'fa-solid fa-layer-group', 'fa-solid fa-clock', 'fa-solid fa-calendar-xmark', 'fa-solid fa-shield-check'
];

function showIconSelector(inputId) {
    currentIconInputId = inputId;
    document.getElementById('iconSelectorModal').classList.remove('hidden');
    populateIcons();
}

function hideIconSelector() {
    document.getElementById('iconSelectorModal').classList.add('hidden');
    currentIconInputId = null;
}

function populateIcons(filteredIcons = null) {
    const iconsGrid = document.getElementById('iconsGrid');
    const icons = filteredIcons || iconList;

    iconsGrid.innerHTML = '';

    icons.forEach(iconClass => {
        const iconButton = document.createElement('button');
        iconButton.type = 'button';
        iconButton.className = 'icon-item p-3 border border-gray-200 dark:border-gray-700 rounded hover:bg-blue-50 hover:border-blue-300 transition duration-150 ease-in-out';
        iconButton.innerHTML = `<i class="${iconClass} text-lg"></i>`;
        iconButton.title = iconClass;
        iconButton.onclick = () => selectIcon(iconClass);

        iconsGrid.appendChild(iconButton);
    });
}

function selectIcon(iconClass) {
    if (currentIconInputId) {
        document.getElementById(currentIconInputId).value = iconClass;
        hideIconSelector();
    }
}

function filterIcons() {
    const searchTerm = document.getElementById('iconSearch').value.toLowerCase();
    const filteredIcons = iconList.filter(icon =>
        icon.toLowerCase().includes(searchTerm)
    );
    populateIcons(filteredIcons);
}

function filterByCategory(category) {
    // Update active button
    document.querySelectorAll('.icon-category-btn').forEach(btn => {
        btn.classList.remove('active', 'bg-blue-600 dark:bg-blue-700', 'text-white');
        btn.classList.add('bg-gray-200 dark:bg-gray-600', 'text-gray-700 dark:text-gray-300');
    });

    event.target.classList.add('active', 'bg-blue-600 dark:bg-blue-700', 'text-white');
    event.target.classList.remove('bg-gray-200 dark:bg-gray-600', 'text-gray-700 dark:text-gray-300');

    let filteredIcons;
    if (category === 'all') {
        filteredIcons = iconList;
    } else if (category === 'solid') {
        filteredIcons = iconList.filter(icon => icon.startsWith('fa-solid '));
    } else if (category === 'regular') {
        filteredIcons = iconList.filter(icon => icon.startsWith('fa-regular '));
    } else if (category === 'light') {
        filteredIcons = iconList.filter(icon => icon.startsWith('fa-light '));
    } else if (category === 'thin') {
        filteredIcons = iconList.filter(icon => icon.startsWith('fa-thin '));
    } else if (category === 'duotone') {
        filteredIcons = iconList.filter(icon => icon.startsWith('fa-duotone '));
    } else if (category === 'brands') {
        filteredIcons = iconList.filter(icon => icon.startsWith('fa-brands '));
    } else if (category === 'social') {
        // Social media and communication icons
        filteredIcons = iconList.filter(icon =>
            icon.includes('whatsapp') || icon.includes('twitter') || icon.includes('facebook') ||
            icon.includes('instagram') || icon.includes('linkedin') || icon.includes('youtube') ||
            icon.includes('telegram') || icon.includes('snapchat') || icon.includes('tiktok') ||
            icon.includes('discord') || icon.includes('slack') || icon.includes('skype') ||
            icon.includes('envelope') || icon.includes('phone') || icon.includes('comments')
        );
    } else if (category === 'business') {
        // Business and finance icons
        filteredIcons = iconList.filter(icon =>
            icon.includes('briefcase') || icon.includes('building') || icon.includes('store') ||
            icon.includes('money') || icon.includes('dollar') || icon.includes('credit-card') ||
            icon.includes('chart') || icon.includes('analytics') || icon.includes('handshake') ||
            icon.includes('calculator') || icon.includes('receipt') || icon.includes('invoice') ||
            icon.includes('bank') || icon.includes('university') || icon.includes('warehouse') ||
            icon.includes('factory') || icon.includes('contract') || icon.includes('deal') ||
            icon.includes('investment') || icon.includes('profit') || icon.includes('budget')
        );
    } else if (category === 'ecommerce') {
        // E-commerce and payment icons
        filteredIcons = iconList.filter(icon =>
            icon.includes('shopping') || icon.includes('cart') || icon.includes('store') ||
            icon.includes('payment') || icon.includes('paypal') || icon.includes('stripe') ||
            icon.includes('cc-') || icon.includes('bitcoin') || icon.includes('ethereum') ||
            icon.includes('wallet') || icon.includes('cash') || icon.includes('barcode') ||
            icon.includes('qrcode') || icon.includes('price') || icon.includes('discount') ||
            icon.includes('coupon') || icon.includes('gift') || icon.includes('refund')
        );
    } else if (category === 'technology') {
        // Technology and development icons
        filteredIcons = iconList.filter(icon =>
            icon.includes('html') || icon.includes('css') || icon.includes('js') ||
            icon.includes('react') || icon.includes('vue') || icon.includes('angular') ||
            icon.includes('node') || icon.includes('php') || icon.includes('python') ||
            icon.includes('java') || icon.includes('docker') || icon.includes('aws') ||
            icon.includes('server') || icon.includes('database') || icon.includes('cloud') ||
            icon.includes('code') || icon.includes('terminal') || icon.includes('git') ||
            icon.includes('microchip') || icon.includes('network') || icon.includes('router')
        );
    } else if (category === 'education') {
        // Education and learning icons
        filteredIcons = iconList.filter(icon =>
            icon.includes('graduation') || icon.includes('school') || icon.includes('university') ||
            icon.includes('book') || icon.includes('library') || icon.includes('study') ||
            icon.includes('research') || icon.includes('diploma') || icon.includes('certificate') ||
            icon.includes('student') || icon.includes('teacher') || icon.includes('classroom') ||
            icon.includes('lecture') || icon.includes('course') || icon.includes('exam') ||
            icon.includes('blackboard') || icon.includes('presentation')
        );
    } else if (category === 'security') {
        // Security and privacy icons
        filteredIcons = iconList.filter(icon =>
            icon.includes('shield') || icon.includes('lock') || icon.includes('unlock') ||
            icon.includes('key') || icon.includes('fingerprint') || icon.includes('password') ||
            icon.includes('encryption') || icon.includes('ssl') || icon.includes('vpn') ||
            icon.includes('firewall') || icon.includes('antivirus') || icon.includes('security') ||
            icon.includes('fraud') || icon.includes('virus') || icon.includes('breach')
        );
    } else if (category === 'analytics') {
        // Analytics and reporting icons
        filteredIcons = iconList.filter(icon =>
            icon.includes('analytics') || icon.includes('chart') || icon.includes('graph') ||
            icon.includes('statistics') || icon.includes('metrics') || icon.includes('kpi') ||
            icon.includes('dashboard') || icon.includes('report') || icon.includes('insights') ||
            icon.includes('trends') || icon.includes('forecast') || icon.includes('data') ||
            icon.includes('visualization') || icon.includes('traffic') || icon.includes('conversion')
        );
    } else if (category === 'content') {
        // Content management icons
        filteredIcons = iconList.filter(icon =>
            icon.includes('content') || icon.includes('article') || icon.includes('blog') ||
            icon.includes('post') || icon.includes('page') || icon.includes('draft') ||
            icon.includes('publish') || icon.includes('editor') || icon.includes('template') ||
            icon.includes('media') || icon.includes('gallery') || icon.includes('cms') ||
            icon.includes('workflow') || icon.includes('approval') || icon.includes('revision')
        );
    } else if (category === 'products') {
        // Product and inventory icons
        filteredIcons = iconList.filter(icon =>
            icon.includes('box') || icon.includes('package') || icon.includes('product') ||
            icon.includes('inventory') || icon.includes('stock') || icon.includes('warehouse') ||
            icon.includes('catalog') || icon.includes('category') || icon.includes('variant') ||
            icon.includes('barcode') || icon.includes('qrcode') || icon.includes('sku') ||
            icon.includes('serial') || icon.includes('batch') || icon.includes('warranty')
        );
    }

    populateIcons(filteredIcons);
}

// Helper functions for enhanced menu display
function getItemType(item) {
    if (item.type === 'separator') {
        return 'separator';
    }
    if (item.is_system) {
        return 'system';
    }
    if (item.plugin) {
        return 'plugin';
    }
    if (item.route) {
        return 'internal';
    }
    if (item.url && item.target === '_blank') {
        return 'external';
    }
    if (item.children && item.children.length > 0) {
        return 'parent';
    }
    return 'custom';
}

function getTypeColors(type) {
    const colorMap = {
        'system': {
            bg: 'bg-blue-50 dark:bg-blue-900/20',
            border: 'border-blue-200 dark:border-blue-800',
            badge: 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200',
            icon: 'text-blue-600 dark:text-blue-400'
        },
        'plugin': {
            bg: 'bg-purple-50 dark:bg-purple-900/20',
            border: 'border-purple-200 dark:border-purple-800',
            badge: 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200',
            icon: 'text-purple-600 dark:text-purple-400'
        },
        'separator': {
            bg: 'bg-gray-50 dark:bg-gray-800',
            border: 'border-gray-200 dark:border-gray-700',
            badge: 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200',
            icon: 'text-gray-400 dark:text-gray-500'
        },
        'external': {
            bg: 'bg-green-50 dark:bg-green-900/20',
            border: 'border-green-200 dark:border-green-800',
            badge: 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200',
            icon: 'text-green-600 dark:text-green-400'
        },
        'parent': {
            bg: 'bg-orange-50 dark:bg-orange-900/20',
            border: 'border-orange-200 dark:border-orange-800',
            badge: 'bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200',
            icon: 'text-orange-600 dark:text-orange-400'
        },
        'internal': {
            bg: 'bg-indigo-50 dark:bg-indigo-900/20',
            border: 'border-indigo-200 dark:border-indigo-800',
            badge: 'bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200',
            icon: 'text-indigo-600 dark:text-indigo-400'
        }
    };

    return colorMap[type] || {
        bg: 'bg-gray-50 dark:bg-gray-800',
        border: 'border-gray-200 dark:border-gray-700',
        badge: 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200',
        icon: 'text-gray-600 dark:text-gray-400'
    };
}

function getTypeLabel(type) {
    const labelMap = {
        'system': 'System',
        'plugin': 'Plugin',
        'separator': 'Separator',
        'external': 'External Link',
        'parent': 'Parent Menu',
        'internal': 'Internal Link',
        'custom': 'Custom'
    };
    return labelMap[type] || 'Custom';
}

function getDefaultIcon(type) {
    const iconMap = {
        'system': 'fas fa-shield-alt',
        'plugin': 'fas fa-puzzle-piece',
        'separator': 'fas fa-minus',
        'external': 'fas fa-external-link-alt',
        'parent': 'fas fa-folder',
        'internal': 'fas fa-link',
        'custom': 'fas fa-circle'
    };
    return iconMap[type] || 'fas fa-circle';
}

function getDestinationInfo(item) {
    if (item.type === 'separator') {
        return 'Visual separator line';
    }
    if (item.route) {
        return `Route: ${item.route}`;
    }
    if (item.url) {
        if (item.target === '_blank') {
            return `External URL: ${item.url}`;
        }
        return `URL: ${item.url}`;
    }
    if (item.children && item.children.length > 0) {
        return `Parent container (${item.children.length} children)`;
    }
    return 'No destination configured';
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/navigation/Views/index.blade.php ENDPATH**/ ?>