<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <nav class="flex" aria-label="Breadcrumb">
                <ol class="flex items-center space-x-4">
                    <li>
                        <a href="<?php echo e(route('plugins.index')); ?>" class="text-gray-400 hover:text-gray-500">
                            <i class="fas fa-puzzle-piece"></i>
                            <span class="sr-only">Plugins</span>
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-gray-400 mr-4"></i>
                            <span class="text-sm font-medium text-gray-500">Install Plugin</span>
                        </div>
                    </li>
                </ol>
            </nav>
            <h1 class="text-2xl font-bold text-gray-900 mt-2">Install Plugin</h1>
            <p class="mt-1 text-sm text-gray-600">Upload and install a new plugin package</p>
        </div>
        <a href="<?php echo e(route('plugins.index')); ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
            <i class="fas fa-arrow-left mr-2"></i>
            Back to Plugins
        </a>
    </div>

    <!-- Install Form -->
    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Upload Plugin Archive</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">Select a ZIP file containing the plugin to install</p>
        </div>
        <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
            <form action="<?php echo e(route('plugins.install.process')); ?>" method="POST" enctype="multipart/form-data" class="space-y-6">
                <?php echo csrf_field(); ?>
                
                <!-- File Upload -->
                <div>
                    <label for="plugin_file" class="block text-sm font-medium text-gray-700">
                        Plugin Archive <span class="text-red-500">*</span>
                    </label>
                    <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400 transition-colors duration-200">
                        <div class="space-y-1 text-center">
                            <i class="fas fa-file-archive text-4xl text-gray-400 mb-4"></i>
                            <div class="flex text-sm text-gray-600">
                                <label for="plugin_file" class="relative cursor-pointer bg-white rounded-md font-medium text-primary-600 hover:text-primary-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500">
                                    <span>Upload a file</span>
                                    <input id="plugin_file" name="plugin_file" type="file" accept=".zip" required class="sr-only">
                                </label>
                                <p class="pl-1">or drag and drop</p>
                            </div>
                            <p class="text-xs text-gray-500">ZIP files up to 50MB</p>
                        </div>
                    </div>
                    <?php $__errorArgs = ['plugin_file'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-2 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Installation Notes -->
                <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-info-circle text-blue-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-blue-800">Installation Requirements</h3>
                            <div class="mt-2 text-sm text-blue-700">
                                <ul class="list-disc list-inside space-y-1">
                                    <li>Plugin archive must be a valid ZIP file</li>
                                    <li>Archive must contain a valid <code class="bg-blue-100 px-1 rounded">config.json</code> file</li>
                                    <li>Plugin name must be unique (not already installed)</li>
                                    <li>Maximum file size: 50MB</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Warning -->
                <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">Security Warning</h3>
                            <div class="mt-2 text-sm text-yellow-700">
                                <p>Only install plugins from trusted sources. Malicious plugins can compromise your system security.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                    <a href="<?php echo e(route('plugins.index')); ?>" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <i class="fas fa-upload mr-2"></i>
                        Install Plugin
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('plugin_file');
    const dropZone = fileInput.closest('.border-dashed');
    
    // Handle file selection
    fileInput.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            const fileName = e.target.files[0].name;
            const fileSize = (e.target.files[0].size / 1024 / 1024).toFixed(2);
            
            dropZone.innerHTML = `
                <div class="space-y-1 text-center">
                    <i class="fas fa-file-archive text-4xl text-green-500 mb-4"></i>
                    <div class="text-sm text-gray-900">
                        <p class="font-medium">${fileName}</p>
                        <p class="text-gray-500">${fileSize} MB</p>
                    </div>
                    <button type="button" onclick="clearFile()" class="text-sm text-red-600 hover:text-red-500">
                        Remove file
                    </button>
                </div>
            `;
        }
    });
    
    // Drag and drop functionality
    dropZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        dropZone.classList.add('border-primary-400', 'bg-primary-50');
    });
    
    dropZone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        dropZone.classList.remove('border-primary-400', 'bg-primary-50');
    });
    
    dropZone.addEventListener('drop', function(e) {
        e.preventDefault();
        dropZone.classList.remove('border-primary-400', 'bg-primary-50');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            fileInput.dispatchEvent(new Event('change'));
        }
    });
});

function clearFile() {
    const fileInput = document.getElementById('plugin_file');
    const dropZone = fileInput.closest('.border-dashed');
    
    fileInput.value = '';
    
    dropZone.innerHTML = `
        <div class="space-y-1 text-center">
            <i class="fas fa-file-archive text-4xl text-gray-400 mb-4"></i>
            <div class="flex text-sm text-gray-600">
                <label for="plugin_file" class="relative cursor-pointer bg-white rounded-md font-medium text-primary-600 hover:text-primary-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500">
                    <span>Upload a file</span>
                    <input id="plugin_file" name="plugin_file" type="file" accept=".zip" required class="sr-only">
                </label>
                <p class="pl-1">or drag and drop</p>
            </div>
            <p class="text-xs text-gray-500">ZIP files up to 50MB</p>
        </div>
    `;
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/resources/views/plugins/install.blade.php ENDPATH**/ ?>