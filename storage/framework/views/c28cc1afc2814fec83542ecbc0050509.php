<?php $__env->startSection('title', 'Bugs & Features Report'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Bugs & Features Report</h1>
        <div class="flex space-x-3">
            <a href="<?php echo e(route('clickup.reports.index')); ?>"
               class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-chart-bar mr-2"></i>
                All Reports
            </a>
            <a href="<?php echo e(route('clickup.dashboard')); ?>"
               class="bg-gray-600 dark:bg-gray-700 hover:bg-gray-700 dark:hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6 border border-gray-200 dark:border-gray-700">
        <form method="GET" action="<?php echo e(route('clickup.reports.bugs-features')); ?>">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="manager_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Product Manager</label>
                    <select name="manager_id" id="manager_id"
                            class="w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Managers</option>
                        <?php $__currentLoopData = $productManagers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $manager): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($manager->id); ?>" <?php echo e(request('manager_id') == $manager->id ? 'selected' : ''); ?>>
                                <?php echo e($manager->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <div>
                    <label for="date_from" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">From Date</label>
                    <input type="date" name="date_from" id="date_from" value="<?php echo e(request('date_from', now()->subMonth()->format('Y-m-d'))); ?>"
                           class="w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label for="date_to" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">To Date</label>
                    <input type="date" name="date_to" id="date_to" value="<?php echo e(request('date_to', now()->format('Y-m-d'))); ?>"
                           class="w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div class="flex items-end">
                    <button type="submit" 
                            class="w-full bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-filter mr-2"></i>
                        Apply Filters
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Overall Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-bug text-red-500 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Total Bugs</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e(number_format($bugsAndFeaturesData['overall_stats']['total_bugs_reported'])); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-green-500 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Bugs Solved</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e(number_format($bugsAndFeaturesData['overall_stats']['total_bugs_solved'])); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-star text-blue-500 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Features Requested</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e(number_format($bugsAndFeaturesData['overall_stats']['total_features_requested'])); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-rocket text-purple-500 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Features Completed</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e(number_format($bugsAndFeaturesData['overall_stats']['total_features_completed'])); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Manager Reports -->
    <div class="space-y-8">
        <?php $__empty_1 = true; $__currentLoopData = $bugsAndFeaturesData['managers']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $managerData): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex justify-between items-center">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($managerData['manager']['name']); ?></h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                <?php echo e($managerData['manager']['role']); ?> • <?php echo e($managerData['manager']['department']); ?> • <?php echo e($managerData['lists_count']); ?> Lists
                            </p>
                        </div>
                        <div class="flex space-x-4 text-sm">
                            <div class="text-center">
                                <div class="text-lg font-bold text-red-600 dark:text-red-400"><?php echo e($managerData['summary']['resolution_rate']); ?>%</div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">Bug Resolution</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-bold text-blue-600 dark:text-blue-400"><?php echo e($managerData['summary']['completion_rate']); ?>%</div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">Feature Completion</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Bugs Section -->
                        <div>
                            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                                <i class="fas fa-bug text-red-500 mr-2"></i>
                                Bug Reports
                            </h4>
                            <div class="grid grid-cols-2 gap-4 mb-4">
                                <div class="bg-red-50 dark:bg-red-900 p-3 rounded-lg">
                                    <div class="text-lg font-bold text-red-600 dark:text-red-400"><?php echo e($managerData['bugs']['total_reported']); ?></div>
                                    <div class="text-xs text-red-500 dark:text-red-300">Total Reported</div>
                                </div>
                                <div class="bg-green-50 dark:bg-green-900 p-3 rounded-lg">
                                    <div class="text-lg font-bold text-green-600 dark:text-green-400"><?php echo e($managerData['bugs']['total_solved']); ?></div>
                                    <div class="text-xs text-green-500 dark:text-green-300">Solved</div>
                                </div>
                                <div class="bg-yellow-50 dark:bg-yellow-900 p-3 rounded-lg">
                                    <div class="text-lg font-bold text-yellow-600 dark:text-yellow-400"><?php echo e($managerData['bugs']['in_progress']); ?></div>
                                    <div class="text-xs text-yellow-500 dark:text-yellow-300">In Progress</div>
                                </div>
                                <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                                    <div class="text-lg font-bold text-gray-600 dark:text-gray-400"><?php echo e($managerData['bugs']['pending']); ?></div>
                                    <div class="text-xs text-gray-500 dark:text-gray-300">Pending</div>
                                </div>
                            </div>
                            <?php if($managerData['bugs']['average_resolution_time']): ?>
                                <div class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                                    <i class="fas fa-clock mr-1"></i>
                                    Avg. Resolution Time: <?php echo e($managerData['bugs']['average_resolution_time']); ?> hours
                                </div>
                            <?php endif; ?>
                            <?php if(count($managerData['bugs']['recent_bugs']) > 0): ?>
                                <div>
                                    <h5 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Recent Bugs</h5>
                                    <div class="space-y-2">
                                        <?php $__currentLoopData = $managerData['bugs']['recent_bugs']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $bug): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded">
                                                <div class="flex-1 min-w-0">
                                                    <div class="text-sm font-medium text-gray-900 dark:text-white truncate"><?php echo e($bug['name']); ?></div>
                                                    <div class="text-xs text-gray-500 dark:text-gray-400">
                                                        <?php echo e(ucfirst($bug['status'])); ?> • <?php echo e(ucfirst($bug['priority']) ?? 'Normal'); ?>

                                                    </div>
                                                </div>
                                                <?php if($bug['url']): ?>
                                                    <a href="<?php echo e($bug['url']); ?>" target="_blank" class="text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300">
                                                        <i class="fas fa-external-link-alt"></i>
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Features Section -->
                        <div>
                            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                                <i class="fas fa-star text-blue-500 mr-2"></i>
                                Feature Requests
                            </h4>
                            <div class="grid grid-cols-2 gap-4 mb-4">
                                <div class="bg-blue-50 dark:bg-blue-900 p-3 rounded-lg">
                                    <div class="text-lg font-bold text-blue-600 dark:text-blue-400"><?php echo e($managerData['features']['total_requested']); ?></div>
                                    <div class="text-xs text-blue-500 dark:text-blue-300">Total Requested</div>
                                </div>
                                <div class="bg-green-50 dark:bg-green-900 p-3 rounded-lg">
                                    <div class="text-lg font-bold text-green-600 dark:text-green-400"><?php echo e($managerData['features']['total_completed']); ?></div>
                                    <div class="text-xs text-green-500 dark:text-green-300">Completed</div>
                                </div>
                                <div class="bg-purple-50 dark:bg-purple-900 p-3 rounded-lg">
                                    <div class="text-lg font-bold text-purple-600 dark:text-purple-400"><?php echo e($managerData['features']['in_development']); ?></div>
                                    <div class="text-xs text-purple-500 dark:text-purple-300">In Development</div>
                                </div>
                                <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                                    <div class="text-lg font-bold text-gray-600 dark:text-gray-400"><?php echo e($managerData['features']['pending_approval']); ?></div>
                                    <div class="text-xs text-gray-500 dark:text-gray-300">Pending</div>
                                </div>
                            </div>
                            <?php if($managerData['features']['average_development_time']): ?>
                                <div class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                                    <i class="fas fa-clock mr-1"></i>
                                    Avg. Development Time: <?php echo e($managerData['features']['average_development_time']); ?> hours
                                </div>
                            <?php endif; ?>
                            <?php if(count($managerData['features']['recent_features']) > 0): ?>
                                <div>
                                    <h5 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Recent Features</h5>
                                    <div class="space-y-2">
                                        <?php $__currentLoopData = $managerData['features']['recent_features']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded">
                                                <div class="flex-1 min-w-0">
                                                    <div class="text-sm font-medium text-gray-900 dark:text-white truncate"><?php echo e($feature['name']); ?></div>
                                                    <div class="text-xs text-gray-500 dark:text-gray-400">
                                                        <?php echo e(ucfirst($feature['status'])); ?> • <?php echo e(ucfirst($feature['priority']) ?? 'Normal'); ?>

                                                    </div>
                                                </div>
                                                <?php if($feature['url']): ?>
                                                    <a href="<?php echo e($feature['url']); ?>" target="_blank" class="text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300">
                                                        <i class="fas fa-external-link-alt"></i>
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="text-center py-12">
                <i class="fas fa-chart-bar text-gray-400 text-6xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Data Available</h3>
                <p class="text-gray-500 dark:text-gray-400">No product managers with lists found for the selected criteria.</p>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/clickup/Views/reports/bugs-features.blade.php ENDPATH**/ ?>