<?php $__env->startSection('title', 'All Products'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="max-w-6xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">All Products</h1>
                <p class="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">Browse all available products in the system</p>
            </div>
            <?php if(auth()->user()->hasPermission('manage_products')): ?>
                <a href="<?php echo e(route('products.create')); ?>" 
                   class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-plus mr-2"></i>Add Product
                </a>
            <?php endif; ?>
        </div>

        <?php if($products->count() > 0): ?>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:shadow-lg transition-shadow transition duration-150 ease-in-out">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center">
                                <?php if($product->icon): ?>
                                    <i class="<?php echo e($product->icon); ?> text-2xl text-blue-500 mr-3"></i>
                                <?php else: ?>
                                    <i class="fas fa-box text-2xl text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mr-3"></i>
                                <?php endif; ?>
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($product->name); ?></h3>
                                    <p class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500"><?php echo e($product->slug); ?></p>
                                </div>
                            </div>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($product->is_active ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'); ?> transition duration-150 ease-in-out">
                                <?php echo e($product->is_active ? 'Active' : 'Inactive'); ?>

                            </span>
                        </div>

                        <?php if($product->description): ?>
                            <p class="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mb-4 line-clamp-3"><?php echo e($product->description); ?></p>
                        <?php endif; ?>

                        <?php if($product->target_audience): ?>
                            <div class="mb-4">
                                <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-1">Target Audience</h4>
                                <p class="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 line-clamp-2"><?php echo e($product->target_audience); ?></p>
                            </div>
                        <?php endif; ?>

                        <?php if($product->use_cases): ?>
                            <div class="mb-4">
                                <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-1">Use Cases</h4>
                                <p class="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 line-clamp-2"><?php echo e($product->use_cases); ?></p>
                            </div>
                        <?php endif; ?>

                        <!-- Pricing Preview -->
                        <?php if($product->pricingItems && $product->pricingItems->count() > 0): ?>
                            <div class="mb-4">
                                <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Pricing</h4>
                                <div class="space-y-2">
                                    <?php $__currentLoopData = $product->pricingItems->take(2); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pricing): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="flex justify-between items-center text-sm">
                                            <span class="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500"><?php echo e($pricing->name); ?></span>
                                            <span class="font-medium text-gray-900 dark:text-white"><?php echo e($pricing->formatted_price); ?></span>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($product->pricingItems->count() > 2): ?>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">+<?php echo e($product->pricingItems->count() - 2); ?> more pricing options</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Reference Links -->
                        <?php if($product->reference_links && count($product->reference_links) > 0): ?>
                            <div class="mb-4">
                                <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Resources</h4>
                                <div class="space-y-1">
                                    <?php $__currentLoopData = array_slice($product->reference_links, 0, 2); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $link): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <a href="<?php echo e($link); ?>" target="_blank" 
                                           class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 block truncate">
                                            <?php echo e(parse_url($link, PHP_URL_HOST)); ?> <i class="fas fa-external-link-alt ml-1"></i>
                                        </a>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php if(count($product->reference_links) > 2): ?>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">+<?php echo e(count($product->reference_links) - 2); ?> more links</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Statistics -->
                        <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mb-4">
                            <span><i class="fas fa-building mr-1"></i><?php echo e($product->businesses_count); ?> businesses</span>
                            <?php if($product->latest_release): ?>
                                <span><i class="fas fa-tag mr-1"></i><?php echo e($product->latest_release->formatted_version); ?></span>
                            <?php endif; ?>
                        </div>

                        <!-- Actions -->
                        <div class="flex justify-between items-center">
                            <a href="<?php echo e(route('products.show', $product)); ?>" 
                               class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 text-sm font-medium">
                                View Details
                            </a>
                            <div class="flex space-x-2">
                                <?php if(auth()->user()->hasPermission('view_products')): ?>
                                    <a href="<?php echo e(route('products.show', $product)); ?>" 
                                       class="bg-blue-100 dark:bg-blue-900 hover:bg-blue-200 text-blue-800 dark:text-blue-200 text-xs font-medium py-1 px-2 rounded transition duration-150 ease-in-out">
                                        View
                                    </a>
                                <?php endif; ?>
                                <?php if(auth()->user()->hasPermission('manage_products')): ?>
                                    <a href="<?php echo e(route('products.edit', $product)); ?>" 
                                       class="bg-indigo-100 hover:bg-indigo-200 text-indigo-800 text-xs font-medium py-1 px-2 rounded transition duration-150 ease-in-out">
                                        Edit
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php else: ?>
            <div class="text-center py-12">
                <i class="fas fa-box text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 text-6xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No products available</h3>
                <p class="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mb-6">There are no products in the system yet.</p>
                <?php if(auth()->user()->hasPermission('manage_products')): ?>
                    <a href="<?php echo e(route('products.create')); ?>" 
                       class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-plus mr-2"></i>Create First Product
                    </a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/products/Views/all-products.blade.php ENDPATH**/ ?>