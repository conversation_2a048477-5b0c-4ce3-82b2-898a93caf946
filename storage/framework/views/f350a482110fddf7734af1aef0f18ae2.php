<?php $__env->startSection('title', 'Edit Document'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="max-w-2xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Edit Document</h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">Update document information for <?php echo e($business->name); ?></p>
            </div>
            <div class="flex space-x-3">
                <a href="<?php echo e(route('business.documents.index', $business)); ?>" 
                   class="bg-gray-500 dark:bg-gray-700 hover:bg-gray-600 dark:hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Back to Documents
                </a>
            </div>
        </div>

        <!-- Document Info Card -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700 mb-6">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Document Information</h3>
            </div>
            <div class="px-6 py-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="<?php echo e($document->file_icon); ?> text-3xl text-gray-600 dark:text-gray-400 mr-4"></i>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($document->original_name); ?></h4>
                        <div class="mt-2 text-sm text-gray-500 dark:text-gray-400 space-y-1">
                            <div class="flex justify-between">
                                <span>File Size:</span>
                                <span><?php echo e($document->formatted_file_size); ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span>Uploaded:</span>
                                <span><?php echo e($document->upload_date->format('M d, Y')); ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span>Uploaded By:</span>
                                <span><?php echo e($document->uploader->name ?? 'Unknown'); ?></span>
                            </div>
                        </div>
                    </div>
                    <div class="flex space-x-2 ml-4">
                        <?php if($document->isImage() || $document->isPdf()): ?>
                            <a href="<?php echo e(route('business.documents.view', [$business, $document])); ?>" 
                               target="_blank"
                               class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">
                                <i class="fas fa-eye"></i>
                            </a>
                        <?php endif; ?>
                        <a href="<?php echo e(route('business.documents.download', [$business, $document])); ?>" 
                           class="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300">
                            <i class="fas fa-download"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Edit Form -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Edit Document Details</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">You can update the document type and description. The file itself cannot be changed.</p>
            </div>
            <div class="px-6 py-4">
                <form method="POST" action="<?php echo e(route('business.documents.update', [$business, $document])); ?>">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>

                    <!-- Document Type -->
                    <div class="mb-6">
                        <label for="document_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Document Type <span class="text-red-500">*</span>
                        </label>
                        <select name="document_type" id="document_type" required
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                            <option value="">Select document type</option>
                            <?php $__currentLoopData = $documentTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($key); ?>" <?php echo e(old('document_type', $document->document_type) === $key ? 'selected' : ''); ?>>
                                    <?php echo e($label); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <?php $__errorArgs = ['document_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Description -->
                    <div class="mb-6">
                        <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Description
                        </label>
                        <textarea name="description" id="description" rows="4" 
                                  placeholder="Optional description for this document..."
                                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"><?php echo e(old('description', $document->description)); ?></textarea>
                        <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Maximum 500 characters</p>
                    </div>

                    <!-- Form Actions -->
                    <div class="flex justify-end space-x-3">
                        <a href="<?php echo e(route('business.documents.index', $business)); ?>" 
                           class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                            <i class="fas fa-save mr-2"></i>
                            Update Document
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/business/Views/documents/edit.blade.php ENDPATH**/ ?>