<?php $__env->startSection('title', $release->formatted_version . ' - ' . $release->title); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="flex justify-between items-start mb-6">
            <div class="flex items-center">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium <?php echo e($release->release_type_badge_class); ?> mr-4">
                    <?php echo e($release->release_type_label); ?>

                </span>
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white"><?php echo e($release->formatted_version); ?></h1>
                    <h2 class="text-xl text-gray-700 dark:text-gray-300 mt-1"><?php echo e($release->title); ?></h2>
                    <p class="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1"><?php echo e($product->name); ?></p>
                    <div class="flex items-center mt-2 space-x-4">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($release->is_published ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'); ?> transition duration-150 ease-in-out">
                            <?php echo e($release->is_published ? 'Published' : 'Draft'); ?>

                        </span>
                        <?php if($release->release_date): ?>
                            <span class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                                Released <?php echo e($release->release_date->format('M d, Y')); ?>

                            </span>
                        <?php endif; ?>
                        <span class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                            by <?php echo e($release->creator->name); ?>

                        </span>
                    </div>
                </div>
            </div>
            <div class="flex space-x-3">
                <a href="<?php echo e(route('products.releases.index', $product)); ?>" 
                   class="bg-gray-50 dark:bg-gray-7000 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Back to Releases
                </a>
                <?php if(auth()->user()->hasPermission('manage_product_releases')): ?>
                    <a href="<?php echo e(route('products.releases.edit', [$product, $release])); ?>" 
                       class="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-edit mr-2"></i>Edit
                    </a>
                    <?php if(!$release->is_published): ?>
                        <form method="POST" action="<?php echo e(route('products.releases.publish', [$product, $release])); ?>" 
                              class="inline">
                            <?php echo csrf_field(); ?>
                            <button type="submit" 
                                    class="bg-green-500 dark:bg-green-600 hover:bg-green-700 dark:hover:bg-green-500 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out"
                                    onclick="return confirm('Are you sure you want to publish this release?')">
                                <i class="fas fa-rocket mr-2"></i>Publish
                            </button>
                        </form>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Release Description -->
                <?php if($release->description): ?>
                    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                        <div class="px-4 py-5 sm:px-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Release Notes</h3>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">What's new in this release</p>
                        </div>
                        <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                            <div class="prose max-w-none">
                                <?php echo nl2br(e($release->description)); ?>

                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Changelog -->
                <?php if($release->hasChanges()): ?>
                    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                        <div class="px-4 py-5 sm:px-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Changelog</h3>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Detailed list of changes in this release</p>
                        </div>
                        <div class="border-t border-gray-200 dark:border-gray-700">
                            <?php if($release->new_features && count($release->new_features_array) > 0): ?>
                                <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                                    <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3 flex items-center">
                                        <i class="fas fa-plus-circle text-green-500 mr-2"></i>
                                        New Features
                                    </h4>
                                    <ul class="space-y-2">
                                        <?php $__currentLoopData = $release->new_features_array; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li class="flex items-start">
                                                <i class="fas fa-check text-green-500 mt-1 mr-2 text-sm"></i>
                                                <span class="text-sm text-gray-700 dark:text-gray-300"><?php echo e($feature); ?></span>
                                            </li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <?php if($release->improvements && count($release->improvements_array) > 0): ?>
                                <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                                    <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3 flex items-center">
                                        <i class="fas fa-arrow-up text-blue-500 mr-2"></i>
                                        Improvements
                                    </h4>
                                    <ul class="space-y-2">
                                        <?php $__currentLoopData = $release->improvements_array; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $improvement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li class="flex items-start">
                                                <i class="fas fa-arrow-right text-blue-500 mt-1 mr-2 text-sm"></i>
                                                <span class="text-sm text-gray-700 dark:text-gray-300"><?php echo e($improvement); ?></span>
                                            </li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <?php if($release->bug_fixes && count($release->bug_fixes_array) > 0): ?>
                                <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                                    <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3 flex items-center">
                                        <i class="fas fa-bug text-red-500 dark:text-red-400 mr-2"></i>
                                        Bug Fixes
                                    </h4>
                                    <ul class="space-y-2">
                                        <?php $__currentLoopData = $release->bug_fixes_array; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $fix): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li class="flex items-start">
                                                <i class="fas fa-times text-red-500 dark:text-red-400 mt-1 mr-2 text-sm"></i>
                                                <span class="text-sm text-gray-700 dark:text-gray-300"><?php echo e($fix); ?></span>
                                            </li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <?php if($release->breaking_changes && count($release->breaking_changes_array) > 0): ?>
                                <div class="px-4 py-5 sm:px-6">
                                    <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3 flex items-center">
                                        <i class="fas fa-exclamation-triangle text-yellow-500 mr-2"></i>
                                        Breaking Changes
                                    </h4>
                                    <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4 transition duration-150 ease-in-out">
                                        <ul class="space-y-2">
                                            <?php $__currentLoopData = $release->breaking_changes_array; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $change): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <li class="flex items-start">
                                                    <i class="fas fa-exclamation text-yellow-600 mt-1 mr-2 text-sm"></i>
                                                    <span class="text-sm text-gray-700 dark:text-gray-300"><?php echo e($change); ?></span>
                                                </li>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </ul>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Migration Notes -->
                <?php if($release->migration_notes): ?>
                    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                        <div class="px-4 py-5 sm:px-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Migration Notes</h3>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Important information for upgrading</p>
                        </div>
                        <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                            <div class="bg-blue-50 border border-blue-200 rounded-md p-4 transition duration-150 ease-in-out">
                                <div class="prose max-w-none text-sm">
                                    <?php echo nl2br(e($release->migration_notes)); ?>

                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Release Information -->
                <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                    <div class="px-4 py-5 sm:px-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Release Information</h3>
                    </div>
                    <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                        <dl class="space-y-4">
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Version</dt>
                                <dd class="text-lg font-semibold text-gray-900 dark:text-white"><?php echo e($release->formatted_version); ?></dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Release Type</dt>
                                <dd class="text-sm text-gray-900 dark:text-white"><?php echo e($release->release_type_label); ?></dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Status</dt>
                                <dd>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($release->is_published ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'); ?> transition duration-150 ease-in-out">
                                        <?php echo e($release->is_published ? 'Published' : 'Draft'); ?>

                                    </span>
                                </dd>
                            </div>
                            <?php if($release->release_date): ?>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Release Date</dt>
                                    <dd class="text-sm text-gray-900 dark:text-white"><?php echo e($release->release_date->format('M d, Y \a\t g:i A')); ?></dd>
                                </div>
                            <?php endif; ?>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Created By</dt>
                                <dd class="text-sm text-gray-900 dark:text-white"><?php echo e($release->creator->name); ?></dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Created</dt>
                                <dd class="text-sm text-gray-900 dark:text-white"><?php echo e($release->created_at->format('M d, Y \a\t g:i A')); ?></dd>
                            </div>
                            <?php if($release->updated_at != $release->created_at): ?>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Last Updated</dt>
                                    <dd class="text-sm text-gray-900 dark:text-white"><?php echo e($release->updated_at->format('M d, Y \a\t g:i A')); ?></dd>
                                </div>
                            <?php endif; ?>
                        </dl>
                    </div>
                </div>

                <!-- Release Statistics -->
                <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                    <div class="px-4 py-5 sm:px-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Statistics</h3>
                    </div>
                    <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                        <dl class="space-y-4">
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Total Changes</dt>
                                <dd class="text-lg font-semibold text-gray-900 dark:text-white"><?php echo e($release->total_changes_count); ?></dd>
                            </div>
                            <?php if($release->new_features_count > 0): ?>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">New Features</dt>
                                    <dd class="text-sm text-gray-900 dark:text-white"><?php echo e($release->new_features_count); ?></dd>
                                </div>
                            <?php endif; ?>
                            <?php if($release->improvements_count > 0): ?>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Improvements</dt>
                                    <dd class="text-sm text-gray-900 dark:text-white"><?php echo e($release->improvements_count); ?></dd>
                                </div>
                            <?php endif; ?>
                            <?php if($release->bug_fixes_count > 0): ?>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Bug Fixes</dt>
                                    <dd class="text-sm text-gray-900 dark:text-white"><?php echo e($release->bug_fixes_count); ?></dd>
                                </div>
                            <?php endif; ?>
                            <?php if($release->breaking_changes_count > 0): ?>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Breaking Changes</dt>
                                    <dd class="text-sm text-red-600 dark:text-red-400 font-medium"><?php echo e($release->breaking_changes_count); ?></dd>
                                </div>
                            <?php endif; ?>
                        </dl>
                    </div>
                </div>

                <!-- Navigation -->
                <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                    <div class="px-4 py-5 sm:px-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Navigation</h3>
                    </div>
                    <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                        <div class="space-y-3">
                            <?php if($previousRelease): ?>
                                <a href="<?php echo e(route('products.releases.show', [$product, $previousRelease])); ?>" 
                                   class="flex items-center text-sm text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300">
                                    <i class="fas fa-chevron-left mr-2"></i>
                                    Previous: <?php echo e($previousRelease->formatted_version); ?>

                                </a>
                            <?php endif; ?>
                            <?php if($nextRelease): ?>
                                <a href="<?php echo e(route('products.releases.show', [$product, $nextRelease])); ?>" 
                                   class="flex items-center text-sm text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300">
                                    <i class="fas fa-chevron-right mr-2"></i>
                                    Next: <?php echo e($nextRelease->formatted_version); ?>

                                </a>
                            <?php endif; ?>
                            <a href="<?php echo e(route('products.show', $product)); ?>" 
                               class="flex items-center text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 hover:text-gray-900 dark:text-white">
                                <i class="fas fa-box mr-2"></i>
                                Back to Product
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/products/Views/releases/show.blade.php ENDPATH**/ ?>