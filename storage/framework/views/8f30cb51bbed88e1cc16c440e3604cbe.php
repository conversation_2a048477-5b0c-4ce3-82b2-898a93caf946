<?php $__env->startSection('title', 'Edit Product - ' . $product->name); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Edit Product</h1>
            <div class="flex space-x-3">
                <a href="<?php echo e(route('products.show', $product)); ?>" 
                   class="bg-gray-50 dark:bg-gray-7000 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Back to Product
                </a>
                <a href="<?php echo e(route('products.index')); ?>" 
                   class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-7000 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    All Products
                </a>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
            <form method="POST" action="<?php echo e(route('products.update', $product)); ?>" class="p-6">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Basic Information -->
                    <div class="md:col-span-2">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Basic Information</h3>
                    </div>

                    <!-- Product Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Product Name <span class="text-red-500 dark:text-red-400">*</span>
                        </label>
                        <input type="text" name="name" id="name" value="<?php echo e(old('name', $product->name)); ?>" required
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 dark:border-red-600 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">The main product name used for identification</p>
                    </div>

                    <!-- Slug -->
                    <div>
                        <label for="slug" class="block text-sm font-medium text-gray-700 dark:text-gray-300">URL Slug</label>
                        <input type="text" name="slug" id="slug" value="<?php echo e(old('slug', $product->slug)); ?>"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 <?php $__errorArgs = ['slug'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 dark:border-red-600 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                        <?php $__errorArgs = ['slug'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">URL-friendly identifier for this product</p>
                    </div>

                    <!-- Description -->
                    <div class="md:col-span-2">
                        <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description</label>
                        <textarea name="description" id="description" rows="4"
                                  class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 dark:border-red-600 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?> dark:bg-gray-700 dark:text-white"><?php echo e(old('description', $product->description)); ?></textarea>
                        <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Detailed description of the product</p>
                    </div>

                    <!-- Icon -->
                    <div class="md:col-span-2">
                        <label for="icon" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Product Icon</label>
                        <select name="icon" id="icon"
                                class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 <?php $__errorArgs = ['icon'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 dark:border-red-600 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?> dark:bg-gray-700 dark:text-white">
                            <option value="">Select an icon...</option>
                            <?php $__currentLoopData = $icons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $iconClass => $iconLabel): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($iconClass); ?>" <?php echo e(old('icon', $product->icon) === $iconClass ? 'selected' : ''); ?>>
                                    <?php echo e($iconLabel); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <?php $__errorArgs = ['icon'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Choose an icon to represent this product</p>
                    </div>

                    <!-- Target Audience -->
                    <div>
                        <label for="target_audience" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Target Audience</label>
                        <textarea name="target_audience" id="target_audience" rows="3"
                                  class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 <?php $__errorArgs = ['target_audience'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 dark:border-red-600 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?> dark:bg-gray-700 dark:text-white"><?php echo e(old('target_audience', $product->target_audience)); ?></textarea>
                        <?php $__errorArgs = ['target_audience'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Who is this product designed for?</p>
                    </div>

                    <!-- Use Cases -->
                    <div>
                        <label for="use_cases" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Use Cases</label>
                        <textarea name="use_cases" id="use_cases" rows="3"
                                  class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 <?php $__errorArgs = ['use_cases'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 dark:border-red-600 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?> dark:bg-gray-700 dark:text-white"><?php echo e(old('use_cases', $product->use_cases)); ?></textarea>
                        <?php $__errorArgs = ['use_cases'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">What problems does this product solve?</p>
                    </div>

                    <!-- Reference Links -->
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Reference Links</label>
                        <div id="reference-links-container">
                            <?php
                                $referenceLinks = old('reference_links', $product->reference_links ?? []);
                                if (empty($referenceLinks)) {
                                    $referenceLinks = [''];
                                }
                            ?>
                            <?php $__currentLoopData = $referenceLinks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $link): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="flex mb-2 reference-link-row">
                                    <input type="url" name="reference_links[]" value="<?php echo e($link); ?>"
                                           placeholder="https://example.com"
                                           class="flex-1 border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:bg-gray-700 dark:text-white">
                                    <button type="button" onclick="removeReferenceLink(this)"
                                            class="ml-2 bg-red-500 dark:bg-red-600 hover:bg-red-700 dark:hover:bg-red-500 text-white font-bold py-2 px-3 rounded transition duration-150 ease-in-out">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        <button type="button" onclick="addReferenceLink()"
                                class="mt-2 bg-green-500 dark:bg-green-600 hover:bg-green-700 dark:hover:bg-green-500 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                            <i class="fas fa-plus mr-2"></i>Add Reference Link
                        </button>
                        <?php $__errorArgs = ['reference_links.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">External links related to this product (documentation, website, etc.)</p>
                    </div>

                    <!-- Status -->
                    <div class="md:col-span-2">
                        <div class="flex items-center">
                            <input type="checkbox" name="is_active" id="is_active" value="1" 
                                   <?php echo e(old('is_active', $product->is_active) ? 'checked' : ''); ?>

                                   class="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 border-gray-300 dark:border-gray-600 rounded">
                            <label for="is_active" class="ml-2 block text-sm text-gray-900 dark:text-white">
                                Product is active
                            </label>
                        </div>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Active products are available for assignment to businesses</p>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="mt-8 flex justify-end space-x-3">
                    <a href="<?php echo e(route('products.show', $product)); ?>" 
                       class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-7000 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Update Product
                    </button>
                </div>
            </form>
        </div>

        <!-- Danger Zone -->
        <?php if(auth()->user()->hasPermission('manage_products')): ?>
            <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg mt-6 transition duration-150 ease-in-out">
                <div class="px-4 py-5 sm:px-6">
                    <h3 class="text-lg leading-6 font-medium text-red-900">Danger Zone</h3>
                    <p class="mt-1 max-w-2xl text-sm text-red-600 dark:text-red-400">Irreversible and destructive actions</p>
                </div>
                <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:px-6">
                    <div class="flex justify-between items-center">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white">Delete Product</h4>
                            <p class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                                Once you delete a product, there is no going back. Please be certain.
                                <?php if($product->businesses()->exists()): ?>
                                    <br><span class="text-red-600 dark:text-red-400 font-medium">This product is assigned to <?php echo e($product->businesses()->count()); ?> business(es) and cannot be deleted.</span>
                                <?php endif; ?>
                            </p>
                        </div>
                        <?php if(!$product->businesses()->exists()): ?>
                            <form method="POST" action="<?php echo e(route('products.destroy', $product)); ?>" 
                                  onsubmit="return confirm('Are you sure you want to delete this product? This action cannot be undone.')">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" 
                                        class="bg-red-600 hover:bg-red-700 dark:hover:bg-red-500 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                                    Delete Product
                                </button>
                            </form>
                        <?php else: ?>
                            <button type="button" disabled
                                    class="bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 font-bold py-2 px-4 rounded cursor-not-allowed transition duration-150 ease-in-out">
                                Cannot Delete
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
function addReferenceLink() {
    const container = document.getElementById('reference-links-container');
    const newRow = document.createElement('div');
    newRow.className = 'flex mb-2 reference-link-row';
    newRow.innerHTML = `
        <input type="url" name="reference_links[]" value=""
               placeholder="https://example.com"
               class="flex-1 border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:bg-gray-700 dark:text-white">
        <button type="button" onclick="removeReferenceLink(this)"
                class="ml-2 bg-red-500 dark:bg-red-600 hover:bg-red-700 dark:hover:bg-red-500 text-white font-bold py-2 px-3 rounded transition duration-150 ease-in-out">
            <i class="fas fa-trash"></i>
        </button>
    `;
    container.appendChild(newRow);
}

function removeReferenceLink(button) {
    const container = document.getElementById('reference-links-container');
    const rows = container.querySelectorAll('.reference-link-row');
    
    // Keep at least one row
    if (rows.length > 1) {
        button.closest('.reference-link-row').remove();
    } else {
        // Clear the input instead of removing the row
        button.closest('.reference-link-row').querySelector('input').value = '';
    }
}

// Auto-generate slug from name if slug field is empty
document.getElementById('name').addEventListener('input', function() {
    const slugField = document.getElementById('slug');
    if (!slugField.value || slugField.dataset.autoGenerated !== 'false') {
        const slug = this.value
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/^-+|-+$/g, '');
        slugField.value = slug;
        slugField.dataset.autoGenerated = 'true';
    }
});

document.getElementById('slug').addEventListener('input', function() {
    this.dataset.autoGenerated = 'false';
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/products/Views/edit.blade.php ENDPATH**/ ?>