<?php $__env->startSection('title', 'Documents - ' . $product->name); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Product Documents</h1>
                <p class="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1"><?php echo e($product->name); ?></p>
            </div>
            <div class="flex space-x-3">
                <a href="<?php echo e(route('products.show', $product)); ?>" 
                   class="bg-gray-50 dark:bg-gray-7000 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Back to Product
                </a>
                <?php if(auth()->user()->hasPermission('manage_product_documents')): ?>
                    <a href="<?php echo e(route('products.documents.create', $product)); ?>" 
                       class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-plus mr-2"></i>Upload Document
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-file text-blue-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Total Documents</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($stats['total']); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Current Versions</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($stats['current_versions']); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $categoryKey => $categoryLabel): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php if(isset($stats['by_category'][$categoryKey])): ?>
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-folder text-purple-500 text-2xl"></i>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate"><?php echo e($categoryLabel); ?></dt>
                                        <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($stats['by_category'][$categoryKey]); ?></dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Filters and Search -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 transition duration-150 ease-in-out">
            <div class="p-6">
                <form method="GET" action="<?php echo e(route('products.documents.index', $product)); ?>" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <!-- Search -->
                        <div>
                            <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Search</label>
                            <input type="text" name="search" id="search" value="<?php echo e(request('search')); ?>"
                                   placeholder="Search documents..."
                                   class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:bg-gray-700 dark:text-white">
                        </div>

                        <!-- Category Filter -->
                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Category</label>
                            <select name="category" id="category"
                                    class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:bg-gray-700 dark:text-white">
                                <option value="">All Categories</option>
                                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $categoryKey => $categoryLabel): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($categoryKey); ?>" <?php echo e(request('category') === $categoryKey ? 'selected' : ''); ?>>
                                        <?php echo e($categoryLabel); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>

                        <!-- Version Filter -->
                        <div>
                            <label for="current_only" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Versions</label>
                            <select name="current_only" id="current_only"
                                    class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:bg-gray-700 dark:text-white">
                                <option value="1" <?php echo e(request('current_only', '1') === '1' ? 'selected' : ''); ?>>Current Versions Only</option>
                                <option value="0" <?php echo e(request('current_only') === '0' ? 'selected' : ''); ?>>All Versions</option>
                            </select>
                        </div>

                        <!-- Sort By -->
                        <div>
                            <label for="sort" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Sort By</label>
                            <select name="sort" id="sort"
                                    class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:bg-gray-700 dark:text-white">
                                <option value="upload_date" <?php echo e(request('sort') === 'upload_date' ? 'selected' : ''); ?>>Upload Date</option>
                                <option value="name" <?php echo e(request('sort') === 'name' ? 'selected' : ''); ?>>Name</option>
                                <option value="category" <?php echo e(request('sort') === 'category' ? 'selected' : ''); ?>>Category</option>
                                <option value="version" <?php echo e(request('sort') === 'version' ? 'selected' : ''); ?>>Version</option>
                            </select>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-3">
                        <a href="<?php echo e(route('products.documents.index', $product)); ?>" 
                           class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-7000 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                            Clear Filters
                        </a>
                        <button type="submit" 
                                class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                            Apply Filters
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Documents List -->
        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
            <?php if($documents->count() > 0): ?>
                <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                    <?php $__currentLoopData = $documents; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $document): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li class="px-6 py-4">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="<?php echo e($document->file_icon); ?> text-2xl mr-4"></i>
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                                            <a href="<?php echo e(route('products.documents.show', [$product, $document])); ?>" 
                                               class="hover:text-blue-600 dark:text-blue-400">
                                                <?php echo e($document->name); ?>

                                            </a>
                                        </h3>
                                        <div class="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 transition duration-150 ease-in-out">
                                                <?php echo e($document->category_label); ?>

                                            </span>
                                            <span><?php echo e($document->formatted_file_size); ?></span>
                                            <span>v<?php echo e($document->version); ?></span>
                                            <?php if($document->is_current_version): ?>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 transition duration-150 ease-in-out">
                                                    Current
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                        <?php if($document->description): ?>
                                            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500"><?php echo e($document->description); ?></p>
                                        <?php endif; ?>
                                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                                            Uploaded by <?php echo e($document->uploader->name); ?> on <?php echo e($document->upload_date->format('M d, Y')); ?>

                                        </p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <?php if(auth()->user()->hasPermission('view_product_documents')): ?>
                                        <?php if($document->isViewableInBrowser()): ?>
                                            <a href="<?php echo e(route('products.documents.view', [$product, $document])); ?>" 
                                               target="_blank"
                                               class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 text-sm font-medium">
                                                View
                                            </a>
                                        <?php endif; ?>
                                        <a href="<?php echo e(route('products.documents.download', [$product, $document])); ?>" 
                                           class="text-green-600 dark:text-green-400 hover:text-green-900 text-sm font-medium">
                                            Download
                                        </a>
                                    <?php endif; ?>
                                    <?php if(auth()->user()->hasPermission('manage_product_documents')): ?>
                                        <a href="<?php echo e(route('products.documents.edit', [$product, $document])); ?>" 
                                           class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 text-sm font-medium">
                                            Edit
                                        </a>
                                        <form method="POST" action="<?php echo e(route('products.documents.destroy', [$product, $document])); ?>" 
                                              class="inline"
                                              onsubmit="return confirm('Are you sure you want to delete this document?')">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" 
                                                    class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 text-sm font-medium">
                                                Delete
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>

                <!-- Pagination -->
                <div class="bg-white dark:bg-gray-800 px-4 py-3 border-t border-gray-200 dark:border-gray-700 sm:px-6 transition duration-150 ease-in-out">
                    <?php echo e($documents->links()); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-12">
                    <i class="fas fa-file text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 text-6xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No documents found</h3>
                    <p class="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mb-6">Upload your first product document to get started.</p>
                    <?php if(auth()->user()->hasPermission('manage_product_documents')): ?>
                        <a href="<?php echo e(route('products.documents.create', $product)); ?>" 
                           class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                            <i class="fas fa-plus mr-2"></i>Upload Document
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/products/Views/documents/index.blade.php ENDPATH**/ ?>