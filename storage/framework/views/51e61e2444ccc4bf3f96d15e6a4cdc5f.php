<?php $__env->startSection('title', $announcement->title); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="max-w-6xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white"><?php echo e($announcement->title); ?></h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">Announcement Details</p>
            </div>
            <div class="flex space-x-3">
                <?php if(auth()->user()->hasPermission('manage_announcements')): ?>
                    <a href="<?php echo e(route('announcements.edit', $announcement)); ?>" 
                       class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-edit mr-2"></i>Edit
                    </a>
                <?php endif; ?>
                <a href="<?php echo e(route('announcements.index')); ?>"
                   class="bg-gray-500 dark:bg-gray-600 hover:bg-gray-600 dark:hover:bg-gray-500 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Back to Announcements
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Announcement Content -->
                <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg mb-6 transition duration-150 ease-in-out">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($announcement->getPriorityBadgeClass()); ?>">
                                    <?php echo e(ucfirst($announcement->priority)); ?>

                                </span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($announcement->getTypeBadgeClass()); ?>">
                                    <?php echo e(ucfirst($announcement->type)); ?>

                                </span>
                                <?php if($announcement->is_active): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 transition duration-150 ease-in-out">
                                        Active
                                    </span>
                                <?php else: ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 transition duration-150 ease-in-out">
                                        Inactive
                                    </span>
                                <?php endif; ?>
                                <?php if($announcement->requires_acknowledgment): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 transition duration-150 ease-in-out">
                                        Requires Acknowledgment
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <div class="px-6 py-4">
                        <div class="prose max-w-none dark:prose-invert text-gray-900 dark:text-white">
                            <?php echo nl2br(e($announcement->content)); ?>

                        </div>
                    </div>
                </div>

                <!-- Read Activity -->
                <?php if($recentReads->count() > 0): ?>
                    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Recent Read Activity</h3>
                        </div>
                        <div class="px-6 py-4">
                            <div class="space-y-3">
                                <?php $__currentLoopData = $recentReads; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $read): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0">
                                                <div class="h-8 w-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center transition duration-150 ease-in-out">
                                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                                                        <?php echo e(substr($read->user->name, 0, 1)); ?>

                                                    </span>
                                                </div>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e($read->user->name); ?></p>
                                                <p class="text-xs text-gray-500 dark:text-gray-400"><?php echo e($read->user->email); ?></p>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <p class="text-sm text-gray-900 dark:text-white">
                                                Read <?php echo e($read->read_at->diffForHumans()); ?>

                                            </p>
                                            <?php if($read->acknowledged_at): ?>
                                                <p class="text-xs text-green-600 dark:text-green-400">
                                                    Acknowledged <?php echo e($read->acknowledged_at->diffForHumans()); ?>

                                                </p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Announcement Info -->
                <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg mb-6 transition duration-150 ease-in-out">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Announcement Info</h3>
                    </div>
                    <div class="px-6 py-4 space-y-4">
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Created by</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($announcement->creator->name); ?></dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Created at</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($announcement->created_at->format('M j, Y g:i A')); ?></dd>
                        </div>
                        <?php if($announcement->published_at): ?>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Published at</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($announcement->published_at->format('M j, Y g:i A')); ?></dd>
                            </div>
                        <?php endif; ?>
                        <?php if($announcement->expires_at): ?>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Expires at</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($announcement->expires_at->format('M j, Y g:i A')); ?></dd>
                            </div>
                        <?php endif; ?>
                        <?php if($announcement->target_roles && count($announcement->target_roles) > 0): ?>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Target roles</dt>
                                <dd class="mt-1">
                                    <?php $__currentLoopData = $announcement->target_roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 mr-1 mb-1 transition duration-150 ease-in-out">
                                            <?php echo e(ucfirst($role)); ?>

                                        </span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </dd>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Read Statistics (Only visible to announcement creator) -->
                <?php if(auth()->id() === $announcement->created_by): ?>
                    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Read Statistics</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Only visible to the announcement creator</p>
                        </div>
                        <div class="px-6 py-4 space-y-4">
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Total users</dt>
                                <dd class="mt-1 text-2xl font-semibold text-gray-900 dark:text-white"><?php echo e($readStats['total_users']); ?></dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Users who read</dt>
                                <dd class="mt-1 text-2xl font-semibold text-blue-600 dark:text-blue-400">
                                    <?php echo e($readStats['read_count']); ?>

                                    <span class="text-sm font-normal text-gray-500 dark:text-gray-400">(<?php echo e($readStats['read_percentage']); ?>%)</span>
                                </dd>
                            </div>
                            <?php if($announcement->requires_acknowledgment): ?>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Users who acknowledged</dt>
                                    <dd class="mt-1 text-2xl font-semibold text-green-600 dark:text-green-400">
                                        <?php echo e($readStats['acknowledged_count']); ?>

                                        <span class="text-sm font-normal text-gray-500 dark:text-gray-400">(<?php echo e($readStats['acknowledged_percentage']); ?>%)</span>
                                    </dd>
                                </div>
                                <?php if($readStats['pending_acknowledgments'] > 0): ?>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Pending acknowledgments</dt>
                                        <dd class="mt-1 text-2xl font-semibold text-orange-600 dark:text-orange-400">
                                            <?php echo e($readStats['pending_acknowledgments']); ?>

                                            <span class="text-sm font-normal text-gray-500 dark:text-gray-400">users still need to acknowledge</span>
                                        </dd>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>

                            <!-- Progress Bars -->
                            <div class="space-y-3">
                                <div>
                                    <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                                        <span>Read Progress</span>
                                        <span><?php echo e($readStats['read_percentage']); ?>%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 transition duration-150 ease-in-out">
                                        <div class="bg-blue-600 h-2 rounded-full transition duration-150 ease-in-out" style="width: <?php echo e($readStats['read_percentage']); ?>%"></div>
                                    </div>
                                </div>
                                <?php if($announcement->requires_acknowledgment): ?>
                                    <div>
                                        <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                                            <span>Acknowledgment Progress</span>
                                            <span><?php echo e($readStats['acknowledged_percentage']); ?>%</span>
                                        </div>
                                        <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 transition duration-150 ease-in-out">
                                            <div class="bg-green-600 h-2 rounded-full transition duration-150 ease-in-out" style="width: <?php echo e($readStats['acknowledged_percentage']); ?>%"></div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/announcements/Views/show.blade.php ENDPATH**/ ?>