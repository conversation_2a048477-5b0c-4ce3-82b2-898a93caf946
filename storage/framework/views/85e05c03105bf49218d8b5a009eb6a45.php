<li class="px-4 py-4 sm:px-6 hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-150 ease-in-out">
    <div class="flex items-center justify-between">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <?php if($plugin->enabled): ?>
                    <div class="h-3 w-3 bg-green-400 rounded-full"></div>
                <?php else: ?>
                    <div class="h-3 w-3 bg-gray-400 rounded-full"></div>
                <?php endif; ?>
            </div>
            <div class="ml-4">
                <div class="flex items-center">
                    <h4 class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($plugin->name); ?></h4>
                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($plugin->enabled ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'); ?>">
                        <?php echo e($plugin->enabled ? 'Enabled' : 'Disabled'); ?>

                    </span>
                    <?php if($plugin->getCategory() === 'System'): ?>
                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                            <i class="fas fa-shield-alt mr-1"></i>
                            System
                        </span>
                    <?php else: ?>
                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200">
                            <i class="fas fa-puzzle-piece mr-1"></i>
                            Third-party
                        </span>
                    <?php endif; ?>
                    <?php if(!$plugin->isValid()): ?>
                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200">
                            <i class="fas fa-exclamation-triangle mr-1"></i>
                            Invalid
                        </span>
                    <?php endif; ?>
                </div>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1"><?php echo e($plugin->description ?: 'No description available'); ?></p>
                <div class="flex items-center mt-2 text-xs text-gray-400 dark:text-gray-500 space-x-4">
                    <span><i class="fas fa-tag mr-1"></i>v<?php echo e($plugin->version); ?></span>
                    <?php if(!empty($plugin->dependencies)): ?>
                        <span><i class="fas fa-link mr-1"></i>Depends on: <?php echo e(implode(', ', $plugin->dependencies)); ?></span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="flex items-center space-x-2">
            <a href="<?php echo e(route('plugins.show', $plugin->name)); ?>" class="inline-flex items-center px-3 py-1 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition duration-150 ease-in-out">
                <i class="fas fa-eye mr-1"></i>
                View
            </a>
            <?php if($plugin->enabled): ?>
                <?php if($plugin->isSystemPlugin()): ?>
                    <button type="button" class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-gray-400 dark:text-gray-500 bg-gray-200 dark:bg-gray-700 cursor-not-allowed" disabled title="System plugins cannot be disabled">
                        <i class="fas fa-lock mr-1"></i>
                        System Plugin
                    </button>
                <?php else: ?>
                    <form method="POST" action="<?php echo e(route('plugins.disable', $plugin->name)); ?>" class="inline">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-150 ease-in-out" onclick="return confirm('Are you sure you want to disable this plugin?')">
                            <i class="fas fa-stop mr-1"></i>
                            Disable
                        </button>
                    </form>
                <?php endif; ?>
            <?php else: ?>
                <form method="POST" action="<?php echo e(route('plugins.enable', $plugin->name)); ?>" class="inline">
                    <?php echo csrf_field(); ?>
                    <button type="submit" class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition duration-150 ease-in-out" <?php echo e(!$plugin->isValid() ? 'disabled' : ''); ?>>
                        <i class="fas fa-play mr-1"></i>
                        Enable
                    </button>
                </form>
            <?php endif; ?>
        </div>
    </div>
</li>
<?php /**PATH /Users/<USER>/Herd/business/resources/views/plugins/partials/plugin-item.blade.php ENDPATH**/ ?>