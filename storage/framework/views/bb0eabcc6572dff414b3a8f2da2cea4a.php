<?php $__env->startSection('title', 'Announcements'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Announcements</h1>
                <p class="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">Important updates and notifications</p>
            </div>
        </div>

        <!-- Unread Announcements -->
        <?php if($unreadAnnouncements->count() > 0): ?>
            <div class="mb-8">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-exclamation-circle text-red-500 dark:text-red-400 mr-2"></i>
                    Unread Announcements (<?php echo e($unreadAnnouncements->count()); ?>)
                </h2>
                <div class="space-y-4">
                    <?php $__currentLoopData = $unreadAnnouncements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $announcement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="bg-white dark:bg-gray-800 border-l-4 border-<?php echo e($announcement->type === 'error' ? 'red' : ($announcement->type === 'warning' ? 'yellow' : ($announcement->type === 'success' ? 'green' : 'blue'))); ?>-500 shadow-lg rounded-lg p-6 transition duration-150 ease-in-out">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center mb-2">
                                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white"><?php echo e($announcement->title); ?></h3>
                                        <span class="ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($announcement->getPriorityBadgeClass()); ?>">
                                            <?php echo e(ucfirst($announcement->priority)); ?>

                                        </span>
                                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($announcement->getTypeBadgeClass()); ?>">
                                            <?php echo e(ucfirst($announcement->type)); ?>

                                        </span>
                                    </div>
                                    <div class="prose max-w-none text-gray-700 dark:text-gray-300 mb-4">
                                        <?php echo nl2br(e($announcement->content)); ?>

                                    </div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                                        Published <?php echo e($announcement->published_at->diffForHumans()); ?> by <?php echo e($announcement->creator->name); ?>

                                    </div>
                                </div>
                            </div>
                            <div class="mt-4 flex space-x-3">
                                <?php if($announcement->requires_acknowledgment): ?>
                                    <!-- Single action for announcements requiring acknowledgment -->
                                    <button onclick="acknowledgeAnnouncement(<?php echo e($announcement->id); ?>)"
                                            class="bg-green-500 dark:bg-green-600 hover:bg-green-700 dark:hover:bg-green-500 text-white font-bold py-2 px-4 rounded text-sm transition duration-150 ease-in-out">
                                        <i class="fas fa-check mr-2"></i>I Have Read and Understood
                                    </button>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 self-center">This will mark the announcement as read and acknowledged</p>
                                <?php else: ?>
                                    <!-- Simple mark as read for announcements not requiring acknowledgment -->
                                    <button onclick="markAsRead(<?php echo e($announcement->id); ?>)"
                                            class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded text-sm transition duration-150 ease-in-out">
                                        <i class="fas fa-eye mr-2"></i>Mark as Read
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Read Announcements -->
        <?php if($readAnnouncements->count() > 0): ?>
            <div>
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                    Read Announcements
                </h2>
                <div class="space-y-4">
                    <?php $__currentLoopData = $readAnnouncements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $announcement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 opacity-75 transition duration-150 ease-in-out">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center mb-2">
                                        <h3 class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($announcement->title); ?></h3>
                                        <span class="ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($announcement->getPriorityBadgeClass()); ?>">
                                            <?php echo e(ucfirst($announcement->priority)); ?>

                                        </span>
                                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($announcement->getTypeBadgeClass()); ?>">
                                            <?php echo e(ucfirst($announcement->type)); ?>

                                        </span>
                                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 transition duration-150 ease-in-out">
                                            <i class="fas fa-check mr-1"></i>Read
                                        </span>
                                        <?php if($announcement->requires_acknowledgment && $announcement->isAcknowledgedByUser(auth()->user())): ?>
                                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 transition duration-150 ease-in-out">
                                                <i class="fas fa-thumbs-up mr-1"></i>Acknowledged
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="prose max-w-none text-gray-700 dark:text-gray-300 mb-4">
                                        <?php echo nl2br(e($announcement->content)); ?>

                                    </div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                                        Published <?php echo e($announcement->published_at->diffForHumans()); ?> by <?php echo e($announcement->creator->name); ?>

                                        <?php
                                            $readRecord = $announcement->reads()->where('user_id', auth()->id())->first();
                                        ?>
                                        <?php if($readRecord): ?>
                                            • Read <?php echo e($readRecord->read_at->diffForHumans()); ?>

                                            <?php if($readRecord->acknowledged_at): ?>
                                                • Acknowledged <?php echo e($readRecord->acknowledged_at->diffForHumans()); ?>

                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <?php if($announcement->requires_acknowledgment && !$announcement->isAcknowledgedByUser(auth()->user())): ?>
                                <div class="mt-4">
                                    <button onclick="acknowledgeAnnouncement(<?php echo e($announcement->id); ?>)" 
                                            class="bg-green-500 dark:bg-green-600 hover:bg-green-700 dark:hover:bg-green-500 text-white font-bold py-2 px-4 rounded text-sm transition duration-150 ease-in-out">
                                        <i class="fas fa-check mr-2"></i>I Have Read and Understood
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- No Announcements -->
        <?php if($unreadAnnouncements->count() === 0 && $readAnnouncements->count() === 0): ?>
            <div class="text-center py-12">
                <i class="fas fa-bullhorn text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 text-6xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No announcements</h3>
                <p class="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">There are currently no announcements to display.</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
function markAsRead(announcementId) {
    fetch(`/announcements/${announcementId}/mark-read`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('Announcement marked as read', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showToast('Error: ' + data.message, 'error');
        }
    })
    .catch(error => {
        showToast('Error: ' + error.message, 'error');
    });
}

function acknowledgeAnnouncement(announcementId) {
    if (confirm('By clicking "OK", you confirm that you have read and understood this announcement. This will mark it as both read and acknowledged.')) {
        fetch(`/announcements/${announcementId}/acknowledge`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('Announcement read and acknowledged successfully', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast('Error: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showToast('Error: ' + error.message, 'error');
        });
    }
}

function showToast(message, type) {
    // Simple toast notification - you can replace this with your preferred notification system
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 p-4 rounded-md text-white z-50 ${type === 'success' ? 'bg-green-500 dark:bg-green-600' : 'bg-red-500 dark:bg-red-600'}`;
    toast.textContent = message;
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 3000);
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/announcements/Views/user-announcements.blade.php ENDPATH**/ ?>