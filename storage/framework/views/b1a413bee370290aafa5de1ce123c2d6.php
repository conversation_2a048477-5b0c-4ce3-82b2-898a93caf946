<?php $__env->startSection('title', 'Task: ' . $task->name); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white"><?php echo e($task->name); ?></h1>
        <div class="flex space-x-3">
            <?php if(auth()->user()->hasPermission('sync_clickup_data')): ?>
                <button onclick="syncTask(<?php echo e($task->id); ?>)" 
                        class="bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-sync mr-2"></i>
                    Sync Task
                </button>
            <?php endif; ?>
            <?php if($task->url): ?>
                <a href="<?php echo e($task->url); ?>" target="_blank"
                   class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-external-link-alt mr-2"></i>
                    View in ClickUp
                </a>
            <?php endif; ?>
            <a href="<?php echo e(route('clickup.tasks.index')); ?>"
               class="bg-gray-600 dark:bg-gray-700 hover:bg-gray-700 dark:hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Tasks
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Task Information -->
        <div class="lg:col-span-2">
            <!-- Task Details -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700 mb-6">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Task Details</h3>
                </div>
                <div class="p-6">
                    <?php if($task->description): ?>
                        <div class="mb-6">
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Description</h4>
                            <div class="text-sm text-gray-900 dark:text-white whitespace-pre-wrap"><?php echo e($task->description); ?></div>
                        </div>
                    <?php endif; ?>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Status</h4>
                            <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full 
                                <?php switch($task->status):
                                    case ('complete'): ?>
                                    <?php case ('closed'): ?>
                                        bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100
                                        <?php break; ?>
                                    <?php case ('in progress'): ?>
                                        bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100
                                        <?php break; ?>
                                    <?php case ('review'): ?>
                                        bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100
                                        <?php break; ?>
                                    <?php default: ?>
                                        bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300
                                <?php endswitch; ?>">
                                <?php echo e(ucfirst($task->status)); ?>

                            </span>
                        </div>

                        <?php if($task->priority): ?>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Priority</h4>
                                <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full 
                                    <?php switch($task->priority):
                                        case ('urgent'): ?>
                                            bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100
                                            <?php break; ?>
                                        <?php case ('high'): ?>
                                            bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-100
                                            <?php break; ?>
                                        <?php case ('normal'): ?>
                                            bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100
                                            <?php break; ?>
                                        <?php case ('low'): ?>
                                            bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300
                                            <?php break; ?>
                                    <?php endswitch; ?>">
                                    <?php echo e(ucfirst($task->priority)); ?>

                                </span>
                            </div>
                        <?php endif; ?>

                        <?php if($task->due_date): ?>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Due Date</h4>
                                <div class="text-sm <?php echo e($task->is_overdue ? 'text-red-600 dark:text-red-400' : 'text-gray-900 dark:text-white'); ?>">
                                    <?php echo e($task->due_date->format('M d, Y H:i')); ?>

                                    <?php if($task->is_overdue): ?>
                                        <span class="ml-2 text-red-600 dark:text-red-400">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            Overdue
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if($task->time_remaining): ?>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Time Remaining</h4>
                                <div class="text-sm text-gray-900 dark:text-white"><?php echo e($task->time_remaining); ?></div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <?php if($task->assignee_names && count($task->assignee_names) > 0): ?>
                        <div class="mt-6">
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Assignees</h4>
                            <div class="flex flex-wrap gap-2">
                                <?php $__currentLoopData = $task->assignee_names; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $assignee): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <span class="px-3 py-1 bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-100 text-sm rounded-full">
                                        <?php echo e($assignee); ?>

                                    </span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if($task->tags && count($task->tags) > 0): ?>
                        <div class="mt-6">
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Tags</h4>
                            <div class="flex flex-wrap gap-2">
                                <?php $__currentLoopData = $task->tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <span class="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300 text-sm rounded-full">
                                        <?php echo e($tag['name'] ?? $tag); ?>

                                    </span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Subtasks -->
            <?php if($task->subtasks->count() > 0): ?>
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Subtasks (<?php echo e($task->subtasks->count()); ?>)</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-3">
                            <?php $__currentLoopData = $task->subtasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subtask): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-<?php echo e($subtask->is_completed ? 'check-circle text-green-500' : 'circle text-gray-400'); ?>"></i>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900 dark:text-white <?php echo e($subtask->is_completed ? 'line-through' : ''); ?>">
                                                <?php echo e($subtask->name); ?>

                                            </div>
                                            <div class="text-xs text-gray-500 dark:text-gray-400">
                                                <?php echo e(ucfirst($subtask->status)); ?>

                                            </div>
                                        </div>
                                    </div>
                                    <?php if($subtask->url): ?>
                                        <a href="<?php echo e($subtask->url); ?>" target="_blank" 
                                           class="text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300">
                                            <i class="fas fa-external-link-alt"></i>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">
            <!-- List Information -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700 mb-6">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">List Information</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">List</h4>
                            <div class="text-sm text-gray-900 dark:text-white"><?php echo e($task->list->name); ?></div>
                        </div>
                        <?php if($task->list->productManager): ?>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Product Manager</h4>
                                <div class="text-sm text-gray-900 dark:text-white"><?php echo e($task->list->productManager->name); ?></div>
                                <div class="text-xs text-gray-500 dark:text-gray-400"><?php echo e($task->list->productManager->email); ?></div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Task Metrics -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700 mb-6">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Task Metrics</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Progress</h4>
                            <div class="flex items-center">
                                <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 mr-2">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: <?php echo e($task->progress_percentage); ?>%"></div>
                                </div>
                                <span class="text-sm text-gray-900 dark:text-white"><?php echo e($task->progress_percentage); ?>%</span>
                            </div>
                        </div>

                        <?php if($task->age_in_days): ?>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Age</h4>
                                <div class="text-sm text-gray-900 dark:text-white"><?php echo e($task->age_in_days); ?> days</div>
                            </div>
                        <?php endif; ?>

                        <?php if($task->completion_time): ?>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Completion Time</h4>
                                <div class="text-sm text-gray-900 dark:text-white"><?php echo e($task->completion_time); ?> hours</div>
                            </div>
                        <?php endif; ?>

                        <?php if($task->points): ?>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Points</h4>
                                <div class="text-sm text-gray-900 dark:text-white"><?php echo e($task->points); ?></div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Timestamps -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Timeline</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <?php if($task->date_created): ?>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Created</h4>
                                <div class="text-sm text-gray-900 dark:text-white">
                                    <?php echo e(\Carbon\Carbon::parse($task->date_created)->format('M d, Y H:i')); ?>

                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if($task->date_updated): ?>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Last Updated</h4>
                                <div class="text-sm text-gray-900 dark:text-white">
                                    <?php echo e(\Carbon\Carbon::parse($task->date_updated)->diffForHumans()); ?>

                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if($task->date_closed): ?>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Completed</h4>
                                <div class="text-sm text-gray-900 dark:text-white">
                                    <?php echo e(\Carbon\Carbon::parse($task->date_closed)->format('M d, Y H:i')); ?>

                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if($task->last_synced_at): ?>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Last Synced</h4>
                                <div class="text-sm text-gray-900 dark:text-white">
                                    <?php echo e($task->last_synced_at->diffForHumans()); ?>

                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
// Sync individual task
window.syncTask = function(taskId) {
    fetch(`/clickup/tasks/${taskId}/sync`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Task marked for sync successfully');
            location.reload();
        } else {
            alert('Sync failed: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Sync failed. Please try again.');
    });
};
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/clickup/Views/tasks/show.blade.php ENDPATH**/ ?>