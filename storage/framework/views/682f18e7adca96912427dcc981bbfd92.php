<?php $__env->startSection('title', 'Business Products'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="max-w-6xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Products: <?php echo e($business->name); ?></h1>
                <p class="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">Manage products and services for this business</p>
            </div>
            <div class="flex space-x-3">
                <a href="<?php echo e(route('business.show', $business)); ?>" 
                   class="bg-gray-50 dark:bg-gray-7000 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Back to Business
                </a>
                <?php if(auth()->user()->hasPermission('manage_businesses') && $availableProducts->count() > 0): ?>
                    <a href="<?php echo e(route('business.products.create', $business)); ?>" 
                       class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Assign Product
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <!-- Assigned Products -->
        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg mb-8 transition duration-150 ease-in-out">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                    Assigned Products (<?php echo e($assignedProducts->count()); ?>)
                </h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Products currently assigned to this business</p>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700">
                <?php if($assignedProducts->count() > 0): ?>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700 transition duration-150 ease-in-out">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Product</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Pricing</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Period</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700 transition duration-150 ease-in-out">
                                <?php $__currentLoopData = $assignedProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e($product->name); ?></div>
                                                <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500"><?php echo e($product->category_label); ?></div>
                                                <?php if($product->pivot->notes): ?>
                                                    <div class="text-xs text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1"><?php echo e(Str::limit($product->pivot->notes, 50)); ?></div>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-white">
                                                <?php if($product->pivot->custom_price): ?>
                                                    $<?php echo e(number_format($product->pivot->custom_price, 2)); ?>

                                                <?php elseif($product->base_price): ?>
                                                    $<?php echo e(number_format($product->base_price, 2)); ?>

                                                <?php else: ?>
                                                    Custom pricing
                                                <?php endif; ?>
                                            </div>
                                            <?php if($product->pivot->pricing_model): ?>
                                                <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500"><?php echo e(ucfirst($product->pivot->pricing_model)); ?></div>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                                <?php echo e($product->pivot->status === 'active' ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 
                                                   ($product->pivot->status === 'pending' ? 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' : 
                                                   ($product->pivot->status === 'cancelled' ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'))); ?> transition duration-150 ease-in-out">
                                                <?php echo e(ucfirst($product->pivot->status)); ?>

                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                                            <?php if($product->pivot->start_date): ?>
                                                <div>Start: <?php echo e(\Carbon\Carbon::parse($product->pivot->start_date)->format('M d, Y')); ?></div>
                                            <?php endif; ?>
                                            <?php if($product->pivot->end_date): ?>
                                                <div>End: <?php echo e(\Carbon\Carbon::parse($product->pivot->end_date)->format('M d, Y')); ?></div>
                                            <?php endif; ?>
                                            <?php if(!$product->pivot->start_date && !$product->pivot->end_date): ?>
                                                <span class="text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">No dates set</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <?php if(auth()->user()->hasPermission('manage_businesses')): ?>
                                                    <a href="<?php echo e(route('business.products.edit', [$business, $product])); ?>" 
                                                       class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300">Edit</a>
                                                    <form method="POST" action="<?php echo e(route('business.products.remove', [$business, $product])); ?>" 
                                                          onsubmit="return confirm('Remove this product from the business?')" class="inline">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('DELETE'); ?>
                                                        <button type="submit" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300">Remove</button>
                                                    </form>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="px-6 py-8 text-center">
                        <p class="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">No products assigned to this business yet.</p>
                        <?php if(auth()->user()->hasPermission('manage_businesses') && $availableProducts->count() > 0): ?>
                            <a href="<?php echo e(route('business.products.create', $business)); ?>" 
                               class="mt-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:text-blue-200 dark:hover:text-blue-300 text-sm">
                                Assign first product
                            </a>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Available Products -->
        <?php if($availableProducts->count() > 0): ?>
            <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                <div class="px-4 py-5 sm:px-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                        Available Products (<?php echo e($availableProducts->count()); ?>)
                    </h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Products that can be assigned to this business</p>
                </div>
                <div class="border-t border-gray-200 dark:border-gray-700">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
                        <?php $__currentLoopData = $availableProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow">
                                <div class="flex justify-between items-start mb-3">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e($product->name); ?></h4>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500"><?php echo e($product->category_label); ?></p>
                                    </div>
                                    <?php if(auth()->user()->hasPermission('manage_businesses')): ?>
                                        <a href="<?php echo e(route('business.products.create', $business)); ?>?product=<?php echo e($product->id); ?>" 
                                           class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:text-blue-200 dark:hover:text-blue-300 text-sm">
                                            Assign
                                        </a>
                                    <?php endif; ?>
                                </div>
                                
                                <?php if($product->description): ?>
                                    <p class="text-xs text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mb-3"><?php echo e(Str::limit($product->description, 80)); ?></p>
                                <?php endif; ?>
                                
                                <div class="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 space-y-1">
                                    <div class="flex justify-between">
                                        <span>Base Price:</span>
                                        <span><?php echo e($product->formatted_base_price); ?></span>
                                    </div>
                                    <?php if($product->pricing_model): ?>
                                        <div class="flex justify-between">
                                            <span>Model:</span>
                                            <span><?php echo e($product->pricing_model_label); ?></span>
                                        </div>
                                    <?php endif; ?>
                                </div>


                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Product Statistics -->
        <div class="mt-8 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-700 rounded-md p-4 transition duration-150 ease-in-out">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
                <div>
                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400"><?php echo e($assignedProducts->count()); ?></div>
                    <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Assigned Products</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-green-600 dark:text-green-400"><?php echo e($assignedProducts->where('pivot.status', 'active')->count()); ?></div>
                    <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Active Products</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-yellow-600"><?php echo e($assignedProducts->where('pivot.status', 'pending')->count()); ?></div>
                    <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Pending Products</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-purple-600 dark:text-purple-400"><?php echo e($availableProducts->count()); ?></div>
                    <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Available Products</div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/business/Views/business-products/index.blade.php ENDPATH**/ ?>