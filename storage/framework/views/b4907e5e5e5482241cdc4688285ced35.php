<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'pluginName' => '',
    'seedRoute' => '',
    'clearRoute' => '',
    'hasData' => false,
    'position' => 'inline' // 'inline' or 'empty-state'
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'pluginName' => '',
    'seedRoute' => '',
    'clearRoute' => '',
    'hasData' => false,
    'position' => 'inline' // 'inline' or 'empty-state'
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<?php if(auth()->user() && (auth()->user()->role->name === 'admin' || auth()->user()->hasPermission('manage_' . strtolower($pluginName)))): ?>
    <div class="<?php echo e($position === 'empty-state' ? 'flex justify-center space-x-2 mt-4' : 'flex space-x-2'); ?>">
        <?php if($seedRoute): ?>
            <button onclick="seedPluginData('<?php echo e($seedRoute); ?>', '<?php echo e($pluginName); ?>')" 
                    class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded transition-colors duration-200">
                <i class="fas fa-seedling mr-2"></i>Seed Sample Data
            </button>
        <?php endif; ?>
        
        <?php if($clearRoute && $hasData): ?>
            <button onclick="clearPluginData('<?php echo e($clearRoute); ?>', '<?php echo e($pluginName); ?>')" 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded transition-colors duration-200">
                <i class="fas fa-trash mr-2"></i>Clear All Data
            </button>
        <?php endif; ?>
    </div>
<?php endif; ?>

<script>
function seedPluginData(route, pluginName) {
    if (confirm(`This will create sample data for ${pluginName} plugin. Continue?`)) {
        showLoadingState('seeding');
        
        fetch(route, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            hideLoadingState();
            if (data.success) {
                showSuccessMessage(data.message);
                setTimeout(() => location.reload(), 1500);
            } else {
                showErrorMessage('Error: ' + data.message);
            }
        })
        .catch(error => {
            hideLoadingState();
            showErrorMessage('Error: ' + error.message);
        });
    }
}

function clearPluginData(route, pluginName) {
    if (confirm(`⚠️ WARNING: This will permanently delete ALL ${pluginName} data. This action cannot be undone. Are you absolutely sure?`)) {
        if (confirm(`This is your final confirmation. All ${pluginName} plugin data will be permanently deleted. Continue?`)) {
            showLoadingState('clearing');
            
            fetch(route, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                hideLoadingState();
                if (data.success) {
                    showSuccessMessage(data.message);
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showErrorMessage('Error: ' + data.message);
                }
            })
            .catch(error => {
                hideLoadingState();
                showErrorMessage('Error: ' + error.message);
            });
        }
    }
}

function showLoadingState(action) {
    // Create loading overlay if it doesn't exist
    if (!document.getElementById('plugin-loading-overlay')) {
        const overlay = document.createElement('div');
        overlay.id = 'plugin-loading-overlay';
        overlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        overlay.innerHTML = `
            <div class="bg-white rounded-lg p-6 max-w-sm mx-4">
                <div class="flex items-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mr-3"></div>
                    <span id="loading-text" class="text-gray-700 font-medium">Processing...</span>
                </div>
            </div>
        `;
        document.body.appendChild(overlay);
    }
    
    const loadingText = document.getElementById('loading-text');
    if (loadingText) {
        loadingText.textContent = action === 'seeding' ? 'Seeding sample data...' : 'Clearing plugin data...';
    }
}

function hideLoadingState() {
    const overlay = document.getElementById('plugin-loading-overlay');
    if (overlay) {
        overlay.remove();
    }
}

function showSuccessMessage(message) {
    showToast(message, 'success');
}

function showErrorMessage(message) {
    showToast(message, 'error');
}

function showToast(message, type) {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'fixed top-4 right-4 z-50 space-y-2';
        document.body.appendChild(toastContainer);
    }
    
    // Create toast element
    const toast = document.createElement('div');
    const bgColor = type === 'success' ? 'bg-green-500' : 'bg-red-500';
    const icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';
    
    toast.className = `${bgColor} text-white px-6 py-3 rounded-lg shadow-lg flex items-center max-w-sm transform transition-all duration-300 translate-x-full opacity-0`;
    toast.innerHTML = `
        <i class="fas ${icon} mr-2"></i>
        <span class="flex-1">${message}</span>
        <button onclick="this.parentElement.remove()" class="ml-2 text-white hover:text-gray-200">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    toastContainer.appendChild(toast);
    
    // Animate in
    setTimeout(() => {
        toast.classList.remove('translate-x-full', 'opacity-0');
    }, 100);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (toast.parentElement) {
            toast.classList.add('translate-x-full', 'opacity-0');
            setTimeout(() => toast.remove(), 300);
        }
    }, 5000);
}
</script>
<?php /**PATH /Users/<USER>/Herd/business/resources/views/components/plugin-data-management.blade.php ENDPATH**/ ?>