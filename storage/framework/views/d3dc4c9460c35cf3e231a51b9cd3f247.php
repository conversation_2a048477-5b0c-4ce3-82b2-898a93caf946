<?php $__env->startSection('title', 'ClickUp Settings'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">ClickUp Settings</h1>
        <div class="flex space-x-3">
            <a href="<?php echo e(route('clickup.settings.sync')); ?>"
               class="bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-sync mr-2"></i>
                Sync Settings
            </a>
            <a href="<?php echo e(route('clickup.settings.general')); ?>"
               class="bg-purple-600 dark:bg-purple-700 hover:bg-purple-700 dark:hover:bg-purple-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-sliders-h mr-2"></i>
                General Settings
            </a>
            <a href="<?php echo e(route('clickup.settings.assignments')); ?>"
               class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-user-cog mr-2"></i>
                List Assignments
            </a>
            <a href="<?php echo e(route('clickup.dashboard')); ?>"
               class="bg-gray-600 dark:bg-gray-700 hover:bg-gray-700 dark:hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Workspace Selection -->
    <?php if($currentToken): ?>
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700 mb-8">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Workspace & Space Selection</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Select which ClickUp workspace and spaces to sync data from.</p>
            </div>
            <div class="p-6">
                <form method="POST" action="<?php echo e(route('clickup.settings.update-workspace')); ?>" class="space-y-6">
                    <?php echo csrf_field(); ?>

                    <!-- Workspace Selection -->
                    <div>
                        <label for="workspace_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            ClickUp Workspace
                        </label>
                        <select name="workspace_id" id="workspace_id" required
                                class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                onchange="this.form.submit()">
                            <option value="">Select a workspace...</option>
                            <?php $__currentLoopData = $workspaces; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $workspace): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($workspace['id']); ?>" <?php echo e($selectedWorkspace === $workspace['id'] ? 'selected' : ''); ?>>
                                    <?php echo e($workspace['name']); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <?php $__errorArgs = ['workspace_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <?php if(empty($workspaces)): ?>
                            <p class="mt-1 text-sm text-yellow-600 dark:text-yellow-400">
                                <i class="fas fa-exclamation-triangle mr-1"></i>
                                No workspaces found. Please check your API token.
                            </p>
                        <?php endif; ?>
                    </div>

                    <!-- Space Selection -->
                    <?php if($selectedWorkspace && !empty($spaces)): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                                ClickUp Spaces (Optional - leave empty to sync all spaces)
                            </label>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-48 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md p-4">
                                <?php $__currentLoopData = $spaces; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $space): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="flex items-center">
                                        <input type="checkbox" name="space_ids[]" value="<?php echo e($space['id']); ?>" id="space_<?php echo e($space['id']); ?>"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                                        <label for="space_<?php echo e($space['id']); ?>" class="ml-2 block text-sm text-gray-900 dark:text-white">
                                            <?php echo e($space['name']); ?>

                                        </label>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                            <?php $__errorArgs = ['space_ids'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="flex justify-end">
                            <button type="submit"
                                    class="bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                                <i class="fas fa-save mr-2"></i>
                                Save Workspace Settings
                            </button>
                        </div>
                    <?php elseif($selectedWorkspace && empty($spaces)): ?>
                        <div class="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-md p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                                        No Spaces Found
                                    </h3>
                                    <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                                        <p>No spaces were found in the selected workspace. This might be normal if your workspace doesn't use spaces.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </form>
            </div>
        </div>
    <?php endif; ?>

    <!-- API Token Configuration -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700 mb-8">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">API Token Configuration</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Configure your ClickUp API token to enable data synchronization.</p>
        </div>
        <div class="p-6">
            <?php if($currentToken): ?>
                <!-- Current Token Info -->
                <div class="bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-md p-4 mb-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-green-800 dark:text-green-200">
                                API Token Configured
                            </h3>
                            <div class="mt-2 text-sm text-green-700 dark:text-green-300">
                                <p>Token Name: <?php echo e($currentToken->name); ?></p>
                                <p>Created: <?php echo e($currentToken->created_at->format('M d, Y H:i')); ?></p>
                                <?php if($currentToken->last_used_at): ?>
                                    <p>Last Used: <?php echo e($currentToken->last_used_at->diffForHumans()); ?></p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Test Connection -->
                <div class="mb-6">
                    <button id="test-connection-btn" 
                            class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-plug mr-2"></i>
                        Test Connection
                    </button>
                </div>

                <!-- Replace Token Form -->
                <form method="POST" action="<?php echo e(route('clickup.settings.save-token')); ?>" class="space-y-6">
                    <?php echo csrf_field(); ?>
                    <div>
                        <label for="replace_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Token Name</label>
                        <input type="text" name="name" id="replace_name" value="<?php echo e(old('name', 'Default Token')); ?>"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div>
                        <label for="replace_token" class="block text-sm font-medium text-gray-700 dark:text-gray-300">New API Token</label>
                        <input type="text" name="token" id="replace_token" placeholder="pk_707692_S2PIZXBJGVMVWOVZ5BGZ1U8LMNX0FONP"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                            Get your API token from ClickUp Settings → Apps → API Token
                        </p>
                        <?php $__errorArgs = ['token'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div class="flex space-x-3">
                        <button type="submit" 
                                class="bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                            <i class="fas fa-save mr-2"></i>
                            Update Token
                        </button>
                        
                        <button type="button" id="delete-token-btn"
                                class="bg-red-600 dark:bg-red-700 hover:bg-red-700 dark:hover:bg-red-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                            <i class="fas fa-trash mr-2"></i>
                            Delete Token
                        </button>
                    </div>
                </form>
            <?php else: ?>
                <!-- No Token Configured -->
                <div class="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-md p-4 mb-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                                No API Token Configured
                            </h3>
                            <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                                <p>Please configure your ClickUp API token to enable data synchronization.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Add Token Form -->
                <form method="POST" action="<?php echo e(route('clickup.settings.save-token')); ?>" class="space-y-6">
                    <?php echo csrf_field(); ?>
                    <div>
                        <label for="add_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Token Name</label>
                        <input type="text" name="name" id="add_name" value="<?php echo e(old('name', 'Default Token')); ?>"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div>
                        <label for="add_token" class="block text-sm font-medium text-gray-700 dark:text-gray-300">ClickUp API Token</label>
                        <input type="text" name="token" id="add_token" placeholder="pk_707692_S2PIZXBJGVMVWOVZ5BGZ1U8LMNX0FONP"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                            Get your API token from ClickUp Settings → Apps → API Token
                        </p>
                        <?php $__errorArgs = ['token'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <button type="submit" 
                            class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-save mr-2"></i>
                        Save Token
                    </button>
                </form>
            <?php endif; ?>
        </div>
    </div>

    <!-- API Status -->
    <?php if($apiStatus['configured']): ?>
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">API Status</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Rate Limit Status</h4>
                        <?php if($apiStatus['rate_limit_remaining'] !== null): ?>
                            <p class="text-sm text-gray-900 dark:text-white">
                                Remaining: <?php echo e($apiStatus['rate_limit_remaining']); ?> requests
                            </p>
                            <?php if($apiStatus['rate_limit_reset_at']): ?>
                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                    Resets: <?php echo e(\Carbon\Carbon::parse($apiStatus['rate_limit_reset_at'])->diffForHumans()); ?>

                                </p>
                            <?php endif; ?>
                        <?php else: ?>
                            <p class="text-sm text-gray-500 dark:text-gray-400">No rate limit data available</p>
                        <?php endif; ?>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Last Activity</h4>
                        <?php if($apiStatus['last_used_at']): ?>
                            <p class="text-sm text-gray-900 dark:text-white">
                                <?php echo e(\Carbon\Carbon::parse($apiStatus['last_used_at'])->diffForHumans()); ?>

                            </p>
                        <?php else: ?>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Never used</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Delete Token Modal -->
<div id="delete-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900">
                <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mt-2">Delete API Token</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500 dark:text-gray-400">
                    Are you sure you want to delete the API token? This will disable ClickUp integration.
                </p>
            </div>
            <div class="items-center px-4 py-3">
                <form method="POST" action="<?php echo e(route('clickup.settings.delete-token')); ?>" class="inline">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" 
                            class="px-4 py-2 bg-red-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-300 mr-2">
                        Delete
                    </button>
                </form>
                <button id="cancel-delete" 
                        class="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Test connection
    const testBtn = document.getElementById('test-connection-btn');
    if (testBtn) {
        testBtn.addEventListener('click', function() {
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Testing...';
            
            fetch('<?php echo e(route("clickup.settings.test-connection")); ?>', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Connection successful!');
                } else {
                    alert('Connection failed: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Connection test failed. Please try again.');
            })
            .finally(() => {
                this.disabled = false;
                this.innerHTML = '<i class="fas fa-plug mr-2"></i>Test Connection';
            });
        });
    }

    // Delete token modal
    const deleteBtn = document.getElementById('delete-token-btn');
    const deleteModal = document.getElementById('delete-modal');
    const cancelBtn = document.getElementById('cancel-delete');

    if (deleteBtn) {
        deleteBtn.addEventListener('click', function() {
            deleteModal.classList.remove('hidden');
        });
    }

    if (cancelBtn) {
        cancelBtn.addEventListener('click', function() {
            deleteModal.classList.add('hidden');
        });
    }

    // Close modal on outside click
    if (deleteModal) {
        deleteModal.addEventListener('click', function(e) {
            if (e.target === deleteModal) {
                deleteModal.classList.add('hidden');
            }
        });
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/clickup/Views/settings/index.blade.php ENDPATH**/ ?>