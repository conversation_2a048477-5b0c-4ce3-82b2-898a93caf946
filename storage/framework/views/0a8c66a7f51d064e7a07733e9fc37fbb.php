<?php $__env->startSection('title', 'Product Managers'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Product Managers</h1>
        <div class="flex space-x-3">
            <?php if(auth()->user()->hasPermission('manage_clickup_product_managers')): ?>
                <a href="<?php echo e(route('clickup.product-managers.create')); ?>"
                   class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-plus mr-2"></i>
                    Add Product Manager
                </a>
            <?php endif; ?>
            <a href="<?php echo e(route('clickup.dashboard')); ?>"
               class="bg-gray-600 dark:bg-gray-700 hover:bg-gray-700 dark:hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Search and Filter Form -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6 border border-gray-200 dark:border-gray-700">
        <form method="GET" action="<?php echo e(route('clickup.product-managers.index')); ?>">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search</label>
                    <input type="text" name="search" id="search" value="<?php echo e(request('search')); ?>"
                           placeholder="Search managers..."
                           class="w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label for="department" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Department</label>
                    <select name="department" id="department"
                            class="w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Departments</option>
                        <option value="Product" <?php echo e(request('department') === 'Product' ? 'selected' : ''); ?>>Product</option>
                        <option value="Engineering" <?php echo e(request('department') === 'Engineering' ? 'selected' : ''); ?>>Engineering</option>
                        <option value="Design" <?php echo e(request('department') === 'Design' ? 'selected' : ''); ?>>Design</option>
                        <option value="Marketing" <?php echo e(request('department') === 'Marketing' ? 'selected' : ''); ?>>Marketing</option>
                    </select>
                </div>

                <div>
                    <label for="active" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label>
                    <select name="active" id="active"
                            class="w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Statuses</option>
                        <option value="1" <?php echo e(request('active') === '1' ? 'selected' : ''); ?>>Active</option>
                        <option value="0" <?php echo e(request('active') === '0' ? 'selected' : ''); ?>>Inactive</option>
                    </select>
                </div>

                <div class="flex items-end">
                    <button type="submit" 
                            class="w-full bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-search mr-2"></i>
                        Filter
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Product Managers Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <?php $__empty_1 = true; $__currentLoopData = $managers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $manager): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow duration-200">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <?php if($manager->avatar_url): ?>
                                <img src="<?php echo e($manager->avatar_url); ?>" alt="<?php echo e($manager->name); ?>" 
                                     class="w-12 h-12 rounded-full mr-3">
                            <?php else: ?>
                                <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-white font-bold text-lg">
                                        <?php echo e(substr($manager->name, 0, 1)); ?>

                                    </span>
                                </div>
                            <?php endif; ?>
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($manager->name); ?></h3>
                                <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo e($manager->email); ?></p>
                            </div>
                        </div>
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                            <?php echo e($manager->is_active ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'); ?>">
                            <?php echo e($manager->is_active ? 'Active' : 'Inactive'); ?>

                        </span>
                    </div>

                    <div class="space-y-2 mb-4">
                        <?php if($manager->role): ?>
                            <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                                <i class="fas fa-briefcase mr-2"></i>
                                <?php echo e($manager->role); ?>

                            </div>
                        <?php endif; ?>
                        <?php if($manager->department): ?>
                            <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                                <i class="fas fa-building mr-2"></i>
                                <?php echo e($manager->department); ?>

                            </div>
                        <?php endif; ?>
                        <?php if($manager->hire_date): ?>
                            <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                                <i class="fas fa-calendar mr-2"></i>
                                Hired <?php echo e($manager->hire_date->format('M Y')); ?>

                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Performance Metrics -->
                    <?php
                        $metrics = $manager->getPerformanceMetrics();
                    ?>
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div class="text-center">
                            <div class="text-lg font-bold text-gray-900 dark:text-white"><?php echo e($metrics['total_lists']); ?></div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">Lists</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-gray-900 dark:text-white"><?php echo e($metrics['total_tasks']); ?></div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">Tasks</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-gray-900 dark:text-white"><?php echo e($metrics['completion_rate']); ?>%</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">Completion</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-gray-900 dark:text-white"><?php echo e($metrics['productivity_score']); ?></div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">Productivity</div>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="flex justify-between items-center">
                        <div class="flex space-x-2">
                            <?php if(auth()->user()->hasPermission('view_clickup_product_managers')): ?>
                                <a href="<?php echo e(route('clickup.product-managers.show', $manager)); ?>" 
                                   class="text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300">
                                    <i class="fas fa-eye"></i>
                                </a>
                            <?php endif; ?>
                            <?php if(auth()->user()->hasPermission('manage_clickup_product_managers')): ?>
                                <a href="<?php echo e(route('clickup.product-managers.edit', $manager)); ?>" 
                                   class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 dark:hover:text-indigo-300">
                                    <i class="fas fa-edit"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Completion Rate Progress Bar -->
                        <div class="flex items-center">
                            <div class="w-16 bg-gray-200 dark:bg-gray-600 rounded-full h-2 mr-2">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: <?php echo e($metrics['completion_rate']); ?>%"></div>
                            </div>
                            <span class="text-xs text-gray-500 dark:text-gray-400"><?php echo e($metrics['completion_rate']); ?>%</span>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="col-span-full">
                <div class="text-center py-12">
                    <i class="fas fa-users text-gray-400 text-6xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Product Managers Found</h3>
                    <p class="text-gray-500 dark:text-gray-400 mb-4">Get started by adding your first product manager.</p>
                    <?php if(auth()->user()->hasPermission('manage_clickup_product_managers')): ?>
                        <a href="<?php echo e(route('clickup.product-managers.create')); ?>"
                           class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600">
                            <i class="fas fa-plus mr-2"></i>
                            Add Product Manager
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <?php if($managers->hasPages()): ?>
        <div class="mt-8">
            <?php echo e($managers->links()); ?>

        </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/clickup/Views/product-managers/index.blade.php ENDPATH**/ ?>