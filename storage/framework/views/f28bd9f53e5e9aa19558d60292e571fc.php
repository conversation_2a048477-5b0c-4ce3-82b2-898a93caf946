<?php $__env->startSection('title', 'Contact Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white"><?php echo e($contact->name); ?></h1>
                <p class="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">Contact details for <?php echo e($business->name); ?></p>
            </div>
            <div class="flex space-x-3">
                <a href="<?php echo e(route('business.contacts.index', $business)); ?>"
                   class="bg-gray-500 dark:bg-gray-600 hover:bg-gray-600 dark:hover:bg-gray-500 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Back to Contacts
                </a>
                <?php if(auth()->user()->hasPermission('manage_businesses')): ?>
                    <a href="<?php echo e(route('business.contacts.edit', [$business, $contact])); ?>" 
                       class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Edit Contact
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Contact Information</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Personal details and contact information.</p>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700">
                <dl>
                    <div class="bg-gray-50 dark:bg-gray-700 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Full name</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                            <?php echo e($contact->name); ?>

                            <?php if($contact->is_primary): ?>
                                <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 transition duration-150 ease-in-out">
                                    Primary Contact
                                </span>
                            <?php endif; ?>
                        </dd>
                    </div>
                    <div class="bg-white dark:bg-gray-800 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Position</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                            <?php echo e($contact->position ?: 'Not specified'); ?>

                        </dd>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Department</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                            <?php echo e($contact->department ?: 'Not specified'); ?>

                        </dd>
                    </div>
                    <div class="bg-white dark:bg-gray-800 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Email address</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                            <?php if($contact->email): ?>
                                <a href="mailto:<?php echo e($contact->email); ?>" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:text-blue-200 dark:hover:text-blue-300">
                                    <?php echo e($contact->email); ?>

                                </a>
                            <?php else: ?>
                                <span class="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Not provided</span>
                            <?php endif; ?>
                        </dd>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Primary Phone</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                            <?php if($contact->phone): ?>
                                <a href="tel:<?php echo e($contact->phone); ?>" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:text-blue-200 dark:hover:text-blue-300">
                                    <?php echo e($contact->formatted_phone); ?>

                                </a>
                            <?php else: ?>
                                <span class="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Not provided</span>
                            <?php endif; ?>
                        </dd>
                    </div>
                    <?php if($contact->phone2): ?>
                        <div class="bg-white dark:bg-gray-800 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Secondary Phone</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                                <a href="tel:<?php echo e($contact->phone2); ?>" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:text-blue-200 dark:hover:text-blue-300">
                                    <?php echo e($contact->formatted_phone2); ?>

                                </a>
                            </dd>
                        </div>
                    <?php endif; ?>
                    <?php if($contact->notes): ?>
                        <div class="bg-white dark:bg-gray-800 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Notes</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                                <?php echo e($contact->notes); ?>

                            </dd>
                        </div>
                    <?php endif; ?>
                </dl>
            </div>
        </div>

        <!-- Contact Actions -->
        <?php if($contact->email || $contact->phone || $contact->phone2): ?>
            <div class="mt-6 bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                <div class="px-4 py-5 sm:px-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Quick Actions</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Contact this person directly.</p>
                </div>
                <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:px-6">
                    <div class="flex flex-wrap gap-4">
                        <?php if($contact->email): ?>
                            <a href="mailto:<?php echo e($contact->email); ?>"
                               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:hover:bg-blue-600 transition duration-150 ease-in-out">
                                <i class="fas fa-envelope mr-2"></i>
                                Send Email
                            </a>
                        <?php endif; ?>
                        <?php if($contact->phone): ?>
                            <a href="tel:<?php echo e($contact->phone); ?>"
                               class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-150 ease-in-out">
                                <i class="fas fa-phone mr-2"></i>
                                Call <?php echo e($contact->formatted_phone); ?>

                            </a>
                        <?php endif; ?>
                        <?php if($contact->phone2): ?>
                            <a href="tel:<?php echo e($contact->phone2); ?>"
                               class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-150 ease-in-out">
                                <i class="fas fa-mobile-alt mr-2"></i>
                                Call <?php echo e($contact->formatted_phone2); ?>

                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Contact Status -->
        <div class="mt-6 bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Contact Status</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Information about this contact's completeness and status.</p>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:px-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="text-2xl font-bold <?php echo e($contact->isComplete() ? 'text-green-600 dark:text-green-400' : 'text-yellow-600'); ?>">
                            <?php echo e($contact->isComplete() ? 'Complete' : 'Incomplete'); ?>

                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Profile Status</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold <?php echo e($contact->is_primary ? 'text-blue-600 dark:text-blue-400' : 'text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500'); ?>">
                            <?php echo e($contact->is_primary ? 'Primary' : 'Secondary'); ?>

                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Contact Type</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                            <?php echo e($contact->created_at->diffForHumans()); ?>

                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Added</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Management Actions -->
        <?php if(auth()->user()->hasPermission('manage_businesses')): ?>
            <div class="mt-6 bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                <div class="px-4 py-5 sm:px-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Management Actions</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Administrative actions for this contact.</p>
                </div>
                <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:px-6">
                    <div class="flex space-x-4">
                        <?php if(!$contact->is_primary): ?>
                            <form method="POST" action="<?php echo e(route('business.contacts.set-primary', [$business, $contact])); ?>" class="inline">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('PATCH'); ?>
                                <button type="submit" 
                                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 dark:hover:bg-green-500 transition duration-150 ease-in-out">
                                    <i class="fas fa-star mr-2"></i>
                                    Set as Primary
                                </button>
                            </form>
                        <?php endif; ?>
                        <form method="POST" action="<?php echo e(route('business.contacts.destroy', [$business, $contact])); ?>" 
                              onsubmit="return confirm('Are you sure you want to delete this contact? This action cannot be undone.')" class="inline">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" 
                                    class="inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 text-sm font-medium rounded-md text-red-700 bg-white dark:bg-gray-800 hover:bg-red-50 transition duration-150 ease-in-out">
                                <i class="fas fa-trash mr-2"></i>
                                Delete Contact
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Timestamps -->
        <div class="mt-6 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-700 rounded-md p-4 transition duration-150 ease-in-out">
            <div class="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                <p><strong>Created:</strong> <?php echo e($contact->created_at->format('M d, Y \a\t g:i A')); ?></p>
                <?php if($contact->updated_at != $contact->created_at): ?>
                    <p><strong>Last updated:</strong> <?php echo e($contact->updated_at->format('M d, Y \a\t g:i A')); ?></p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/business/Views/contacts/show.blade.php ENDPATH**/ ?>