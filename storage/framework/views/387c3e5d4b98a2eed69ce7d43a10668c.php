<?php $__env->startSection('title', 'ClickUp General Settings'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">General Settings</h1>
        <div class="flex space-x-3">
            <a href="<?php echo e(route('clickup.settings.index')); ?>"
               class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-key mr-2"></i>
                API Settings
            </a>
            <a href="<?php echo e(route('clickup.settings.sync')); ?>"
               class="bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-sync mr-2"></i>
                Sync Settings
            </a>
            <a href="<?php echo e(route('clickup.dashboard')); ?>"
               class="bg-gray-600 dark:bg-gray-700 hover:bg-gray-700 dark:hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Dashboard
            </a>
        </div>
    </div>

    <!-- General Settings Form -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700 mb-8">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">General Configuration</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Configure general application settings for ClickUp integration.</p>
        </div>
        <div class="p-6">
            <form method="POST" action="<?php echo e(route('clickup.settings.update-general')); ?>" class="space-y-6">
                <?php echo csrf_field(); ?>

                <!-- Notifications -->
                <div class="space-y-4">
                    <h4 class="text-md font-medium text-gray-900 dark:text-white">Notifications</h4>
                    
                    <div class="flex items-center">
                        <input type="checkbox" name="notifications_enabled" id="notifications_enabled" value="1" 
                               <?php echo e($generalSettings['notifications_enabled'] ? 'checked' : ''); ?>

                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                        <label for="notifications_enabled" class="ml-2 block text-sm text-gray-900 dark:text-white">
                            Enable in-app notifications
                        </label>
                    </div>
                    <p class="text-sm text-gray-500 dark:text-gray-400 ml-6">
                        Show notifications for sync status, errors, and important updates
                    </p>

                    <div class="flex items-center">
                        <input type="checkbox" name="email_notifications" id="email_notifications" value="1" 
                               <?php echo e($generalSettings['email_notifications'] ? 'checked' : ''); ?>

                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                        <label for="email_notifications" class="ml-2 block text-sm text-gray-900 dark:text-white">
                            Enable email notifications
                        </label>
                    </div>
                    <p class="text-sm text-gray-500 dark:text-gray-400 ml-6">
                        Send email notifications for critical errors and sync failures
                    </p>
                </div>

                <!-- Cache Duration -->
                <div>
                    <label for="cache_duration" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Cache Duration (seconds)
                    </label>
                    <select name="cache_duration" id="cache_duration"
                            class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        <option value="60" <?php echo e($generalSettings['cache_duration'] == 60 ? 'selected' : ''); ?>>1 minute</option>
                        <option value="300" <?php echo e($generalSettings['cache_duration'] == 300 ? 'selected' : ''); ?>>5 minutes</option>
                        <option value="900" <?php echo e($generalSettings['cache_duration'] == 900 ? 'selected' : ''); ?>>15 minutes</option>
                        <option value="1800" <?php echo e($generalSettings['cache_duration'] == 1800 ? 'selected' : ''); ?>>30 minutes</option>
                        <option value="3600" <?php echo e($generalSettings['cache_duration'] == 3600 ? 'selected' : ''); ?>>1 hour</option>
                        <option value="7200" <?php echo e($generalSettings['cache_duration'] == 7200 ? 'selected' : ''); ?>>2 hours</option>
                    </select>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        How long to cache API responses and computed data
                    </p>
                    <?php $__errorArgs = ['cache_duration'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Timezone -->
                <div>
                    <label for="timezone" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Default Timezone
                    </label>
                    <select name="timezone" id="timezone"
                            class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        <option value="UTC" <?php echo e($generalSettings['timezone'] == 'UTC' ? 'selected' : ''); ?>>UTC</option>
                        <option value="America/New_York" <?php echo e($generalSettings['timezone'] == 'America/New_York' ? 'selected' : ''); ?>>Eastern Time (ET)</option>
                        <option value="America/Chicago" <?php echo e($generalSettings['timezone'] == 'America/Chicago' ? 'selected' : ''); ?>>Central Time (CT)</option>
                        <option value="America/Denver" <?php echo e($generalSettings['timezone'] == 'America/Denver' ? 'selected' : ''); ?>>Mountain Time (MT)</option>
                        <option value="America/Los_Angeles" <?php echo e($generalSettings['timezone'] == 'America/Los_Angeles' ? 'selected' : ''); ?>>Pacific Time (PT)</option>
                        <option value="Europe/London" <?php echo e($generalSettings['timezone'] == 'Europe/London' ? 'selected' : ''); ?>>London (GMT)</option>
                        <option value="Europe/Paris" <?php echo e($generalSettings['timezone'] == 'Europe/Paris' ? 'selected' : ''); ?>>Paris (CET)</option>
                        <option value="Asia/Tokyo" <?php echo e($generalSettings['timezone'] == 'Asia/Tokyo' ? 'selected' : ''); ?>>Tokyo (JST)</option>
                        <option value="Asia/Shanghai" <?php echo e($generalSettings['timezone'] == 'Asia/Shanghai' ? 'selected' : ''); ?>>Shanghai (CST)</option>
                        <option value="Australia/Sydney" <?php echo e($generalSettings['timezone'] == 'Australia/Sydney' ? 'selected' : ''); ?>>Sydney (AEST)</option>
                    </select>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        Default timezone for displaying dates and times
                    </p>
                    <?php $__errorArgs = ['timezone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="resetToDefaults()" 
                            class="bg-gray-600 dark:bg-gray-700 hover:bg-gray-700 dark:hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Reset to Defaults
                    </button>
                    <button type="submit" 
                            class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-save mr-2"></i>
                        Save General Settings
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- System Information -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">System Information</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Current system configuration and status.</p>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Plugin Version</h4>
                    <div class="text-sm text-gray-900 dark:text-white">1.0.0</div>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">PHP Version</h4>
                    <div class="text-sm text-gray-900 dark:text-white"><?php echo e(PHP_VERSION); ?></div>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Laravel Version</h4>
                    <div class="text-sm text-gray-900 dark:text-white"><?php echo e(app()->version()); ?></div>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Current Timezone</h4>
                    <div class="text-sm text-gray-900 dark:text-white"><?php echo e(config('app.timezone')); ?></div>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Cache Driver</h4>
                    <div class="text-sm text-gray-900 dark:text-white"><?php echo e(config('cache.default')); ?></div>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Queue Driver</h4>
                    <div class="text-sm text-gray-900 dark:text-white"><?php echo e(config('queue.default')); ?></div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Reset to defaults
    window.resetToDefaults = function() {
        document.getElementById('notifications_enabled').checked = true;
        document.getElementById('email_notifications').checked = false;
        document.getElementById('cache_duration').value = '1800';
        document.getElementById('timezone').value = 'UTC';
    };
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/clickup/Views/settings/general.blade.php ENDPATH**/ ?>