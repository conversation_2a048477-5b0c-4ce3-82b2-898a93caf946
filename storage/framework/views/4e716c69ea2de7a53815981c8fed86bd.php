<!-- Sync Settings -->
<div class="mb-8">
    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Sync Settings</h3>
    <p class="text-sm text-gray-500 dark:text-gray-400 mb-6">Configure how data is synchronized between ClickUp and your system.</p>
    
    <form method="POST" action="<?php echo e(route('clickup.settings.update-sync')); ?>" class="space-y-6">
        <?php echo csrf_field(); ?>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Sync Interval -->
            <div>
                <label for="sync_interval" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Sync Interval (seconds)
                </label>
                <select name="sync_interval" id="sync_interval" 
                        class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                    <option value="300" <?php echo e(($syncSettings['sync_interval'] ?? 3600) == 300 ? 'selected' : ''); ?>>5 minutes</option>
                    <option value="900" <?php echo e(($syncSettings['sync_interval'] ?? 3600) == 900 ? 'selected' : ''); ?>>15 minutes</option>
                    <option value="1800" <?php echo e(($syncSettings['sync_interval'] ?? 3600) == 1800 ? 'selected' : ''); ?>>30 minutes</option>
                    <option value="3600" <?php echo e(($syncSettings['sync_interval'] ?? 3600) == 3600 ? 'selected' : ''); ?>>1 hour</option>
                    <option value="7200" <?php echo e(($syncSettings['sync_interval'] ?? 3600) == 7200 ? 'selected' : ''); ?>>2 hours</option>
                    <option value="21600" <?php echo e(($syncSettings['sync_interval'] ?? 3600) == 21600 ? 'selected' : ''); ?>>6 hours</option>
                    <option value="43200" <?php echo e(($syncSettings['sync_interval'] ?? 3600) == 43200 ? 'selected' : ''); ?>>12 hours</option>
                    <option value="86400" <?php echo e(($syncSettings['sync_interval'] ?? 3600) == 86400 ? 'selected' : ''); ?>>24 hours</option>
                </select>
                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">How often to automatically sync data from ClickUp</p>
            </div>
            
            <!-- Auto Sync -->
            <div class="flex items-center">
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="auto_sync" value="1" 
                               <?php echo e(($syncSettings['auto_sync'] ?? false) ? 'checked' : ''); ?>

                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Enable automatic sync</span>
                    </label>
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Automatically sync data at the specified interval</p>
                </div>
            </div>
        </div>
        
        <!-- Sync Options -->
        <div>
            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">What to Sync</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="sync_tasks" value="1" 
                               <?php echo e(($syncSettings['sync_tasks'] ?? true) ? 'checked' : ''); ?>

                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Tasks</span>
                    </label>
                </div>
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="sync_lists" value="1" 
                               <?php echo e(($syncSettings['sync_lists'] ?? true) ? 'checked' : ''); ?>

                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Lists</span>
                    </label>
                </div>
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="sync_comments" value="1" 
                               <?php echo e(($syncSettings['sync_comments'] ?? false) ? 'checked' : ''); ?>

                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Comments</span>
                    </label>
                </div>
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="sync_attachments" value="1" 
                               <?php echo e(($syncSettings['sync_attachments'] ?? false) ? 'checked' : ''); ?>

                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Attachments</span>
                    </label>
                </div>
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="sync_time_tracking" value="1" 
                               <?php echo e(($syncSettings['sync_time_tracking'] ?? false) ? 'checked' : ''); ?>

                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Time Tracking</span>
                    </label>
                </div>
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="sync_custom_fields" value="1" 
                               <?php echo e(($syncSettings['sync_custom_fields'] ?? false) ? 'checked' : ''); ?>

                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Custom Fields</span>
                    </label>
                </div>
            </div>
        </div>
        
        <div class="flex justify-end">
            <button type="submit"
                    class="bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-save mr-2"></i>
                Save Sync Settings
            </button>
        </div>
    </form>
</div>

<!-- API Settings -->
<div class="mb-8">
    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">API Settings</h3>
    <p class="text-sm text-gray-500 dark:text-gray-400 mb-6">Configure API rate limits and performance settings.</p>
    
    <form method="POST" action="<?php echo e(route('clickup.settings.update-general')); ?>" class="space-y-6">
        <?php echo csrf_field(); ?>
        <input type="hidden" name="section" value="api">
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Rate Limit -->
            <div>
                <label for="rate_limit" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Rate Limit (requests per minute)
                </label>
                <input type="number" name="rate_limit" id="rate_limit" 
                       value="<?php echo e($generalSettings['rate_limit'] ?? 100); ?>" min="10" max="1000"
                       class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Maximum API requests per minute (ClickUp limit is 100)</p>
            </div>
            
            <!-- Cache Duration -->
            <div>
                <label for="cache_duration" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Cache Duration (seconds)
                </label>
                <select name="cache_duration" id="cache_duration" 
                        class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                    <option value="300" <?php echo e(($generalSettings['cache_duration'] ?? 1800) == 300 ? 'selected' : ''); ?>>5 minutes</option>
                    <option value="900" <?php echo e(($generalSettings['cache_duration'] ?? 1800) == 900 ? 'selected' : ''); ?>>15 minutes</option>
                    <option value="1800" <?php echo e(($generalSettings['cache_duration'] ?? 1800) == 1800 ? 'selected' : ''); ?>>30 minutes</option>
                    <option value="3600" <?php echo e(($generalSettings['cache_duration'] ?? 1800) == 3600 ? 'selected' : ''); ?>>1 hour</option>
                    <option value="7200" <?php echo e(($generalSettings['cache_duration'] ?? 1800) == 7200 ? 'selected' : ''); ?>>2 hours</option>
                </select>
                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">How long to cache API responses</p>
            </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Debug Mode -->
            <div>
                <label class="flex items-center">
                    <input type="checkbox" name="debug_mode" value="1" 
                           <?php echo e(($generalSettings['debug_mode'] ?? false) ? 'checked' : ''); ?>

                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Enable debug mode</span>
                </label>
                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Log detailed API requests and responses</p>
            </div>
            
            <!-- Webhook Support -->
            <div>
                <label class="flex items-center">
                    <input type="checkbox" name="webhook_enabled" value="1" 
                           <?php echo e(($generalSettings['webhook_enabled'] ?? false) ? 'checked' : ''); ?>

                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Enable webhook support</span>
                </label>
                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Receive real-time updates from ClickUp</p>
            </div>
        </div>
        
        <div class="flex justify-end">
            <button type="submit"
                    class="bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-save mr-2"></i>
                Save API Settings
            </button>
        </div>
    </form>
</div>

<!-- Notification Settings -->
<div class="mb-8">
    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Notification Settings</h3>
    <p class="text-sm text-gray-500 dark:text-gray-400 mb-6">Configure notification preferences for ClickUp integration events.</p>
    
    <form method="POST" action="<?php echo e(route('clickup.settings.update-general')); ?>" class="space-y-6">
        <?php echo csrf_field(); ?>
        <input type="hidden" name="section" value="notifications">
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label class="flex items-center">
                    <input type="checkbox" name="notify_sync_completion" value="1" 
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Notify on sync completion</span>
                </label>
                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Get notified when data sync completes</p>
            </div>
            
            <div>
                <label class="flex items-center">
                    <input type="checkbox" name="notify_sync_errors" value="1" checked
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Notify on sync errors</span>
                </label>
                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Get notified when sync encounters errors</p>
            </div>
            
            <div>
                <label class="flex items-center">
                    <input type="checkbox" name="notify_api_limits" value="1" checked
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Notify on API rate limits</span>
                </label>
                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Get notified when approaching API limits</p>
            </div>
            
            <div>
                <label class="flex items-center">
                    <input type="checkbox" name="notify_new_tasks" value="1" 
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Notify on new tasks</span>
                </label>
                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Get notified when new tasks are created</p>
            </div>
        </div>
        
        <div class="flex justify-end">
            <button type="submit"
                    class="bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-save mr-2"></i>
                Save Notification Settings
            </button>
        </div>
    </form>
</div>

<!-- API Status -->
<?php if($currentToken && isset($apiStatus) && $apiStatus['configured']): ?>
    <div class="mb-8">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">API Status</h3>
        <div class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Rate Limit Status</h4>
                    <?php if($apiStatus['rate_limit_remaining'] !== null): ?>
                        <p class="text-sm text-gray-900 dark:text-white">
                            Remaining: <?php echo e($apiStatus['rate_limit_remaining']); ?> requests
                        </p>
                        <?php if($apiStatus['rate_limit_reset_at']): ?>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                Resets: <?php echo e(\Carbon\Carbon::parse($apiStatus['rate_limit_reset_at'])->diffForHumans()); ?>

                            </p>
                        <?php endif; ?>
                    <?php else: ?>
                        <p class="text-sm text-gray-500 dark:text-gray-400">No rate limit data available</p>
                    <?php endif; ?>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Last Activity</h4>
                    <?php if($apiStatus['last_used_at']): ?>
                        <p class="text-sm text-gray-900 dark:text-white">
                            <?php echo e(\Carbon\Carbon::parse($apiStatus['last_used_at'])->diffForHumans()); ?>

                        </p>
                    <?php else: ?>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Never used</p>
                    <?php endif; ?>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Connection Status</h4>
                    <div class="flex items-center">
                        <div class="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                        <span class="text-sm text-gray-900 dark:text-white">Connected</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>
<?php /**PATH /Users/<USER>/Herd/business/plugins/clickup/Views/settings/tabs/general.blade.php ENDPATH**/ ?>