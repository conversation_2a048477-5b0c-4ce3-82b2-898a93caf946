<?php
    $hasChildren = isset($item['filtered_children']) && count($item['filtered_children']) > 0;
    $isActive = false;
    
    // Determine if this item is active
    if (isset($item['route']) && $item['route']) {
        $isActive = request()->routeIs($item['route'] . '*');
    } elseif (isset($item['url']) && $item['url']) {
        $isActive = request()->is(ltrim($item['url'], '/') . '*');
    }
    
    // Check if any children are active
    $hasActiveChild = false;
    if ($hasChildren) {
        foreach ($item['filtered_children'] as $child) {
            if (isset($child['route']) && $child['route'] && request()->routeIs($child['route'] . '*')) {
                $hasActiveChild = true;
                break;
            } elseif (isset($child['url']) && $child['url'] && request()->is(ltrim($child['url'], '/') . '*')) {
                $hasActiveChild = true;
                break;
            }
        }
    }
    
    $isExpanded = $isActive || $hasActiveChild;
?>

<?php if($item['type'] === 'separator'): ?>
    <!-- Separator -->
    <div class="border-t border-gray-200 dark:border-gray-700 my-4"></div>
    <?php if($item['label']): ?>
        <h3 class="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
            <?php echo e($item['label']); ?>

        </h3>
    <?php endif; ?>
<?php else: ?>
    <?php if($hasChildren): ?>
        <!-- Parent item with children -->
        <div class="space-y-1">
            <button type="button"
                    onclick="toggleSubmenu('<?php echo e($item['name']); ?>')"
                    class="w-full flex items-center justify-between px-2 py-2 text-sm font-medium rounded-md <?php echo e($isActive || $hasActiveChild ? 'bg-primary-100 dark:bg-primary-900 text-primary-900 dark:text-primary-100' : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white'); ?> transition duration-150 ease-in-out">
                <div class="flex items-center">
                    <?php if($item['icon']): ?>
                        <i class="<?php echo e($item['icon']); ?> mr-3 <?php echo e($isActive || $hasActiveChild ? 'text-primary-500 dark:text-primary-400' : 'text-gray-400 dark:text-gray-300'); ?> group-hover:text-gray-500 dark:group-hover:text-gray-200"></i>
                    <?php endif; ?>
                    <?php echo e($item['label']); ?>

                </div>
                <i id="<?php echo e($item['name']); ?>-menu-icon" class="fas fa-chevron-right transition-transform duration-200 <?php echo e($isExpanded ? 'transform rotate-90' : ''); ?>"></i>
            </button>

            <!-- Submenu -->
            <div id="<?php echo e($item['name']); ?>-submenu" class="ml-6 space-y-1 <?php echo e($isExpanded ? '' : 'hidden'); ?>">
                <?php $__currentLoopData = $item['filtered_children']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php echo $__env->make('partials.navigation-item', ['item' => $child], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    <?php else: ?>
        <!-- Regular link item -->
        <?php
            $href = '#';
            if (isset($item['route']) && $item['route']) {
                $href = route($item['route']);
            } elseif (isset($item['url']) && $item['url']) {
                $href = url($item['url']);
            }
            
            $target = $item['target'] ?? '_self';
        ?>
        
        <a href="<?php echo e($href); ?>" 
           target="<?php echo e($target); ?>"
           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md <?php echo e($isActive ? 'bg-primary-100 dark:bg-primary-900 text-primary-900 dark:text-primary-100' : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white'); ?> transition duration-150 ease-in-out">
            <?php if($item['icon']): ?>
                <i class="<?php echo e($item['icon']); ?> mr-3 <?php echo e($isActive ? 'text-primary-500 dark:text-primary-400' : 'text-gray-400 dark:text-gray-300'); ?> group-hover:text-gray-500 dark:group-hover:text-gray-200"></i>
            <?php endif; ?>
            <?php echo e($item['label']); ?>

        </a>
    <?php endif; ?>
<?php endif; ?>
<?php /**PATH /Users/<USER>/Herd/business/resources/views/partials/navigation-item.blade.php ENDPATH**/ ?>