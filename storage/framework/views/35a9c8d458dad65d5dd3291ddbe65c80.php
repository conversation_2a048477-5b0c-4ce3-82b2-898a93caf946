<?php $__env->startSection('title', 'Product Manager Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white"><?php echo e($manager->name); ?></h1>
        <div class="flex space-x-3">
            <?php if(auth()->user()->hasPermission('manage_clickup_product_managers')): ?>
                <a href="<?php echo e(route('clickup.product-managers.edit', $manager)); ?>"
                   class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-edit mr-2"></i>
                    Edit
                </a>
            <?php endif; ?>
            <a href="<?php echo e(route('clickup.product-managers.index')); ?>"
               class="bg-gray-600 dark:bg-gray-700 hover:bg-gray-700 dark:hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Product Managers
            </a>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if(session('success')): ?>
        <div class="bg-green-100 dark:bg-green-900 border border-green-400 dark:border-green-600 text-green-700 dark:text-green-300 px-4 py-3 rounded mb-6">
            <i class="fas fa-check-circle mr-2"></i>
            <?php echo e(session('success')); ?>

        </div>
    <?php endif; ?>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Manager Information -->
        <div class="lg:col-span-2">
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700 mb-6">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Manager Information</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Name</label>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($manager->name); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Email</label>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white">
                                <a href="mailto:<?php echo e($manager->email); ?>" class="text-blue-600 dark:text-blue-400 hover:underline">
                                    <?php echo e($manager->email); ?>

                                </a>
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Role</label>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($manager->role ?: 'Not specified'); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Department</label>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($manager->department ?: 'Not specified'); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Phone</label>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white">
                                <?php if($manager->phone): ?>
                                    <a href="tel:<?php echo e($manager->phone); ?>" class="text-blue-600 dark:text-blue-400 hover:underline">
                                        <?php echo e($manager->phone); ?>

                                    </a>
                                <?php else: ?>
                                    Not specified
                                <?php endif; ?>
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Timezone</label>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($manager->timezone ?: 'UTC'); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Status</label>
                            <p class="mt-1">
                                <?php if($manager->is_active): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                                        <i class="fas fa-check-circle mr-1"></i>
                                        Active
                                    </span>
                                <?php else: ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200">
                                        <i class="fas fa-times-circle mr-1"></i>
                                        Inactive
                                    </span>
                                <?php endif; ?>
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Associated User</label>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white">
                                <?php if($manager->user): ?>
                                    <?php echo e($manager->user->name); ?>

                                <?php else: ?>
                                    Standalone manager
                                <?php endif; ?>
                            </p>
                        </div>
                        <?php if($manager->notes): ?>
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Notes</label>
                                <p class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($manager->notes); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Assigned Lists -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Assigned Lists</h3>
                </div>
                <div class="p-6">
                    <?php if($manager->clickupLists && $manager->clickupLists->count() > 0): ?>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <?php $__currentLoopData = $manager->clickupLists; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $list): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                                    <div class="flex items-center justify-between">
                                        <h4 class="font-medium text-gray-900 dark:text-white"><?php echo e($list->name); ?></h4>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium" 
                                              style="background-color: <?php echo e($list->color); ?>20; color: <?php echo e($list->color); ?>;">
                                            <?php echo e(ucfirst($list->status)); ?>

                                        </span>
                                    </div>
                                    <?php if($list->description): ?>
                                        <p class="text-sm text-gray-500 dark:text-gray-400 mt-2"><?php echo e($list->description); ?></p>
                                    <?php endif; ?>
                                    <div class="flex items-center justify-between mt-3 text-xs text-gray-500 dark:text-gray-400">
                                        <span><?php echo e($list->tasks_count ?? 0); ?> tasks</span>
                                        <a href="<?php echo e(route('clickup.lists.show', $list)); ?>" 
                                           class="text-blue-600 dark:text-blue-400 hover:underline">
                                            View Details
                                        </a>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-8">
                            <i class="fas fa-list-ul text-4xl text-gray-400 dark:text-gray-600 mb-4"></i>
                            <p class="text-gray-500 dark:text-gray-400">No lists assigned to this product manager yet.</p>
                            <?php if(auth()->user()->hasPermission('manage_clickup_settings')): ?>
                                <a href="<?php echo e(route('clickup.settings.assignments')); ?>" 
                                   class="mt-4 inline-flex items-center text-blue-600 dark:text-blue-400 hover:underline">
                                    <i class="fas fa-plus mr-2"></i>
                                    Assign Lists
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Performance Metrics & Actions -->
        <div class="lg:col-span-1">
            <!-- Performance Metrics -->
            <?php if(isset($metrics) && !empty($metrics)): ?>
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700 mb-6">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Performance Metrics</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <?php $__currentLoopData = $metrics; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500 dark:text-gray-400"><?php echo e(ucwords(str_replace('_', ' ', $key))); ?></span>
                                    <span class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e($value); ?></span>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Quick Actions -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700 mb-6">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Quick Actions</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-3">
                        <?php if(auth()->user()->hasPermission('manage_clickup_settings')): ?>
                            <a href="<?php echo e(route('clickup.settings.assignments')); ?>" 
                               class="w-full bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out text-center block">
                                <i class="fas fa-tasks mr-2"></i>
                                Manage List Assignments
                            </a>
                        <?php endif; ?>
                        
                        <a href="mailto:<?php echo e($manager->email); ?>" 
                           class="w-full bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out text-center block">
                            <i class="fas fa-envelope mr-2"></i>
                            Send Email
                        </a>
                        
                        <?php if($manager->phone): ?>
                            <a href="tel:<?php echo e($manager->phone); ?>" 
                               class="w-full bg-purple-600 dark:bg-purple-700 hover:bg-purple-700 dark:hover:bg-purple-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out text-center block">
                                <i class="fas fa-phone mr-2"></i>
                                Call
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Manager Details -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Details</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-3 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-500 dark:text-gray-400">Created</span>
                            <span class="text-gray-900 dark:text-white"><?php echo e($manager->created_at->format('M j, Y')); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-500 dark:text-gray-400">Last Updated</span>
                            <span class="text-gray-900 dark:text-white"><?php echo e($manager->updated_at->format('M j, Y')); ?></span>
                        </div>
                        <?php if($manager->creator): ?>
                            <div class="flex justify-between">
                                <span class="text-gray-500 dark:text-gray-400">Created By</span>
                                <span class="text-gray-900 dark:text-white"><?php echo e($manager->creator->name); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/clickup/Views/product-managers/show.blade.php ENDPATH**/ ?>