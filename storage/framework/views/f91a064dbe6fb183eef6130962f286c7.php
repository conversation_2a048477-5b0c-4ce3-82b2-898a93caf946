<style>
    .list-item {
        transition: all 0.3s ease;
    }

    .list-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .dark .list-item:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    /* Assigned list styling */
    .list-assigned {
        background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
        border-color: #16a34a !important;
    }

    .dark .list-assigned {
        background: linear-gradient(135deg, #14532d 0%, #166534 100%);
        border-color: #22c55e !important;
    }

    .assignment-badge {
        animation: fadeIn 0.5s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>

<?php if(!$currentToken): ?>
    <div class="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-md p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas fa-exclamation-triangle text-yellow-400"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                    API Token Required
                </h3>
                <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                    <p>Please configure your ClickUp API token in the Workspace tab to manage lists.</p>
                </div>
            </div>
        </div>
    </div>
<?php else: ?>
    <!-- Lists Configuration -->
    <div class="mb-8">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Lists Configuration</h3>
        <p class="text-sm text-gray-500 dark:text-gray-400 mb-6">Configure and manage your ClickUp lists, including list organization and mapping to internal systems.</p>

        <!-- Configuration Status -->
        <div class="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-md p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-info-circle text-blue-400"></i>
                </div>
                <div class="ml-3">
                    <h4 class="text-sm font-medium text-blue-800 dark:text-blue-200">Current Configuration</h4>
                    <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                        <p>Lists found: <?php echo e($lists ? $lists->count() : 0); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <?php if(!empty($lists)): ?>
            <!-- Lists Management -->
            <div class="space-y-6">
                <!-- Search and Filter -->
                <div class="flex flex-col sm:flex-row gap-4">
                    <div class="flex-1">
                        <input type="text" id="list-search" placeholder="Search lists, workspaces, spaces, folders..."
                               class="w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div class="flex space-x-2">
                        <select id="space-filter" class="border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                            <option value="">All Spaces</option>
                            <?php if($lists && $lists->count() > 0): ?>
                                <?php $__currentLoopData = $lists->groupBy('space_name'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $spaceName => $spaceList): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($spaceName); ?>"><?php echo e($spaceName); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php endif; ?>
                        </select>
                        <button type="button" 
                                class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out"
                                onclick="refreshLists()">
                            <i class="fas fa-sync mr-2"></i>
                            Refresh
                        </button>
                    </div>
                </div>

                <!-- Lists Grid -->
                <div id="lists-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <?php $__currentLoopData = $lists; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $list): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php
                            // Check if this list is already assigned
                            $assignment = collect($assignedLists)->firstWhere('clickup_list_id', $list['id']);
                            $isAssigned = !is_null($assignment);
                        ?>
                        <div class="list-item bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:shadow-md transition duration-150 ease-in-out <?php echo e($isAssigned ? 'list-assigned border-green-300 dark:border-green-600' : ''); ?>"
                             data-list-id="<?php echo e($list['id']); ?>"
                             data-space="<?php echo e($list['space_name'] ?? ''); ?>"
                             data-name="<?php echo e(strtolower($list['name'])); ?>"
                             data-full-path="<?php echo e(strtolower($list['full_path'] ?? '')); ?>"
                             data-workspace="<?php echo e($list['workspace_name'] ?? ''); ?>"
                             data-folder="<?php echo e($list['folder_name'] ?? ''); ?>"
                             data-assignment="<?php echo e($isAssigned ? 'assigned' : 'unassigned'); ?>">
                            <div class="list-card-header flex items-start justify-between mb-3">
                                <div class="flex-1">
                                    <h4 class="text-lg font-medium text-gray-900 dark:text-white truncate"><?php echo e($list['name']); ?></h4>
                                    <p class="text-sm text-gray-500 dark:text-gray-400" title="<?php echo e($list['full_path'] ?? 'Unknown Path'); ?>">
                                        <i class="fas fa-sitemap mr-1"></i>
                                        <?php echo e($list['full_path'] ?? ($list['workspace_name'] ?? 'Unknown Workspace') . ' > ' . ($list['space_name'] ?? 'Unknown Space')); ?>

                                    </p>
                                    <?php if(isset($list['folder_name'])): ?>
                                        <p class="text-xs text-blue-600 dark:text-blue-400">
                                            <i class="fas fa-folder mr-1"></i>
                                            Folder: <?php echo e($list['folder_name']); ?>

                                        </p>
                                    <?php endif; ?>
                                    <p class="text-xs text-gray-400 dark:text-gray-500">ID: <?php echo e($list['id']); ?></p>

                                    <?php if($isAssigned): ?>
                                        <div class="assignment-badge inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 mt-2">
                                            <i class="fas fa-user-check mr-1"></i>
                                            Assigned to: <?php echo e($assignment['assigned_to_contact_name']); ?>

                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="flex-shrink-0 flex flex-col space-y-2">
                                    <?php if(isset($list['status']) && $list['status'] === 'active'): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                                            Active
                                        </span>
                                    <?php else: ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
                                            Inactive
                                        </span>
                                    <?php endif; ?>

                                    <?php if($isAssigned): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                                            <i class="fas fa-check mr-1"></i>
                                            Assigned
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <?php if(isset($list['task_count'])): ?>
                                <div class="mb-3">
                                    <p class="text-sm text-gray-600 dark:text-gray-400">
                                        <i class="fas fa-tasks mr-1"></i>
                                        <?php echo e($list['task_count']); ?> tasks
                                    </p>
                                </div>
                            <?php endif; ?>
                            
                            <div class="flex space-x-2">
                                <button type="button"
                                        class="flex-1 bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-3 rounded text-sm transition duration-150 ease-in-out"
                                        onclick="viewListDetails('<?php echo e($list['id']); ?>')">
                                    <i class="fas fa-eye mr-1"></i>
                                    View
                                </button>

                                <?php if($isAssigned): ?>
                                    <button type="button"
                                            class="assign-button flex-1 bg-green-600 dark:bg-green-700 text-white font-bold py-2 px-3 rounded text-sm cursor-not-allowed opacity-75"
                                            disabled
                                            title="Already assigned to <?php echo e($assignment['assigned_to_contact_name']); ?>">
                                        <i class="fas fa-check mr-1"></i>
                                        Assigned
                                    </button>
                                <?php else: ?>
                                    <button type="button"
                                            class="assign-button flex-1 bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-bold py-2 px-3 rounded text-sm transition duration-150 ease-in-out"
                                            onclick="assignList('<?php echo e($list['id']); ?>', '<?php echo e($list['name']); ?>')">
                                        <i class="fas fa-plus mr-1"></i>
                                        Assign
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        <?php else: ?>
            <div class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-8 text-center">
                <div class="flex flex-col items-center">
                    <div class="w-16 h-16 bg-gray-100 dark:bg-gray-600 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-list text-gray-400 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Lists Found</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
                        No lists were found. Please check your configuration:
                    </p>
                    <div class="text-left text-sm text-gray-600 dark:text-gray-400 mb-4">
                        <p><strong>1.</strong> Verify API token is configured in the Workspace tab</p>
                        <p><strong>2.</strong> Ensure a workspace is selected</p>
                        <p><strong>3.</strong> Check that spaces are available in your workspace</p>
                        <p><strong>4.</strong> Use the Debug button below for detailed information</p>
                    </div>
                    <div class="flex space-x-2">
                        <button type="button"
                                class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out"
                                onclick="refreshLists()">
                            <i class="fas fa-sync mr-2"></i>
                            Refresh Lists
                        </button>
                        <button type="button"
                                class="bg-yellow-600 dark:bg-yellow-700 hover:bg-yellow-700 dark:hover:bg-yellow-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out"
                                onclick="debugLists()">
                            <i class="fas fa-bug mr-2"></i>
                            Debug
                        </button>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Assigned Lists -->
    <div class="mb-8">
        <div class="flex items-center justify-between mb-4">
            <div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                    Assigned Lists
                    <?php if(!empty($assignedLists)): ?>
                        <span class="text-sm text-gray-500 dark:text-gray-400 ml-2">(<?php echo e(count($assignedLists)); ?>)</span>
                    <?php endif; ?>
                </h3>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Lists that are currently assigned to contacts. Click to expand details.</p>
            </div>
            <?php if(!empty($assignedLists)): ?>
                <div class="flex space-x-2">
                    <button type="button"
                            onclick="expandAllAssignments()"
                            class="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 px-2 py-1 rounded border border-blue-200 dark:border-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900 transition duration-150 ease-in-out">
                        <i class="fas fa-expand-alt mr-1"></i>
                        Expand All
                    </button>
                    <button type="button"
                            onclick="collapseAllAssignments()"
                            class="text-xs text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 px-2 py-1 rounded border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-900 transition duration-150 ease-in-out">
                        <i class="fas fa-compress-alt mr-1"></i>
                        Collapse All
                    </button>
                </div>
            <?php endif; ?>
        </div>
        
        <?php if(!empty($assignedLists)): ?>
            <div class="space-y-2">
                <?php $__currentLoopData = $assignedLists; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $assignedList): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php
                        $assignmentId = 'assignment-' . $index;
                        $fullPath = [];

                        // Add space name
                        if (!empty($assignedList['clickup_space_name'])) {
                            $fullPath[] = $assignedList['clickup_space_name'];
                        }

                        // Add folder name from metadata if available
                        if (!empty($assignedList['metadata']['list_data']['folder']['name'])) {
                            $fullPath[] = $assignedList['metadata']['list_data']['folder']['name'];
                        }

                        // Add list name
                        if (!empty($assignedList['clickup_list_name'])) {
                            $fullPath[] = $assignedList['clickup_list_name'];
                        }

                        $pathString = implode(' > ', $fullPath);
                    ?>

                    <div class="bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-lg overflow-hidden">
                        <!-- Collapsible Header -->
                        <div class="p-3 cursor-pointer hover:bg-green-100 dark:hover:bg-green-800 transition duration-150 ease-in-out"
                             onclick="toggleAssignment('<?php echo e($assignmentId); ?>')">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center flex-1">
                                    <div class="flex-shrink-0">
                                        <div class="w-6 h-6 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center">
                                            <i class="fas fa-check text-green-600 dark:text-green-400 text-xs"></i>
                                        </div>
                                    </div>
                                    <div class="ml-3 flex-1">
                                        <h4 class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e($assignedList['clickup_list_name'] ?? 'Unknown List'); ?></h4>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">
                                            <?php echo e($assignedList['assigned_to_contact_name'] ?? 'System'); ?>

                                        </p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="text-xs text-gray-400">
                                        <i id="<?php echo e($assignmentId); ?>-icon" class="fas fa-chevron-down transition-transform duration-200"></i>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Collapsible Content -->
                        <div id="<?php echo e($assignmentId); ?>-content" class="hidden border-t border-green-200 dark:border-green-700 bg-green-25 dark:bg-green-950">
                            <div class="p-4">
                                <!-- Full Path -->
                                <div class="mb-3">
                                    <label class="text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Full Path</label>
                                    <p class="text-sm text-blue-600 dark:text-blue-400 mt-1">
                                        <i class="fas fa-sitemap mr-1"></i><?php echo e($pathString); ?>

                                    </p>
                                </div>

                                <!-- Assignment Details -->
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                    <div>
                                        <label class="text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Assigned To</label>
                                        <p class="text-sm text-gray-900 dark:text-white mt-1"><?php echo e($assignedList['assigned_to_contact_name'] ?? 'System'); ?></p>
                                    </div>
                                    <div>
                                        <label class="text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Assigned Date</label>
                                        <p class="text-sm text-gray-900 dark:text-white mt-1">
                                            <?php echo e(isset($assignedList['assigned_at']) ? \Carbon\Carbon::parse($assignedList['assigned_at'])->format('M j, Y g:i A') : 'Unknown'); ?>

                                        </p>
                                    </div>
                                </div>

                                <!-- Actions -->
                                <div class="flex justify-between items-center pt-3 border-t border-green-200 dark:border-green-700">
                                    <a href="https://app.clickup.com/t/<?php echo e($assignedList['clickup_list_id']); ?>"
                                       target="_blank"
                                       class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm">
                                        <i class="fas fa-external-link-alt mr-1"></i>
                                        Open in ClickUp
                                    </a>
                                    <button type="button"
                                            class="bg-red-600 dark:bg-red-700 hover:bg-red-700 dark:hover:bg-red-600 text-white font-bold py-1 px-3 rounded text-sm transition duration-150 ease-in-out"
                                            onclick="unassignList('<?php echo e($assignedList['clickup_list_id']); ?>')">
                                        <i class="fas fa-times mr-1"></i>
                                        Unassign
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php else: ?>
            <div class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-6 text-center">
                <p class="text-sm text-gray-500 dark:text-gray-400">No lists are currently assigned.</p>
            </div>
        <?php endif; ?>
    </div>

    <!-- List Configuration Options -->
    <div class="mb-8">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">List Configuration</h3>
        <p class="text-sm text-gray-500 dark:text-gray-400 mb-6">Configure how lists are managed and synchronized.</p>
        
        <form method="POST" action="<?php echo e(route('clickup.settings.update-general')); ?>" class="space-y-6">
            <?php echo csrf_field(); ?>
            <input type="hidden" name="section" value="lists">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="auto_sync_lists" value="1" 
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Auto-sync list changes</span>
                    </label>
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Automatically sync when lists are modified in ClickUp</p>
                </div>
                
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="sync_list_members" value="1" 
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Sync list member assignments</span>
                    </label>
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Keep list member assignments synchronized</p>
                </div>
                
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="track_list_performance" value="1" 
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Track list performance</span>
                    </label>
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Enable performance tracking for lists</p>
                </div>
                
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="notify_list_changes" value="1" 
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded">
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Notify on list changes</span>
                    </label>
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Send notifications when lists are modified</p>
                </div>
            </div>
            
            <div class="flex justify-end">
                <button type="submit"
                        class="bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-save mr-2"></i>
                    Save List Settings
                </button>
            </div>
        </form>
    </div>
<?php endif; ?>

<!-- Assignment Modal -->
<div id="assignment-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-md shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white" id="assignment-modal-title">Assign List</h3>
                <button type="button" id="close-assignment-modal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="assignment-form">
                <input type="hidden" id="assignment-list-id" name="list_id">
                <input type="hidden" id="selected-contact-id" name="contact_id">
                <div class="mb-4">
                    <label for="contact-search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Search Contact (Arabic/English)
                    </label>
                    <div class="relative">
                        <input type="text" id="contact-search" placeholder="Search by name in Arabic or English..."
                               class="w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 pl-10"
                               autocomplete="off" dir="auto"
                               oninput="handleContactSearchInline(this)"
                               onkeyup="handleContactSearchInline(this)">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <div id="contact-results" class="hidden absolute z-50 w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-y-auto mt-1 left-0 right-0">
                            <!-- Search results will appear here -->
                        </div>
                    </div>
                    <div id="selected-contact-display" class="hidden mt-2 p-2 bg-blue-50 dark:bg-blue-900 rounded border">
                        <span class="text-sm text-blue-800 dark:text-blue-200">Selected: </span>
                        <span id="selected-contact-name" class="font-medium text-blue-900 dark:text-blue-100"></span>
                        <button type="button" id="clear-selection" class="ml-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="mt-2 flex flex-wrap gap-2">
                        <button type="button" id="test-search" class="text-xs text-gray-500 hover:text-gray-700">
                            🔧 Test Search
                        </button>
                        <button type="button" id="test-contacts-api" class="text-xs text-blue-500 hover:text-blue-700">
                            📡 Test API
                        </button>
                        <button type="button" id="debug-search" class="text-xs text-red-500 hover:text-red-700">
                            🐛 Full Debug
                        </button>
                        <button type="button" id="force-search" class="text-xs text-green-500 hover:text-green-700">
                            🚀 Force Search
                        </button>
                        <button type="button" id="test-full-flow" class="text-xs text-purple-500 hover:text-purple-700">
                            🔄 Test Full Flow
                        </button>
                    </div>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" id="cancel-assignment"
                            class="bg-gray-500 dark:bg-gray-600 hover:bg-gray-600 dark:hover:bg-gray-500 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Cancel
                    </button>
                    <button type="submit"
                            class="bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Assign
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- List Details Modal -->
<div id="list-details-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white" id="list-details-modal-title">List Details</h3>
                <button type="button" id="close-list-details-modal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="list-details-content" class="text-gray-700 dark:text-gray-300">
                Loading...
            </div>
        </div>
    </div>
</div>

<script>
// List search functionality
document.getElementById('list-search')?.addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const listItems = document.querySelectorAll('.list-item');

    listItems.forEach(item => {
        const name = item.getAttribute('data-name');
        const fullPath = item.getAttribute('data-full-path');
        const workspace = item.getAttribute('data-workspace');
        const folder = item.getAttribute('data-folder');

        // Search in name, full path, workspace, and folder
        if (name.includes(searchTerm) ||
            fullPath.includes(searchTerm) ||
            workspace.toLowerCase().includes(searchTerm) ||
            folder.toLowerCase().includes(searchTerm)) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
});

// Space filter functionality
document.getElementById('space-filter')?.addEventListener('change', function() {
    const selectedSpace = this.value;
    const listItems = document.querySelectorAll('.list-item');
    
    listItems.forEach(item => {
        const space = item.getAttribute('data-space');
        if (!selectedSpace || space === selectedSpace) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
});

function viewListDetails(listId) {
    // Show loading state
    document.getElementById('list-details-content').innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin mr-2"></i>Loading list details...</div>';
    document.getElementById('list-details-modal').classList.remove('hidden');

    // Fetch list details from API
    fetch(`/clickup/api/lists/${listId}`, {
        method: 'GET',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayListDetails(data.list);
        } else {
            document.getElementById('list-details-content').innerHTML = `<div class="text-red-600">Error: ${data.message}</div>`;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById('list-details-content').innerHTML = '<div class="text-red-600">Failed to load list details</div>';
    });
}

function displayListDetails(list) {
    const content = `
        <div class="space-y-4">
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 class="font-medium text-gray-900 dark:text-white mb-2">${list.name}</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                        <p class="text-gray-600 dark:text-gray-400"><strong>List ID:</strong> ${list.id}</p>
                        <p class="text-gray-600 dark:text-gray-400"><strong>Space:</strong> ${list.space?.name || 'Unknown'}</p>
                        <p class="text-gray-600 dark:text-gray-400"><strong>Folder:</strong> ${list.folder?.name || 'No folder'}</p>
                    </div>
                    <div>
                        <p class="text-gray-600 dark:text-gray-400"><strong>Status:</strong> ${list.status || 'Unknown'}</p>
                        <p class="text-gray-600 dark:text-gray-400"><strong>Task Count:</strong> ${list.task_count || 0}</p>
                        <p class="text-gray-600 dark:text-gray-400"><strong>Archived:</strong> ${list.archived ? 'Yes' : 'No'}</p>
                    </div>
                </div>
                ${list.content ? `<div class="mt-3"><p class="text-gray-600 dark:text-gray-400"><strong>Description:</strong></p><p class="text-sm">${list.content}</p></div>` : ''}
            </div>

            <div class="bg-blue-50 dark:bg-blue-900 rounded-lg p-4">
                <h5 class="font-medium text-blue-900 dark:text-blue-200 mb-2">Quick Actions</h5>
                <div class="flex space-x-2">
                    <button onclick="assignListFromDetails('${list.id}', '${list.name}')"
                            class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm">
                        <i class="fas fa-user-plus mr-1"></i>Assign to Contact
                    </button>
                    <a href="https://app.clickup.com/t/${list.id}" target="_blank"
                       class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm inline-block">
                        <i class="fas fa-external-link-alt mr-1"></i>Open in ClickUp
                    </a>
                </div>
            </div>
        </div>
    `;
    document.getElementById('list-details-content').innerHTML = content;
}

function assignList(listId, listName) {
    console.log('🚀 Assign list called:', listId, listName);

    // Set modal title and list ID
    document.getElementById('assignment-modal-title').textContent = `Assign "${listName}"`;
    document.getElementById('assignment-list-id').value = listId;

    // Clear previous selection
    clearContactSelection();

    // Show modal first
    document.getElementById('assignment-modal').classList.remove('hidden');

    // Reset search state
    allContacts = [];
    searchSetupComplete = false;

    // Load contacts for search
    loadContactsForSearch();
}

function assignListFromDetails(listId, listName) {
    // Close details modal first
    document.getElementById('list-details-modal').classList.add('hidden');

    // Then open assignment modal
    assignList(listId, listName);
}

let allContacts = [];
let searchTimeout = null;
let searchSetupComplete = false;

function loadContactsForSearch() {
    console.log('🔄 Loading contacts for search...');
    console.log('🔍 Current allContacts state:', allContacts.length);

    // Show loading state
    const searchInput = document.getElementById('contact-search');
    if (searchInput) {
        searchInput.placeholder = 'Loading contacts...';
        searchInput.disabled = true;
        console.log('✅ Search input found and disabled');
    } else {
        console.error('❌ Search input not found!');
    }

    // Get the base URL dynamically
    const baseUrl = window.location.origin;
    const apiUrl = `${baseUrl}/clickup/api/contacts`;
    console.log('🌐 API URL:', apiUrl);

    // Load all contacts once for searching
    fetch(apiUrl, {
        method: 'GET',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    })
    .then(response => {
        console.log('📡 Response status:', response.status);
        console.log('📡 Response ok:', response.ok);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('📋 Contacts API response:', data);
        console.log('📋 Response type:', typeof data);
        console.log('📋 Response keys:', Object.keys(data));

        if (data.success && data.contacts) {
            allContacts = data.contacts;
            console.log('✅ Loaded contacts:', allContacts.length);
            console.log('👤 Sample contact:', allContacts[0]);
            console.log('👤 Sample contact keys:', allContacts[0] ? Object.keys(allContacts[0]) : 'No contacts');

            // Verify contacts are properly stored
            window.debugContacts = allContacts; // Make available globally for debugging

            // Enable search input
            if (searchInput) {
                searchInput.placeholder = 'Search by name in Arabic or English...';
                searchInput.disabled = false;
                console.log('✅ Search input enabled');
            }

            setupContactSearch();

            // Test search immediately with a known contact
            if (allContacts.length > 0) {
                console.log('🧪 Testing search with first contact name...');
                const testName = allContacts[0].name;
                console.log('🧪 Test search term:', testName);
                setTimeout(() => {
                    searchContacts(testName.substring(0, 3)); // Search with first 3 characters
                }, 1000);
            }
        } else {
            console.error('❌ Failed to load contacts:', data);
            console.error('❌ Data success:', data.success);
            console.error('❌ Data contacts:', data.contacts);
            if (searchInput) {
                searchInput.placeholder = 'Failed to load contacts - Check console';
                searchInput.disabled = false;
            }
        }
    })
    .catch(error => {
        console.error('💥 Error loading contacts:', error);
        console.error('💥 Error details:', error.message);
        if (searchInput) {
            searchInput.placeholder = 'Error loading contacts - Check console';
            searchInput.disabled = false;
        }
    });
}

function setupContactSearch() {
    console.log('🔧 Setting up contact search with bulletproof approach...');

    // Use multiple attempts to ensure the search works
    let attempts = 0;
    const maxAttempts = 10;

    function attemptSetup() {
        attempts++;
        console.log(`🔄 Setup attempt ${attempts}/${maxAttempts}`);

        const searchInput = document.getElementById('contact-search');
        if (!searchInput) {
            console.error(`❌ Search input not found on attempt ${attempts}`);
            if (attempts < maxAttempts) {
                setTimeout(attemptSetup, 200);
            }
            return;
        }

        console.log('✅ Search input found, setting up events...');

        // Clear any existing value and enable input
        searchInput.value = '';
        searchInput.placeholder = 'Search by name in Arabic or English...';
        searchInput.disabled = false;

        // Define the search handler function
        function handleSearch(e) {
            const query = e.target.value.trim();
            console.log('🎯 SEARCH TRIGGERED!', {
                event: e.type,
                query: query,
                timestamp: new Date().toISOString()
            });

            // Clear previous timeout
            if (window.searchTimeout) {
                clearTimeout(window.searchTimeout);
            }

            // Debounce search
            window.searchTimeout = setTimeout(() => {
                if (query.length >= 1) {
                    console.log('🔍 Executing search for:', query);
                    searchContacts(query);
                } else {
                    console.log('🚫 Query too short, hiding results');
                    hideSearchResults();
                }
            }, 300);
        }

        // Method 1: Direct property assignment
        searchInput.oninput = handleSearch;
        searchInput.onkeyup = handleSearch;
        searchInput.onchange = handleSearch;

        // Method 2: addEventListener (with removal first)
        searchInput.removeEventListener('input', handleSearch);
        searchInput.removeEventListener('keyup', handleSearch);
        searchInput.removeEventListener('change', handleSearch);

        searchInput.addEventListener('input', handleSearch);
        searchInput.addEventListener('keyup', handleSearch);
        searchInput.addEventListener('change', handleSearch);

        // Method 3: Inline attribute as backup
        searchInput.setAttribute('oninput', 'handleContactSearchInline(this)');

        // Test the setup immediately
        console.log('🧪 Testing search setup...');
        searchInput.value = 'test';
        handleSearch({ target: searchInput, type: 'test' });
        searchInput.value = '';

        // Focus the input
        setTimeout(() => {
            searchInput.focus();
            console.log('✅ Search input focused and ready');
        }, 100);

        searchSetupComplete = true;
        console.log('✅ Contact search setup complete with bulletproof approach');
    }

    attemptSetup();
}

// Global inline handler as backup
window.handleContactSearchInline = function(input) {
    console.log('🎯 Inline handler triggered:', input.value);
    const query = input.value.trim();

    if (window.searchTimeout) {
        clearTimeout(window.searchTimeout);
    }

    window.searchTimeout = setTimeout(() => {
        if (query.length >= 1) {
            console.log('🔍 Inline search executing:', query);
            searchContacts(query);
        } else {
            hideSearchResults();
        }
    }, 300);
};

// Legacy function - keeping for compatibility but not used
function handleSearchInput(e) {
    console.log('⚠️ Legacy handleSearchInput called - this should not happen');
}

function searchContacts(query) {
    console.log('🔍 === SEARCH CONTACTS DEBUG ===');
    console.log('🔍 Query:', query);
    console.log('🔍 Query type:', typeof query);
    console.log('🔍 Query length:', query.length);
    console.log('🔍 Available contacts:', allContacts.length);
    console.log('🔍 AllContacts type:', typeof allContacts);
    console.log('🔍 AllContacts is array:', Array.isArray(allContacts));

    const resultsDiv = document.getElementById('contact-results');
    if (!resultsDiv) {
        console.error('❌ Results div not found');
        return;
    }
    console.log('✅ Results div found');

    // Check if allContacts is properly populated
    if (!allContacts || allContacts.length === 0) {
        console.error('❌ No contacts available for search');
        console.error('❌ AllContacts value:', allContacts);
        resultsDiv.innerHTML = '<div class="p-3 text-red-500 dark:text-red-400 text-center">No contacts loaded. Please refresh and try again.</div>';
        resultsDiv.classList.remove('hidden');
        return;
    }

    console.log('✅ Contacts available for search');
    console.log('👤 First contact sample:', allContacts[0]);

    const normalizedQuery = query.toLowerCase();
    console.log('🔍 Normalized query:', normalizedQuery);

    // Search in both English and Arabic names
    const filteredContacts = allContacts.filter(contact => {
        console.log('🔍 Checking contact:', contact.name);

        const englishName = (contact.name || '').toLowerCase();
        const arabicName = (contact.arabic_name || '').toLowerCase();
        const position = (contact.position || '').toLowerCase();
        const email = (contact.email || '').toLowerCase();

        console.log('🔍 Fields to search:', {
            englishName,
            arabicName,
            position,
            email
        });

        const matches = englishName.includes(normalizedQuery) ||
               arabicName.includes(normalizedQuery) ||
               position.includes(normalizedQuery) ||
               email.includes(normalizedQuery);

        if (matches) {
            console.log('✅ Match found:', contact.name);
        }

        return matches;
    });

    console.log('🔍 Filtered contacts:', filteredContacts.length);
    console.log('🔍 Filtered contacts list:', filteredContacts.map(c => c.name));

    if (filteredContacts.length > 0) {
        let html = '';
        filteredContacts.slice(0, 10).forEach(contact => { // Limit to 10 results
            const displayName = contact.arabic_name && contact.arabic_name !== contact.name
                ? `${contact.name} (${contact.arabic_name})`
                : contact.name;

            html += `
                <div class="contact-result p-3 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer border-b border-gray-200 dark:border-gray-600 last:border-b-0"
                     data-contact-id="${contact.id}"
                     data-contact-name="${displayName}">
                    <div class="font-medium text-gray-900 dark:text-white">${displayName}</div>
                    ${contact.position ? `<div class="text-sm text-gray-600 dark:text-gray-400">${contact.position}</div>` : ''}
                    ${contact.email ? `<div class="text-xs text-gray-500 dark:text-gray-500">${contact.email}</div>` : ''}
                </div>
            `;
        });

        resultsDiv.innerHTML = html;
        resultsDiv.classList.remove('hidden');
        console.log('✅ Results displayed successfully');

        // Add click handlers to results
        resultsDiv.querySelectorAll('.contact-result').forEach(result => {
            result.addEventListener('click', function() {
                console.log('👤 Contact selected:', this.dataset.contactName);
                selectContact(this.dataset.contactId, this.dataset.contactName);
            });
        });
    } else {
        console.log('❌ No matches found for query:', query);
        resultsDiv.innerHTML = '<div class="p-3 text-gray-500 dark:text-gray-400 text-center">No contacts found for "' + query + '"</div>';
        resultsDiv.classList.remove('hidden');
        console.log('❌ No contacts found message displayed');
    }

    console.log('🔍 === END SEARCH DEBUG ===');
}

function selectContact(contactId, contactName) {
    document.getElementById('selected-contact-id').value = contactId;
    document.getElementById('selected-contact-name').textContent = contactName;
    document.getElementById('contact-search').value = '';
    document.getElementById('selected-contact-display').classList.remove('hidden');
    hideSearchResults();
}

function clearContactSelection() {
    document.getElementById('selected-contact-id').value = '';
    document.getElementById('selected-contact-name').textContent = '';
    document.getElementById('contact-search').value = '';
    document.getElementById('selected-contact-display').classList.add('hidden');
    hideSearchResults();
}

function hideSearchResults() {
    document.getElementById('contact-results').classList.add('hidden');
}

// Global test function for debugging
window.testContactSearch = function(query = 'ahmad') {
    console.log('🧪 Manual test search for:', query);
    console.log('📋 Available contacts:', allContacts.length);

    if (allContacts.length === 0) {
        console.log('⚠️ No contacts loaded, loading now...');
        loadContactsForSearch();
        setTimeout(() => {
            console.log('🔄 Retrying search after loading...');
            searchContacts(query);
        }, 2000);
    } else {
        searchContacts(query);
    }
};

// Global function to manually trigger search
window.manualSearch = function(query) {
    console.log('🔧 Manual search trigger:', query);
    const searchInput = document.getElementById('contact-search');
    if (searchInput) {
        searchInput.value = query;
        searchInput.dispatchEvent(new Event('input', { bubbles: true }));
    } else {
        console.error('❌ Search input not found');
    }
};

// Global function to test assignment
window.testAssignment = function(listId = 'test123', contactId = null) {
    console.log('🧪 Testing assignment...', { listId, contactId });

    // Use first contact if none specified
    if (!contactId && allContacts.length > 0) {
        contactId = allContacts[0].id;
        console.log('Using first contact:', allContacts[0].name, 'ID:', contactId);
    }

    if (!contactId) {
        console.error('❌ No contact ID provided and no contacts loaded');
        return;
    }

    // Test the assignment API
    fetch('/clickup/settings/assign-list', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            list_id: listId,
            contact_id: contactId
        })
    })
    .then(response => {
        console.log('📡 Assignment response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('📋 Assignment response:', data);
        if (data.success) {
            console.log('✅ Assignment successful!');
        } else {
            console.error('❌ Assignment failed:', data.message);
        }
    })
    .catch(error => {
        console.error('💥 Assignment error:', error);
    });
};

// Comprehensive debugging function
window.debugContactSearch = function() {
    console.log('🔍 === CONTACT SEARCH DEBUG REPORT ===');

    // 1. Check if contacts are loaded
    console.log('1. Contacts loaded:', allContacts.length);
    if (allContacts.length > 0) {
        console.log('   Sample contact:', allContacts[0]);
        console.log('   Contact fields:', Object.keys(allContacts[0]));
    }

    // 2. Check if search input exists
    const searchInput = document.getElementById('contact-search');
    console.log('2. Search input found:', !!searchInput);
    if (searchInput) {
        console.log('   Input value:', searchInput.value);
        console.log('   Input placeholder:', searchInput.placeholder);
        console.log('   Input disabled:', searchInput.disabled);
        console.log('   Input event listeners:', getEventListeners ? getEventListeners(searchInput) : 'Cannot check listeners');
    }

    // 3. Check if results container exists
    const resultsDiv = document.getElementById('contact-results');
    console.log('3. Results container found:', !!resultsDiv);
    if (resultsDiv) {
        console.log('   Results visible:', !resultsDiv.classList.contains('hidden'));
        console.log('   Results content:', resultsDiv.innerHTML.length > 0 ? 'Has content' : 'Empty');
        console.log('   Results HTML:', resultsDiv.innerHTML.substring(0, 200));
    }

    // 4. Test search setup
    console.log('4. Search setup complete:', searchSetupComplete);

    // 5. Test manual search
    console.log('5. Testing manual search...');
    if (allContacts.length > 0) {
        searchContacts('ahmad');
    }

    // 6. Test event handling
    console.log('6. Testing event handling...');
    if (searchInput) {
        console.log('   Simulating input event...');
        searchInput.value = 'test';
        const inputEvent = new Event('input', { bubbles: true });
        searchInput.dispatchEvent(inputEvent);

        setTimeout(() => {
            console.log('   Results after simulated input:', !resultsDiv.classList.contains('hidden'));
        }, 500);
    }

    console.log('=== END DEBUG REPORT ===');

    return {
        contactsLoaded: allContacts.length,
        searchInputExists: !!searchInput,
        resultsContainerExists: !!resultsDiv,
        searchSetupComplete: searchSetupComplete
    };
};

function unassignList(listId) {
    if (confirm('Are you sure you want to unassign this list?')) {
        // Make AJAX call to unassign list
        fetch(`<?php echo e(route('clickup.settings.unassign-list')); ?>`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                list_id: listId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Failed to unassign list: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error unassigning list');
        });
    }
}

function refreshLists() {
    location.reload();
}

// Toggle assignment details
function toggleAssignment(assignmentId) {
    const content = document.getElementById(assignmentId + '-content');
    const icon = document.getElementById(assignmentId + '-icon');

    if (!content || !icon) {
        console.error('Assignment elements not found:', assignmentId);
        return;
    }

    if (content.classList.contains('hidden')) {
        // Expand
        content.classList.remove('hidden');
        icon.classList.remove('fa-chevron-down');
        icon.classList.add('fa-chevron-up');
        icon.style.transform = 'rotate(180deg)';
        console.log('Expanded assignment:', assignmentId);
    } else {
        // Collapse
        content.classList.add('hidden');
        icon.classList.remove('fa-chevron-up');
        icon.classList.add('fa-chevron-down');
        icon.style.transform = 'rotate(0deg)';
        console.log('Collapsed assignment:', assignmentId);
    }
}

// Expand all assignments
function expandAllAssignments() {
    const allContents = document.querySelectorAll('[id$="-content"]');
    const allIcons = document.querySelectorAll('[id$="-icon"]');

    allContents.forEach(content => {
        content.classList.remove('hidden');
    });

    allIcons.forEach(icon => {
        icon.classList.remove('fa-chevron-down');
        icon.classList.add('fa-chevron-up');
        icon.style.transform = 'rotate(180deg)';
    });

    console.log('Expanded all assignments');
}

// Collapse all assignments
function collapseAllAssignments() {
    const allContents = document.querySelectorAll('[id$="-content"]');
    const allIcons = document.querySelectorAll('[id$="-icon"]');

    allContents.forEach(content => {
        content.classList.add('hidden');
    });

    allIcons.forEach(icon => {
        icon.classList.remove('fa-chevron-up');
        icon.classList.add('fa-chevron-down');
        icon.style.transform = 'rotate(0deg)';
    });

    console.log('Collapsed all assignments');
}

// Handle clear selection button
document.getElementById('clear-selection')?.addEventListener('click', function() {
    clearContactSelection();
});

// Test search functionality
document.getElementById('test-search')?.addEventListener('click', function() {
    console.log('🧪 Testing search functionality...');
    console.log('📋 All contacts loaded:', allContacts.length);

    const searchInput = document.getElementById('contact-search');
    if (searchInput) {
        console.log('✅ Search input found');

        // Test multiple ways
        console.log('🔄 Testing direct value assignment...');
        searchInput.value = 'ahmad';

        console.log('🔄 Testing input event...');
        searchInput.dispatchEvent(new Event('input', { bubbles: true }));

        console.log('🔄 Testing keyup event...');
        searchInput.dispatchEvent(new Event('keyup', { bubbles: true }));

        // Also test the global function
        setTimeout(() => {
            console.log('🔄 Testing global search function...');
            window.testContactSearch('noor');
        }, 1000);

    } else {
        console.error('❌ Search input not found');
    }

    // Test with sample data if no contacts loaded
    if (allContacts.length === 0) {
        console.log('⚠️ No contacts loaded, using sample data');
        allContacts = [
            { id: 1, name: 'John Doe', arabic_name: 'جون دو', position: 'Manager', email: '<EMAIL>' },
            { id: 2, name: 'Ahmed Ali', arabic_name: 'أحمد علي', position: 'Developer', email: '<EMAIL>' },
            { id: 3, name: 'Noor Oweis', arabic_name: 'نور اويس', position: 'Sales Team Leader', email: '<EMAIL>' }
        ];
        console.log('✅ Sample data loaded, testing search...');
        searchContacts('noor');
    }

    alert('Search test completed. Check console for detailed results.');
});

// Debug search functionality
document.getElementById('debug-search')?.addEventListener('click', function() {
    const result = window.debugContactSearch();

    let message = 'Debug Results:\n\n';
    message += `Contacts Loaded: ${result.contactsLoaded}\n`;
    message += `Search Input Exists: ${result.searchInputExists}\n`;
    message += `Results Container Exists: ${result.resultsContainerExists}\n`;
    message += `Search Setup Complete: ${result.searchSetupComplete}\n\n`;
    message += 'Check console for detailed debug information.';

    alert(message);
});

// Force search functionality
document.getElementById('force-search')?.addEventListener('click', function() {
    console.log('🚀 Force search triggered');

    // Force load contacts if not loaded
    if (allContacts.length === 0) {
        console.log('📡 Loading contacts first...');
        loadContactsForSearch();
        setTimeout(() => {
            console.log('🔍 Testing search with loaded contacts...');
            searchContacts('ahmad');
        }, 2000);
    } else {
        console.log('🔍 Testing search with existing contacts...');
        searchContacts('ahmad');
    }

    // Also test the input field
    const searchInput = document.getElementById('contact-search');
    if (searchInput) {
        console.log('⌨️ Setting input value and triggering events...');
        searchInput.value = 'ahmad';
        searchInput.focus();

        // Trigger multiple events
        searchInput.dispatchEvent(new Event('input', { bubbles: true }));
        searchInput.dispatchEvent(new Event('keyup', { bubbles: true }));
        searchInput.dispatchEvent(new Event('change', { bubbles: true }));
    }

    alert('Force search completed. Check console and search results.');
});

// Test full flow functionality
document.getElementById('test-full-flow')?.addEventListener('click', function() {
    console.log('🔄 Testing full assignment flow...');

    // Step 1: Check if contacts are loaded
    console.log('Step 1: Checking contacts...');
    if (allContacts.length === 0) {
        console.log('📡 Loading contacts first...');
        loadContactsForSearch();
        setTimeout(() => testFullFlowStep2(), 2000);
    } else {
        testFullFlowStep2();
    }
});

function testFullFlowStep2() {
    console.log('Step 2: Testing search...');

    // Test search functionality
    const searchInput = document.getElementById('contact-search');
    if (!searchInput) {
        console.error('❌ Search input not found');
        alert('Search input not found. Make sure modal is open.');
        return;
    }

    console.log('✅ Search input found');

    // Simulate typing
    searchInput.value = 'noor';
    searchInput.dispatchEvent(new Event('input', { bubbles: true }));

    setTimeout(() => {
        console.log('Step 3: Checking search results...');
        const resultsDiv = document.getElementById('contact-results');

        if (resultsDiv && !resultsDiv.classList.contains('hidden')) {
            console.log('✅ Search results visible');

            // Try to select first result
            const firstResult = resultsDiv.querySelector('.contact-result');
            if (firstResult) {
                console.log('✅ First result found, clicking...');
                firstResult.click();

                setTimeout(() => testFullFlowStep3(), 500);
            } else {
                console.error('❌ No search results found');
                alert('No search results found. Check if contacts contain "noor".');
            }
        } else {
            console.error('❌ Search results not visible');
            alert('Search results not visible. Check search functionality.');
        }
    }, 1000);
}

function testFullFlowStep3() {
    console.log('Step 4: Testing assignment...');

    // Check if contact is selected
    const selectedContactId = document.getElementById('selected-contact-id').value;
    if (!selectedContactId) {
        console.error('❌ No contact selected');
        alert('No contact selected. Selection failed.');
        return;
    }

    console.log('✅ Contact selected:', selectedContactId);

    // Test assignment (but don't actually submit)
    const form = document.getElementById('assignment-form');
    if (form) {
        console.log('✅ Assignment form found');
        console.log('Form data:', {
            list_id: document.getElementById('assignment-list-id').value,
            contact_id: selectedContactId
        });

        alert('Full flow test completed successfully! Check console for details. Form is ready for submission.');
    } else {
        console.error('❌ Assignment form not found');
        alert('Assignment form not found.');
    }
}

// Test contacts API
document.getElementById('test-contacts-api')?.addEventListener('click', function() {
    console.log('Testing contacts API...');

    fetch('/clickup/api/debug/contacts', {
        method: 'GET',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        }
    })
    .then(response => {
        console.log('API Response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('Contacts API Debug Response:', data);

        let message = 'Contacts API Test Results:\n\n';
        if (data.success) {
            const debug = data.debug;
            message += `Contact Count: ${debug.contact_count}\n`;
            message += `Table Exists: ${debug.table_exists}\n`;
            message += `Columns: ${debug.columns.join(', ')}\n\n`;

            if (debug.sample_contact) {
                message += 'Sample Contact:\n';
                message += `- Name: ${debug.sample_contact.name}\n`;
                message += `- Arabic Name: ${debug.sample_contact.arabic_name || 'None'}\n`;
                message += `- Email: ${debug.sample_contact.email || 'None'}\n`;
                message += `- Position: ${debug.sample_contact.position || 'None'}\n`;
            }
        } else {
            message += `Error: ${data.error}`;
        }

        alert(message);
    })
    .catch(error => {
        console.error('API test error:', error);
        alert('API test failed. Check console for details.');
    });
});

// Handle assignment form submission
document.getElementById('assignment-form')?.addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const listId = formData.get('list_id');
    const contactId = formData.get('contact_id');

    console.log('🚀 Assignment form submitted:', { listId, contactId });

    if (!contactId) {
        alert('Please search and select a contact first.');
        document.getElementById('contact-search').focus();
        return;
    }

    // Show loading state
    const submitButton = this.querySelector('button[type="submit"]');
    const originalText = submitButton.textContent;
    submitButton.textContent = 'Assigning...';
    submitButton.disabled = true;

    fetch('/clickup/settings/assign-list', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            list_id: listId,
            contact_id: contactId
        })
    })
    .then(response => {
        console.log('📡 Assignment response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('📋 Assignment response data:', data);

        // Reset button
        submitButton.textContent = originalText;
        submitButton.disabled = false;

        if (data.success) {
            let message = 'List assigned successfully!';
            if (data.team_member) {
                message += `\n\nContact added to team as: ${data.team_member.role}`;
                message += `\nTotal assigned lists: ${data.team_member.assigned_lists_count}`;
            }

            console.log('✅ Assignment successful, updating interface...');

            // Update the list card to show it's assigned
            const listCard = document.querySelector(`[data-list-id="${listId}"]`);
            if (listCard) {
                console.log('📋 Found list card, updating assignment status...');

                // Add assigned class and update visual indicators
                listCard.classList.add('list-assigned');
                listCard.setAttribute('data-assignment', 'assigned');

                // Update the assign button to show assigned status
                const assignButton = listCard.querySelector('.assign-button');
                if (assignButton) {
                    assignButton.innerHTML = '<i class="fas fa-check mr-1"></i>Assigned';
                    assignButton.classList.remove('bg-blue-600', 'hover:bg-blue-700');
                    assignButton.classList.add('bg-green-600', 'hover:bg-green-700');
                    assignButton.disabled = true;
                }

                // Add assignment indicator to the card
                const cardHeader = listCard.querySelector('.list-card-header');
                if (cardHeader && !cardHeader.querySelector('.assignment-badge')) {
                    const assignmentBadge = document.createElement('div');
                    assignmentBadge.className = 'assignment-badge inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
                    assignmentBadge.innerHTML = '<i class="fas fa-user-check mr-1"></i>Assigned';
                    cardHeader.appendChild(assignmentBadge);
                }

                console.log('✅ List card updated successfully');
            } else {
                console.log('⚠️ List card not found, will reload page');
            }

            alert(message);
            document.getElementById('assignment-modal').classList.add('hidden');

            // Reload the page to ensure all data is fresh
            setTimeout(() => {
                console.log('🔄 Reloading page to refresh assignment data...');
                location.reload();
            }, 1000);
        } else {
            alert('Failed to assign list: ' + data.message);
        }
    })
    .catch(error => {
        console.error('💥 Assignment error:', error);

        // Reset button
        submitButton.textContent = originalText;
        submitButton.disabled = false;

        alert('Error assigning list. Check console for details.');
    });
});

// Modal close functionality
document.getElementById('close-assignment-modal')?.addEventListener('click', function() {
    clearContactSelection();
    document.getElementById('assignment-modal').classList.add('hidden');
});

document.getElementById('cancel-assignment')?.addEventListener('click', function() {
    clearContactSelection();
    document.getElementById('assignment-modal').classList.add('hidden');
});

document.getElementById('close-list-details-modal')?.addEventListener('click', function() {
    document.getElementById('list-details-modal').classList.add('hidden');
});

function debugLists() {
    fetch('/clickup/api/debug/lists', {
        method: 'GET',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        console.log('Debug Lists Response:', data);

        let message = 'Debug Information:\n\n';
        if (data.success) {
            const debug = data.debug;
            message += `API Configured: ${debug.api_configured}\n`;
            message += `Selected Workspace: ${debug.selected_workspace || 'None'}\n`;
            message += `Selected Spaces: ${JSON.stringify(debug.selected_spaces)}\n`;
            message += `Available Spaces: ${debug.spaces ? debug.spaces.length : 0}\n`;
            message += `Retrieved Lists: ${debug.lists ? debug.lists.length : 0}\n\n`;

            if (debug.spaces && debug.spaces.length > 0) {
                message += 'Spaces:\n';
                debug.spaces.forEach(space => {
                    message += `- ${space.name} (ID: ${space.id})\n`;
                });
                message += '\n';
            }

            if (debug.lists && debug.lists.length > 0) {
                message += 'Lists:\n';
                debug.lists.slice(0, 5).forEach(list => {
                    message += `- ${list.name} (Space: ${list.space_name})\n`;
                });
                if (debug.lists.length > 5) {
                    message += `... and ${debug.lists.length - 5} more\n`;
                }
            }
        } else {
            message += `Error: ${data.error}`;
        }

        alert(message);
    })
    .catch(error => {
        console.error('Debug error:', error);
        alert('Debug request failed. Check console for details.');
    });
}
</script>
<?php /**PATH /Users/<USER>/Herd/business/plugins/clickup/Views/settings/tabs/lists.blade.php ENDPATH**/ ?>