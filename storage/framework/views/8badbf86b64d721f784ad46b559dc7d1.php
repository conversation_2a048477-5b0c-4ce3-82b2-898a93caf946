<?php $__env->startSection('title', 'Create Release - ' . $product->name); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Create New Release</h1>
                <p class="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1"><?php echo e($product->name); ?></p>
            </div>
            <div class="flex space-x-3">
                <a href="<?php echo e(route('products.releases.index', $product)); ?>" 
                   class="bg-gray-50 dark:bg-gray-7000 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Back to Releases
                </a>
                <a href="<?php echo e(route('products.show', $product)); ?>" 
                   class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-7000 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Back to Product
                </a>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
            <form method="POST" action="<?php echo e(route('products.releases.store', $product)); ?>" class="p-6">
                <?php echo csrf_field(); ?>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Basic Information -->
                    <div class="md:col-span-2">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Release Information</h3>
                    </div>

                    <!-- Version -->
                    <div>
                        <label for="version" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Version <span class="text-red-500 dark:text-red-400">*</span>
                        </label>
                        <input type="text" name="version" id="version" value="<?php echo e(old('version', $suggestedVersion)); ?>" required
                               placeholder="e.g., 1.0.0, 2.1.3"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 <?php $__errorArgs = ['version'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 dark:border-red-600 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?> dark:bg-gray-700 dark:text-white">
                        <?php $__errorArgs = ['version'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Use semantic versioning (e.g., 1.0.0)</p>
                    </div>

                    <!-- Release Type -->
                    <div>
                        <label for="release_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Release Type <span class="text-red-500 dark:text-red-400">*</span>
                        </label>
                        <select name="release_type" id="release_type" required onchange="suggestVersion()"
                                class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 <?php $__errorArgs = ['release_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 dark:border-red-600 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?> dark:bg-gray-700 dark:text-white">
                            <?php $__currentLoopData = $releaseTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $typeKey => $typeLabel): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($typeKey); ?>" <?php echo e(old('release_type', 'minor') === $typeKey ? 'selected' : ''); ?>>
                                    <?php echo e($typeLabel); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <?php $__errorArgs = ['release_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Choose the type of release</p>
                    </div>

                    <!-- Title -->
                    <div class="md:col-span-2">
                        <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Release Title <span class="text-red-500 dark:text-red-400">*</span>
                        </label>
                        <input type="text" name="title" id="title" value="<?php echo e(old('title')); ?>" required
                               placeholder="e.g., New Dashboard Features, Bug Fixes and Improvements"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 dark:border-red-600 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?> dark:bg-gray-700 dark:text-white">
                        <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Release Date -->
                    <div>
                        <label for="release_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Release Date <span class="text-red-500 dark:text-red-400">*</span>
                        </label>
                        <input type="date" name="release_date" id="release_date" value="<?php echo e(old('release_date', date('Y-m-d'))); ?>" required
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 <?php $__errorArgs = ['release_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 dark:border-red-600 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?> dark:bg-gray-700 dark:text-white">
                        <?php $__errorArgs = ['release_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Published Status -->
                    <div class="flex items-center">
                        <div class="flex items-center h-5">
                            <input type="checkbox" name="is_published" id="is_published" value="1" <?php echo e(old('is_published') ? 'checked' : ''); ?>

                                   class="focus:ring-blue-500 dark:focus:ring-blue-400 h-4 w-4 text-blue-600 dark:text-blue-400 border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:text-white">
                        </div>
                        <div class="ml-3 text-sm">
                            <label for="is_published" class="font-medium text-gray-700 dark:text-gray-300">Publish immediately</label>
                            <p class="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Make this release visible to users</p>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="md:col-span-2">
                        <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Description
                        </label>
                        <textarea name="description" id="description" rows="4"
                                  placeholder="Describe what's new in this release..."
                                  class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 dark:border-red-600 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?> dark:bg-gray-700 dark:text-white"><?php echo e(old('description')); ?></textarea>
                        <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Changelog -->
                    <div class="md:col-span-2">
                        <label for="changelog" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Detailed Changelog
                        </label>
                        <textarea name="changelog" id="changelog" rows="6"
                                  placeholder="Detailed technical changelog for developers..."
                                  class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 <?php $__errorArgs = ['changelog'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 dark:border-red-600 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?> dark:bg-gray-700 dark:text-white"><?php echo e(old('changelog')); ?></textarea>
                        <?php $__errorArgs = ['changelog'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Technical details for developers and advanced users</p>
                    </div>
                </div>

                <!-- Features Section -->
                <div class="mt-8">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">New Features</h3>
                    <div id="features-container">
                        <?php if(old('features')): ?>
                            <?php $__currentLoopData = old('features'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="feature-item flex items-center space-x-2 mb-2">
                                    <input type="text" name="features[]" value="<?php echo e($feature); ?>"
                                           placeholder="Describe a new feature..."
                                           class="flex-1 border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:bg-gray-700 dark:text-white">
                                    <button type="button" onclick="removeFeature(this)" 
                                            class="bg-red-500 dark:bg-red-600 hover:bg-red-700 dark:hover:bg-red-500 text-white px-3 py-2 rounded transition duration-150 ease-in-out">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <div class="feature-item flex items-center space-x-2 mb-2">
                                <input type="text" name="features[]" placeholder="Describe a new feature..."
                                       class="flex-1 border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:bg-gray-700 dark:text-white">
                                <button type="button" onclick="removeFeature(this)" 
                                        class="bg-red-500 dark:bg-red-600 hover:bg-red-700 dark:hover:bg-red-500 text-white px-3 py-2 rounded transition duration-150 ease-in-out">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>
                    <button type="button" onclick="addFeature()"
                            class="mt-2 bg-green-500 dark:bg-green-600 hover:bg-green-700 dark:hover:bg-green-500 text-white px-4 py-2 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-plus mr-2"></i>Add Feature
                    </button>
                </div>

                <!-- Bug Fixes Section -->
                <div class="mt-8">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Bug Fixes</h3>
                    <div id="bug-fixes-container">
                        <?php if(old('bug_fixes')): ?>
                            <?php $__currentLoopData = old('bug_fixes'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $bugFix): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="bug-fix-item flex items-center space-x-2 mb-2">
                                    <input type="text" name="bug_fixes[]" value="<?php echo e($bugFix); ?>"
                                           placeholder="Describe a bug fix..."
                                           class="flex-1 border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:bg-gray-700 dark:text-white">
                                    <button type="button" onclick="removeBugFix(this)"
                                            class="bg-red-500 dark:bg-red-600 hover:bg-red-700 dark:hover:bg-red-500 text-white px-3 py-2 rounded transition duration-150 ease-in-out">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <div class="bug-fix-item flex items-center space-x-2 mb-2">
                                <input type="text" name="bug_fixes[]" placeholder="Describe a bug fix..."
                                       class="flex-1 border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:bg-gray-700 dark:text-white">
                                <button type="button" onclick="removeBugFix(this)"
                                        class="bg-red-500 dark:bg-red-600 hover:bg-red-700 dark:hover:bg-red-500 text-white px-3 py-2 rounded transition duration-150 ease-in-out">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>
                    <button type="button" onclick="addBugFix()"
                            class="mt-2 bg-green-500 dark:bg-green-600 hover:bg-green-700 dark:hover:bg-green-500 text-white px-4 py-2 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-plus mr-2"></i>Add Bug Fix
                    </button>
                </div>

                <!-- Breaking Changes Section -->
                <div class="mt-8">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Breaking Changes</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mb-4">List any changes that might break existing functionality</p>
                    <div id="breaking-changes-container">
                        <?php if(old('breaking_changes')): ?>
                            <?php $__currentLoopData = old('breaking_changes'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $breakingChange): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="breaking-change-item flex items-center space-x-2 mb-2">
                                    <input type="text" name="breaking_changes[]" value="<?php echo e($breakingChange); ?>"
                                           placeholder="Describe a breaking change..."
                                           class="flex-1 border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:bg-gray-700 dark:text-white">
                                    <button type="button" onclick="removeBreakingChange(this)"
                                            class="bg-red-500 dark:bg-red-600 hover:bg-red-700 dark:hover:bg-red-500 text-white px-3 py-2 rounded transition duration-150 ease-in-out">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <div class="breaking-change-item flex items-center space-x-2 mb-2">
                                <input type="text" name="breaking_changes[]" placeholder="Describe a breaking change..."
                                       class="flex-1 border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:bg-gray-700 dark:text-white">
                                <button type="button" onclick="removeBreakingChange(this)"
                                        class="bg-red-500 dark:bg-red-600 hover:bg-red-700 dark:hover:bg-red-500 text-white px-3 py-2 rounded transition duration-150 ease-in-out">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>
                    <button type="button" onclick="addBreakingChange()"
                            class="mt-2 bg-green-500 dark:bg-green-600 hover:bg-green-700 dark:hover:bg-green-500 text-white px-4 py-2 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-plus mr-2"></i>Add Breaking Change
                    </button>
                </div>

                <!-- Form Actions -->
                <div class="mt-8 flex justify-end space-x-3">
                    <a href="<?php echo e(route('products.releases.index', $product)); ?>"
                       class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-7000 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Cancel
                    </a>
                    <button type="submit"
                            class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Create Release
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function addFeature() {
    const container = document.getElementById('features-container');
    const newItem = document.createElement('div');
    newItem.className = 'feature-item flex items-center space-x-2 mb-2';
    newItem.innerHTML = `
        <input type="text" name="features[]" placeholder="Describe a new feature..."
               class="flex-1 border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:bg-gray-700 dark:text-white">
        <button type="button" onclick="removeFeature(this)"
                class="bg-red-500 dark:bg-red-600 hover:bg-red-700 dark:hover:bg-red-500 text-white px-3 py-2 rounded transition duration-150 ease-in-out">
            <i class="fas fa-times"></i>
        </button>
    `;
    container.appendChild(newItem);
}

function removeFeature(button) {
    const container = document.getElementById('features-container');
    if (container.children.length > 1) {
        button.parentElement.remove();
    }
}

function addBugFix() {
    const container = document.getElementById('bug-fixes-container');
    const newItem = document.createElement('div');
    newItem.className = 'bug-fix-item flex items-center space-x-2 mb-2';
    newItem.innerHTML = `
        <input type="text" name="bug_fixes[]" placeholder="Describe a bug fix..."
               class="flex-1 border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:bg-gray-700 dark:text-white">
        <button type="button" onclick="removeBugFix(this)"
                class="bg-red-500 dark:bg-red-600 hover:bg-red-700 dark:hover:bg-red-500 text-white px-3 py-2 rounded transition duration-150 ease-in-out">
            <i class="fas fa-times"></i>
        </button>
    `;
    container.appendChild(newItem);
}

function removeBugFix(button) {
    const container = document.getElementById('bug-fixes-container');
    if (container.children.length > 1) {
        button.parentElement.remove();
    }
}

function addBreakingChange() {
    const container = document.getElementById('breaking-changes-container');
    const newItem = document.createElement('div');
    newItem.className = 'breaking-change-item flex items-center space-x-2 mb-2';
    newItem.innerHTML = `
        <input type="text" name="breaking_changes[]" placeholder="Describe a breaking change..."
               class="flex-1 border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:bg-gray-700 dark:text-white">
        <button type="button" onclick="removeBreakingChange(this)"
                class="bg-red-500 dark:bg-red-600 hover:bg-red-700 dark:hover:bg-red-500 text-white px-3 py-2 rounded transition duration-150 ease-in-out">
            <i class="fas fa-times"></i>
        </button>
    `;
    container.appendChild(newItem);
}

function removeBreakingChange(button) {
    const container = document.getElementById('breaking-changes-container');
    if (container.children.length > 1) {
        button.parentElement.remove();
    }
}

function suggestVersion() {
    // This function could make an AJAX call to suggest the next version
    // For now, it's just a placeholder
    console.log('Suggesting version based on release type...');
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/products/Views/releases/create.blade.php ENDPATH**/ ?>