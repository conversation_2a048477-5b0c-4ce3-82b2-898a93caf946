<?php $__env->startSection('title', $document->name); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="flex justify-between items-start mb-6">
            <div class="flex items-center">
                <i class="<?php echo e($document->file_icon); ?> text-4xl mr-4"></i>
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white"><?php echo e($document->name); ?></h1>
                    <p class="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1"><?php echo e($product->name); ?></p>
                    <div class="flex items-center mt-2 space-x-4">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 transition duration-150 ease-in-out">
                            <?php echo e($document->category_label); ?>

                        </span>
                        <span class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">v<?php echo e($document->version); ?></span>
                        <?php if($document->is_current_version): ?>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 transition duration-150 ease-in-out">
                                Current Version
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="flex space-x-3">
                <a href="<?php echo e(route('products.documents.index', $product)); ?>" 
                   class="bg-gray-50 dark:bg-gray-7000 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Back to Documents
                </a>
                <?php if(auth()->user()->hasPermission('view_product_documents')): ?>
                    <?php if($document->isViewableInBrowser()): ?>
                        <a href="<?php echo e(route('products.documents.view', [$product, $document])); ?>" 
                           target="_blank"
                           class="bg-green-500 dark:bg-green-600 hover:bg-green-700 dark:hover:bg-green-500 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                            <i class="fas fa-eye mr-2"></i>View
                        </a>
                    <?php endif; ?>
                    <a href="<?php echo e(route('products.documents.download', [$product, $document])); ?>" 
                       class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-download mr-2"></i>Download
                    </a>
                <?php endif; ?>
                <?php if(auth()->user()->hasPermission('manage_product_documents')): ?>
                    <a href="<?php echo e(route('products.documents.edit', [$product, $document])); ?>" 
                       class="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-edit mr-2"></i>Edit
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Document Information -->
                <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                    <div class="px-4 py-5 sm:px-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Document Information</h3>
                        <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Details about this document</p>
                    </div>
                    <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-0">
                        <dl class="sm:divide-y sm:divide-gray-200 dark:divide-gray-700">
                            <?php if($document->description): ?>
                                <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Description</dt>
                                    <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2"><?php echo e($document->description); ?></dd>
                                </div>
                            <?php endif; ?>
                            <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">File Name</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2"><?php echo e($document->original_name); ?></dd>
                            </div>
                            <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">File Size</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2"><?php echo e($document->formatted_file_size); ?></dd>
                            </div>
                            <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">File Type</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2"><?php echo e($document->mime_type); ?></dd>
                            </div>
                            <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Uploaded By</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                                    <?php echo e($document->uploader->name); ?> on <?php echo e($document->upload_date->format('M d, Y \a\t g:i A')); ?>

                                </dd>
                            </div>
                        </dl>
                    </div>
                </div>

                <!-- Version History -->
                <?php if($versions->count() > 1): ?>
                    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                        <div class="px-4 py-5 sm:px-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Version History</h3>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">All versions of this document</p>
                        </div>
                        <div class="border-t border-gray-200 dark:border-gray-700">
                            <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                                <?php $__currentLoopData = $versions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $version): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li class="px-4 py-4 sm:px-6 <?php echo e($version->is_current_version ? 'bg-blue-50' : ''); ?> transition duration-150 ease-in-out">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <i class="<?php echo e($version->file_icon); ?> mr-3"></i>
                                                <div>
                                                    <p class="text-sm font-medium text-gray-900 dark:text-white">
                                                        Version <?php echo e($version->version); ?>

                                                        <?php if($version->is_current_version): ?>
                                                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 transition duration-150 ease-in-out">
                                                                Current
                                                            </span>
                                                        <?php endif; ?>
                                                    </p>
                                                    <p class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                                                        <?php echo e($version->formatted_file_size); ?> • 
                                                        Uploaded <?php echo e($version->upload_date->format('M d, Y')); ?> by <?php echo e($version->uploader->name); ?>

                                                    </p>
                                                </div>
                                            </div>
                                            <div class="flex space-x-2">
                                                <?php if(auth()->user()->hasPermission('view_product_documents')): ?>
                                                    <a href="<?php echo e(route('products.documents.version-download', [$product, $document, $version->version])); ?>" 
                                                       class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 text-sm">
                                                        Download
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Quick Actions -->
                <?php if(auth()->user()->hasPermission('manage_product_documents')): ?>
                    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                        <div class="px-4 py-5 sm:px-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Quick Actions</h3>
                        </div>
                        <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                            <div class="space-y-4">
                                <form method="POST" action="<?php echo e(route('products.documents.new-version', [$product, $document])); ?>" 
                                      enctype="multipart/form-data" id="new-version-form">
                                    <?php echo csrf_field(); ?>
                                    <div class="space-y-3">
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Upload New Version</label>
                                        <input type="file" name="file" id="new-version-file" 
                                               class="block w-full text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 dark:bg-blue-900 transition duration-150 ease-in-out"
                                               accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.jpeg,.png,.gif,.zip,.rar">
                                        <textarea name="description" placeholder="Version notes (optional)" rows="2"
                                                  class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 text-sm dark:bg-gray-700 dark:text-white"></textarea>
                                        <button type="submit" 
                                                class="w-full bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded text-sm transition duration-150 ease-in-out">
                                            Upload New Version
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Document Stats -->
                <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                    <div class="px-4 py-5 sm:px-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Document Stats</h3>
                    </div>
                    <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                        <dl class="space-y-4">
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Total Versions</dt>
                                <dd class="text-lg font-semibold text-gray-900 dark:text-white"><?php echo e($versions->count()); ?></dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">First Upload</dt>
                                <dd class="text-sm text-gray-900 dark:text-white"><?php echo e($versions->sortBy('upload_date')->first()->upload_date->format('M d, Y')); ?></dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Last Updated</dt>
                                <dd class="text-sm text-gray-900 dark:text-white"><?php echo e($document->updated_at->format('M d, Y')); ?></dd>
                            </div>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/products/Views/documents/show.blade.php ENDPATH**/ ?>