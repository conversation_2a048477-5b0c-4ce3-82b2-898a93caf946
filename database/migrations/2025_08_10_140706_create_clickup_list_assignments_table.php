<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clickup_list_assignments', function (Blueprint $table) {
            $table->id();
            $table->string('clickup_list_id')->index(); // ClickUp list ID
            $table->string('clickup_list_name'); // ClickUp list name for display
            $table->string('clickup_space_id')->nullable(); // ClickUp space ID
            $table->string('clickup_space_name')->nullable(); // ClickUp space name for display
            $table->string('clickup_workspace_id')->nullable(); // ClickUp workspace ID
            $table->unsignedBigInteger('assigned_to_contact_id')->nullable(); // Contact ID from contacts system
            $table->string('assigned_to_contact_name')->nullable(); // Contact name for display
            $table->unsignedBigInteger('assigned_by_user_id'); // User who made the assignment
            $table->timestamp('assigned_at')->useCurrent(); // When the assignment was made
            $table->json('metadata')->nullable(); // Additional metadata about the list
            $table->boolean('is_active')->default(true); // Whether the assignment is active
            $table->timestamps();

            // Indexes for performance
            $table->unique('clickup_list_id'); // Each list can only be assigned once
            $table->index(['assigned_to_contact_id', 'is_active']);
            $table->index(['clickup_space_id', 'is_active']);
            $table->index(['assigned_by_user_id']);

            // Foreign key constraints
            $table->foreign('assigned_to_contact_id')->references('id')->on('business_contacts')->onDelete('set null');
            $table->foreign('assigned_by_user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clickup_list_assignments');
    }
};
